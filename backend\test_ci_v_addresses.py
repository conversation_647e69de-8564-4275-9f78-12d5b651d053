#!/usr/bin/env python3
"""
Test des différentes adresses CI-V pour IC-R8600
Teste 0x94, 0x96, 0x98 et autres adresses possibles
"""

import socket
import time

def test_ci_v_address(address, port=50002):
    """Test d'une adresse CI-V spécifique"""
    print(f"\n📡 Test adresse CI-V: 0x{address:02X} sur port {port}")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(3.0)
        
        # Commande Power OFF avec l'adresse testée
        power_off = bytes([0xFE, 0xFE, address, 0xE0, 0x18, 0x00, 0xFD])
        print(f"Envoi Power OFF: {power_off.hex().upper()}")
        sock.sendto(power_off, ('**************', port))
        
        print("👀 OBSERVEZ L'IC-R8600 - S'éteint-il ? (3 secondes)")
        time.sleep(3)
        
        # Commande Power ON
        power_on = bytes([0xFE, 0xFE, address, 0xE0, 0x18, 0x01, 0xFD])
        print(f"Envoi Power ON: {power_on.hex().upper()}")
        sock.sendto(power_on, ('**************', port))
        
        print("👀 OBSERVEZ L'IC-R8600 - Se rallume-t-il ? (3 secondes)")
        time.sleep(3)
        
        # Test fréquence pour confirmation
        # 145.500 MHz = 145500000 Hz en BCD
        freq_cmd = bytes([0xFE, 0xFE, address, 0xE0, 0x05, 0x00, 0x00, 0x50, 0x45, 0x01, 0xFD])
        print(f"Envoi fréquence 145.500 MHz: {freq_cmd.hex().upper()}")
        sock.sendto(freq_cmd, ('**************', port))
        
        print("👀 OBSERVEZ L'AFFICHAGE - Fréquence change-t-elle ? (2 secondes)")
        time.sleep(2)
        
        sock.close()
        
        # Demander confirmation à l'utilisateur
        response = input(f"L'adresse 0x{address:02X} a-t-elle eu un effet ? (o/n): ").lower()
        return response in ['o', 'oui', 'y', 'yes']
        
    except Exception as e:
        print(f"❌ Erreur avec adresse 0x{address:02X}: {e}")
        return False

def test_different_ports(address=0x94):
    """Test de différents ports avec une adresse"""
    print(f"\n🔍 Test de différents ports avec adresse 0x{address:02X}")
    
    ports = [50001, 50002, 50003, 50004]
    
    for port in ports:
        print(f"\n--- Test port {port} ---")
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(2.0)
            
            # Commande simple de test
            test_cmd = bytes([0xFE, 0xFE, address, 0xE0, 0x19, 0x00, 0xFD])  # Status
            sock.sendto(test_cmd, ('**************', port))
            
            try:
                response, addr = sock.recvfrom(1024)
                print(f"✅ Port {port}: Réponse reçue - {response.hex().upper()}")
                return port  # Retourner le port qui répond
            except socket.timeout:
                print(f"⚠️  Port {port}: Pas de réponse")
            
            sock.close()
            
        except Exception as e:
            print(f"❌ Port {port}: Erreur - {e}")
    
    return None

def main():
    """Test principal des adresses CI-V"""
    print("🎯 TEST DES ADRESSES CI-V IC-R8600")
    print("=" * 60)
    print("Nous allons tester différentes adresses CI-V")
    print("OBSERVEZ ATTENTIVEMENT votre IC-R8600 pendant les tests")
    print("=" * 60)
    
    # Adresses CI-V courantes pour IC-R8600
    addresses_to_test = [
        0x94,  # Adresse standard IC-R8600
        0x96,  # Adresse alternative
        0x98,  # Autre adresse possible
        0x9A,  # Adresse étendue
        0x88,  # Adresse générique ICOM
        0x76,  # Adresse IC-R8500 (similaire)
    ]
    
    print("Étape 1: Test des différents ports")
    working_port = test_different_ports()
    if working_port:
        print(f"✅ Port {working_port} semble répondre")
        test_port = working_port
    else:
        print("⚠️  Aucun port ne répond, utilisation du port 50002")
        test_port = 50002
    
    print(f"\nÉtape 2: Test des adresses CI-V sur port {test_port}")
    working_addresses = []
    
    for addr in addresses_to_test:
        print(f"\n{'='*40}")
        print(f"TEST ADRESSE CI-V: 0x{addr:02X}")
        print(f"{'='*40}")
        
        if test_ci_v_address(addr, test_port):
            working_addresses.append(addr)
            print(f"✅ Adresse 0x{addr:02X} FONCTIONNE !")
            
            # Demander si on continue ou si on s'arrête
            continue_test = input("Continuer les tests ? (o/n): ").lower()
            if continue_test not in ['o', 'oui', 'y', 'yes']:
                break
        else:
            print(f"❌ Adresse 0x{addr:02X} sans effet")
    
    # Résultats
    print(f"\n{'='*60}")
    print("📊 RÉSULTATS")
    print(f"{'='*60}")
    
    if working_addresses:
        print("✅ Adresses CI-V fonctionnelles:")
        for addr in working_addresses:
            print(f"   - 0x{addr:02X}")
        print(f"\n🎯 Utilisez l'adresse 0x{working_addresses[0]:02X} dans l'application")
    else:
        print("❌ Aucune adresse CI-V ne fonctionne")
        print("\n🔍 Vérifications supplémentaires nécessaires:")
        print("1. MENU → SET → Network → CI-V Address")
        print("2. MENU → SET → Network → Network Control: ON")
        print("3. Bouton LOCAL éteint")
        print("4. Redémarrage IC-R8600")
        print("5. Vérifier firmware et compatibilité")

if __name__ == "__main__":
    main()
