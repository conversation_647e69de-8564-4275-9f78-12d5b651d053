#!/usr/bin/env python3
"""
Test des commandes interface pour debug
"""

import requests
import time

BASE_URL = "http://localhost:8001"

def test_frequency_change():
    """Test changement de fréquence via interface"""
    print("🔍 TEST CHANGEMENT FRÉQUENCE")
    print("=" * 40)
    
    # Test fréquence 88.5 MHz
    data = {"frequency": 88.5}
    response = requests.post(f"{BASE_URL}/api/command", json=data)
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Succès: {result}")
        print("⚠️  Vérifiez l'affichage IC-R8600: 88.500 MHz ?")
    else:
        print(f"❌ Erreur: {response.text}")

def test_mode_change():
    """Test changement de mode"""
    print("\n🔍 TEST CHANGEMENT MODE")
    print("=" * 40)
    
    # Test mode AM
    data = {"mode": "AM"}
    response = requests.post(f"{BASE_URL}/api/command", json=data)
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Succès: {result}")
        print("⚠️  Vérifiez l'affichage IC-R8600: Mode AM ?")
    else:
        print(f"❌ Erreur: {response.text}")

def test_status():
    """Test lecture statut"""
    print("\n🔍 TEST STATUT")
    print("=" * 40)
    
    response = requests.get(f"{BASE_URL}/api/status")
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Statut: {result}")
    else:
        print(f"❌ Erreur: {response.text}")

if __name__ == "__main__":
    print("🎯 TEST INTERFACE IC-R8600")
    print("=" * 50)
    
    test_status()
    test_frequency_change()
    test_mode_change()
    
    print("\n🎉 Tests terminés !")
