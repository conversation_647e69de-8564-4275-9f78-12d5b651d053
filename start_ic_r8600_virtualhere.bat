@echo off
REM ========================================
REM Démarrage IC-R8600 avec VirtualHere
REM Solution USB distante complète
REM ========================================

echo ========================================
echo IC-R8600 VIRTUALHERE - DÉMARRAGE
echo ========================================
echo.
echo Configuration:
echo - PC distant: ************ (IC-R8600 + VirtualHere Server)
echo - PC supervision: 192.168.37.10 (VirtualHere Client + FastAPI)
echo - Port COM: COM6 (CP2102 USB to UART Bridge)
echo.

REM Vérifier VirtualHere Client
echo [1/4] Vérification VirtualHere Client...
tasklist /fi "imagename eq vhui64.exe" | find "vhui64.exe" >nul
if %errorlevel%==0 (
    echo ✅ VirtualHere Client: EN COURS
) else (
    echo ⚠️  VirtualHere Client: NON LANCÉ
    echo Lancement automatique...
    start "" "C:\VirtualHere-Client\vhui64.exe"
    timeout /t 3
)

REM Vérifier port COM6
echo.
echo [2/4] Vérification port COM6...
powershell "Get-WmiObject -Class Win32_SerialPort | Where-Object { $_.DeviceID -eq 'COM6' } | Select-Object DeviceID, Description" | find "COM6" >nul
if %errorlevel%==0 (
    echo ✅ Port COM6: DÉTECTÉ
) else (
    echo ❌ Port COM6: NON DÉTECTÉ
    echo.
    echo Actions requises:
    echo 1. Ouvrez VirtualHere Client
    echo 2. Connectez-vous au serveur ************
    echo 3. Clic droit sur IC-R8600 ^> 'Use this device'
    echo.
    pause
)

REM Installer dépendances Python si nécessaire
echo.
echo [3/4] Vérification dépendances Python...
pip show fastapi >nul 2>&1
if %errorlevel%==0 (
    echo ✅ FastAPI: INSTALLÉ
) else (
    echo ⚠️  Installation FastAPI...
    pip install fastapi uvicorn pyserial python-multipart
)

REM Démarrer l'application
echo.
echo [4/4] Démarrage application IC-R8600...
echo.
echo ========================================
echo APPLICATION DÉMARRÉE
echo ========================================
echo.
echo Interface Web: http://localhost:8000
echo API Documentation: http://localhost:8000/docs
echo.
echo Contrôles disponibles:
echo - Fréquence, Mode, RF Gain
echo - Enregistrement audio
echo - Monitoring en temps réel
echo.
echo Appuyez sur Ctrl+C pour arrêter
echo ========================================
echo.

cd /d "%~dp0backend"
python main.py

echo.
echo ========================================
echo APPLICATION ARRÊTÉE
echo ========================================
pause
