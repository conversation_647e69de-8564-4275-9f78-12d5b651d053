#!/usr/bin/env python3
"""
Serveur IC-R8600 - Connexion USB directe
Version simplifiée pour développement des fonctionnalités
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
import logging
from ic_r8600_simple import ICR8600Simple

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('backend.log')
    ]
)
logger = logging.getLogger(__name__)

app = FastAPI(title="IC-R8600 Controller - USB Direct")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Models
class CommandRequest(BaseModel):
    frequency: float = None
    mode: str = None
    power: bool = None
    rf_gain: int = None

class ScanRequest(BaseModel):
    start_frequency: int
    end_frequency: int
    step: int = 25000

class CommandResponse(BaseModel):
    success: bool
    message: str = ""
    simulation_mode: bool = False

# Variables globales
ic_r8600 = None
connection_status = {
    "connected": False,
    "port": None,
    "frequency": 145500000,
    "mode": "FM",
    "power_on": True,
    "volume": 128,
    "squelch": 50,
    "rf_gain": 128
}

@app.on_event("startup")
async def startup_event():
    """Initialisation au démarrage"""
    global ic_r8600, connection_status
    
    logger.info("🚀 Démarrage contrôleur IC-R8600 USB Direct")
    
    # Connexion USB directe
    try:
        ic_r8600 = ICR8600Simple()
        if ic_r8600.connect():
            logger.info("✅ IC-R8600 connecté en USB direct")
            connection_status.update({
                "connected": True,
                "port": ic_r8600.port
            })
        else:
            logger.error("❌ Échec connexion IC-R8600")
            connection_status["connected"] = False
    except Exception as e:
        logger.error(f"Erreur connexion: {e}")
        connection_status["connected"] = False
    
    logger.info("✅ Serveur API prêt sur http://localhost:8000")

@app.get("/api/status")
async def get_status():
    """Statut du récepteur"""
    if ic_r8600 and ic_r8600.connected:
        status = ic_r8600.get_status()
        status.update(connection_status)
        return status
    return connection_status

@app.post("/api/command")
async def send_command(command: CommandRequest):
    """Envoi de commande au récepteur"""
    global ic_r8600, connection_status

    # Debug de la commande reçue
    logger.info(f"🔍 Commande reçue: frequency={command.frequency}, mode={command.mode}, power={command.power}, rf_gain={command.rf_gain}")

    if not connection_status["connected"]:
        return CommandResponse(
            success=False,
            message="IC-R8600 non connecté"
        )

    results = []
    success = True

    try:
        if command.frequency:
            # Conversion MHz vers Hz
            freq_hz = int(command.frequency * 1000000)
            if ic_r8600.set_frequency(freq_hz):
                connection_status["frequency"] = freq_hz
                results.append(f"Fréquence: {command.frequency} MHz")
            else:
                results.append(f"❌ Échec fréquence: {command.frequency} MHz")
                success = False
        
        if command.mode:
            if ic_r8600.set_mode(command.mode):
                connection_status["mode"] = command.mode
                results.append(f"Mode: {command.mode}")
            else:
                results.append(f"❌ Échec mode: {command.mode}")
                success = False
        
        if command.power is not None:
            if ic_r8600.set_power(command.power):
                connection_status["power_on"] = command.power
                state = "ON" if command.power else "OFF"
                results.append(f"Alimentation: {state}")
            else:
                state = "ON" if command.power else "OFF"
                results.append(f"❌ Échec alimentation: {state}")
                success = False
        
        if command.rf_gain is not None:
            connection_status["rf_gain"] = command.rf_gain
            results.append(f"RF Gain: {command.rf_gain}")
        
        if results:
            return CommandResponse(
                success=success,
                message=" | ".join(results)
            )
        
        return CommandResponse(
            success=False,
            message="Aucune commande valide"
        )
        
    except Exception as e:
        logger.error(f"Erreur commande: {e}")
        return CommandResponse(
            success=False,
            message=f"Erreur: {str(e)}"
        )

@app.post("/api/scan/start")
async def start_scan(scan_request: ScanRequest):
    """Démarre un scan de fréquences"""
    try:
        if not connection_status["connected"]:
            return CommandResponse(
                success=False,
                message="IC-R8600 non connecté"
            )
        
        # Validation des fréquences
        if scan_request.start_frequency >= scan_request.end_frequency:
            raise HTTPException(status_code=400, detail="Fréquence de début doit être inférieure à la fréquence de fin")
        
        logger.info(f"Démarrage scan: {scan_request.start_frequency} -> {scan_request.end_frequency}")
        
        # TODO: Implémenter le vrai scan
        return CommandResponse(
            success=True,
            message=f"Scan démarré: {scan_request.start_frequency/1000000:.3f} - {scan_request.end_frequency/1000000:.3f} MHz"
        )
        
    except Exception as e:
        logger.error(f"Erreur scan: {e}")
        return CommandResponse(
            success=False,
            message=f"Erreur scan: {str(e)}"
        )

@app.post("/api/scan/stop")
async def stop_scan():
    """Arrête le scan en cours"""
    return CommandResponse(
        success=True,
        message="Scan arrêté"
    )

@app.get("/api/recordings")
async def get_recordings():
    """Liste des enregistrements"""
    return {
        "recordings": [
            {
                "filename": "ic_r8600_direct_test.wav",
                "date": "2025-07-17 15:35:00",
                "duration": "00:02:30",
                "frequency": "145.500 MHz",
                "mode": "FM"
            }
        ]
    }

@app.post("/api/audio/start")
async def start_recording():
    """Démarrage enregistrement"""
    return CommandResponse(
        success=True,
        message="Enregistrement démarré (USB direct)"
    )

@app.post("/api/audio/stop")
async def stop_recording():
    """Arrêt enregistrement"""
    return CommandResponse(
        success=True,
        message="Enregistrement arrêté (USB direct)"
    )

@app.post("/api/reconnect")
async def reconnect():
    """Reconnexion manuelle"""
    global ic_r8600, connection_status
    
    try:
        if ic_r8600:
            ic_r8600.disconnect()
        
        ic_r8600 = ICR8600Simple()
        if ic_r8600.connect():
            connection_status.update({
                "connected": True,
                "port": ic_r8600.port
            })
            return CommandResponse(
                success=True,
                message=f"Reconnecté sur {ic_r8600.port}"
            )
        else:
            connection_status["connected"] = False
            return CommandResponse(
                success=False,
                message="Échec reconnexion"
            )
    except Exception as e:
        logger.error(f"Erreur reconnexion: {e}")
        return CommandResponse(
            success=False,
            message=f"Erreur: {str(e)}"
        )

if __name__ == "__main__":
    print("🚀 Serveur IC-R8600 USB Direct")
    print("📡 http://localhost:8000")
    print("🔌 Connexion USB directe")
    uvicorn.run(app, host="0.0.0.0", port=8000)
