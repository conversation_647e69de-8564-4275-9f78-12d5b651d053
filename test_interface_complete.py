#!/usr/bin/env python3
"""
Test complet de l'interface comme elle l'utilise vraiment
"""
import requests
import json

API_BASE = "http://localhost:8001"

def test_command_like_interface():
    """Test d'une commande comme l'interface l'envoie"""
    print("🔍 TEST COMMANDE COMPLÈTE (comme l'interface)")
    print("=" * 50)
    
    # Commande exacte comme l'interface l'envoie
    command_data = {
        "frequency": 88.5,  # MHz
        "mode": "FM",
        "rf_gain": 128
    }
    
    try:
        response = requests.post(f"{API_BASE}/api/command", json=command_data)
        print(f"Status: {response.status_code}")
        result = response.json()
        print(f"✅ Résultat: {result}")
        
        if result.get('success'):
            print("🎉 SUCCÈS ! Plus de message 'Aucune commande valide'")
        else:
            print("❌ Échec:", result.get('message'))
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_recordings():
    """Test de l'endpoint recordings"""
    print("\n🔍 TEST ENREGISTREMENTS")
    print("=" * 50)
    
    try:
        response = requests.get(f"{API_BASE}/api/recordings")
        print(f"Status: {response.status_code}")
        result = response.json()
        print(f"✅ Enregistrements: {len(result['recordings'])} fichiers")
        for rec in result['recordings']:
            print(f"   📁 {rec['filename']} - {rec['frequency']} - {rec['duration']}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_mode_change():
    """Test changement de mode seul"""
    print("\n🔍 TEST CHANGEMENT MODE SEUL")
    print("=" * 50)
    
    try:
        response = requests.post(f"{API_BASE}/api/command", json={"mode": "AM"})
        result = response.json()
        print(f"✅ Mode AM: {result}")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    print("🎯 TEST INTERFACE COMPLÈTE")
    print("=" * 50)
    
    test_command_like_interface()
    test_recordings()
    test_mode_change()
    
    print("\n🎉 Tests terminés !")
