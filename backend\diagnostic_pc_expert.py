#!/usr/bin/env python3
"""
Diagnostic Expert PC - Télécommunications & Radioélectricité
Vérification complète de la stack réseau Windows pour IC-R8600
"""

import socket
import subprocess
import psutil
import time
import threading
import struct
import sys
import os

class DiagnosticExpertPC:
    """Diagnostic expert niveau télécommunications"""
    
    def __init__(self):
        self.target_ip = "**************"
        self.test_ports = [23, 50001, 50002, 50003, 50004]
        self.results = {}
    
    def test_1_interfaces_reseau(self):
        """Test 1: Interfaces réseau et configuration IP"""
        print("🔍 TEST 1: INTERFACES RÉSEAU")
        print("-" * 50)
        
        try:
            # Interfaces réseau actives
            interfaces = psutil.net_if_addrs()
            stats = psutil.net_if_stats()
            
            for interface, addrs in interfaces.items():
                if interface in stats and stats[interface].isup:
                    print(f"✅ Interface: {interface}")
                    for addr in addrs:
                        if addr.family == socket.AF_INET:
                            print(f"   IP: {addr.address}")
                            print(f"   Masque: {addr.netmask}")
                            
                            # Vérifier si dans le même réseau que l'IC-R8600
                            if self._same_network(addr.address, addr.netmask, self.target_ip):
                                print(f"   ✅ Même réseau que {self.target_ip}")
                                self.results['interface_ok'] = True
                            else:
                                print(f"   ⚠️  Réseau différent de {self.target_ip}")
            
            return True
        except Exception as e:
            print(f"❌ Erreur interfaces: {e}")
            return False
    
    def _same_network(self, ip1, netmask, ip2):
        """Vérifier si deux IPs sont dans le même réseau"""
        try:
            ip1_int = struct.unpack("!I", socket.inet_aton(ip1))[0]
            ip2_int = struct.unpack("!I", socket.inet_aton(ip2))[0]
            mask_int = struct.unpack("!I", socket.inet_aton(netmask))[0]
            
            return (ip1_int & mask_int) == (ip2_int & mask_int)
        except:
            return False
    
    def test_2_table_routage(self):
        """Test 2: Table de routage"""
        print("\n🔍 TEST 2: TABLE DE ROUTAGE")
        print("-" * 50)
        
        try:
            result = subprocess.run(['route', 'print'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            
            route_found = False
            for line in lines:
                if '192.168.37' in line or '0.0.0.0' in line:
                    print(f"✅ Route: {line.strip()}")
                    route_found = True
            
            if route_found:
                self.results['routing_ok'] = True
            else:
                print("⚠️  Aucune route vers 192.168.37.x trouvée")
            
            return True
        except Exception as e:
            print(f"❌ Erreur routage: {e}")
            return False
    
    def test_3_arp_table(self):
        """Test 3: Table ARP"""
        print("\n🔍 TEST 3: TABLE ARP")
        print("-" * 50)
        
        try:
            result = subprocess.run(['arp', '-a'], capture_output=True, text=True)
            
            if self.target_ip in result.stdout:
                print(f"✅ {self.target_ip} présent dans table ARP")
                # Extraire l'adresse MAC
                for line in result.stdout.split('\n'):
                    if self.target_ip in line:
                        print(f"   {line.strip()}")
                        self.results['arp_ok'] = True
            else:
                print(f"⚠️  {self.target_ip} absent de la table ARP")
                print("   Tentative de ping pour forcer l'entrée ARP...")
                subprocess.run(['ping', '-n', '1', self.target_ip], capture_output=True)
                
                # Revérifier
                result2 = subprocess.run(['arp', '-a'], capture_output=True, text=True)
                if self.target_ip in result2.stdout:
                    print(f"✅ {self.target_ip} ajouté à la table ARP")
                    self.results['arp_ok'] = True
            
            return True
        except Exception as e:
            print(f"❌ Erreur ARP: {e}")
            return False
    
    def test_4_pare_feu_windows(self):
        """Test 4: Pare-feu Windows"""
        print("\n🔍 TEST 4: PARE-FEU WINDOWS")
        print("-" * 50)
        
        try:
            # État du pare-feu
            result = subprocess.run(['netsh', 'advfirewall', 'show', 'allprofiles', 'state'], 
                                  capture_output=True, text=True)
            
            if 'ON' in result.stdout:
                print("⚠️  Pare-feu Windows activé")
                
                # Vérifier les règles pour nos ports
                for port in self.test_ports:
                    rule_result = subprocess.run([
                        'netsh', 'advfirewall', 'firewall', 'show', 'rule', 
                        f'name=all', 'dir=out', f'localport={port}'
                    ], capture_output=True, text=True)
                    
                    if str(port) in rule_result.stdout:
                        print(f"✅ Règle sortante trouvée pour port {port}")
                    else:
                        print(f"⚠️  Aucune règle sortante pour port {port}")
            else:
                print("✅ Pare-feu Windows désactivé")
                self.results['firewall_ok'] = True
            
            return True
        except Exception as e:
            print(f"❌ Erreur pare-feu: {e}")
            return False
    
    def test_5_sockets_udp_disponibles(self):
        """Test 5: Sockets UDP disponibles"""
        print("\n🔍 TEST 5: SOCKETS UDP DISPONIBLES")
        print("-" * 50)
        
        try:
            # Tester la création de sockets UDP sur différents ports locaux
            test_local_ports = [0, 12345, 54321]  # 0 = port automatique
            
            for local_port in test_local_ports:
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                    sock.bind(('', local_port))
                    actual_port = sock.getsockname()[1]
                    print(f"✅ Socket UDP créé sur port local {actual_port}")
                    sock.close()
                    self.results['udp_sockets_ok'] = True
                except Exception as e:
                    print(f"❌ Erreur socket UDP port {local_port}: {e}")
            
            return True
        except Exception as e:
            print(f"❌ Erreur test sockets: {e}")
            return False
    
    def test_6_connectivite_icmp(self):
        """Test 6: Connectivité ICMP (ping)"""
        print("\n🔍 TEST 6: CONNECTIVITÉ ICMP")
        print("-" * 50)
        
        try:
            # Ping avec statistiques détaillées
            result = subprocess.run([
                'ping', '-n', '4', '-l', '32', self.target_ip
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Ping vers {self.target_ip} réussi")
                
                # Analyser les statistiques
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'Paquets' in line or 'temps' in line or 'TTL' in line:
                        print(f"   {line.strip()}")
                
                self.results['ping_ok'] = True
            else:
                print(f"❌ Ping vers {self.target_ip} échoué")
                print(result.stdout)
            
            return True
        except Exception as e:
            print(f"❌ Erreur ping: {e}")
            return False
    
    def test_7_ports_tcp_udp_cible(self):
        """Test 7: Ports TCP/UDP sur la cible"""
        print("\n🔍 TEST 7: PORTS TCP/UDP SUR CIBLE")
        print("-" * 50)
        
        # Test TCP
        print("Tests TCP:")
        for port in [23, 50001]:  # Ports TCP possibles
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(3.0)
                result = sock.connect_ex((self.target_ip, port))
                
                if result == 0:
                    print(f"✅ Port TCP {port}: OUVERT")
                    self.results[f'tcp_{port}_ok'] = True
                else:
                    print(f"❌ Port TCP {port}: FERMÉ")
                
                sock.close()
            except Exception as e:
                print(f"❌ Port TCP {port}: Erreur - {e}")
        
        # Test UDP avec timeout court
        print("\nTests UDP:")
        for port in self.test_ports:
            if port != 23:  # Skip Telnet pour UDP
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                    sock.settimeout(2.0)
                    
                    # Envoyer paquet de test
                    test_packet = b'\xFE\xFE\x94\xE0\x19\x00\xFD'
                    sock.sendto(test_packet, (self.target_ip, port))
                    
                    try:
                        response, addr = sock.recvfrom(1024)
                        print(f"✅ Port UDP {port}: RÉPONSE reçue")
                        self.results[f'udp_{port}_response'] = True
                    except socket.timeout:
                        print(f"⚠️  Port UDP {port}: Pas de réponse (normal)")
                        self.results[f'udp_{port}_open'] = True
                    
                    sock.close()
                except Exception as e:
                    print(f"❌ Port UDP {port}: Erreur - {e}")
        
        return True
    
    def test_8_processus_reseau(self):
        """Test 8: Processus utilisant le réseau"""
        print("\n🔍 TEST 8: PROCESSUS RÉSEAU")
        print("-" * 50)
        
        try:
            connections = psutil.net_connections()
            
            # Chercher les connexions vers notre cible
            target_connections = []
            for conn in connections:
                if (conn.raddr and conn.raddr.ip == self.target_ip):
                    target_connections.append(conn)
            
            if target_connections:
                print(f"✅ {len(target_connections)} connexions actives vers {self.target_ip}")
                for conn in target_connections:
                    print(f"   {conn.laddr} -> {conn.raddr} ({conn.status})")
            else:
                print(f"⚠️  Aucune connexion active vers {self.target_ip}")
            
            return True
        except Exception as e:
            print(f"❌ Erreur processus réseau: {e}")
            return False
    
    def generer_rapport(self):
        """Générer le rapport final"""
        print("\n" + "=" * 60)
        print("📊 RAPPORT DIAGNOSTIC PC")
        print("=" * 60)
        
        total_tests = 8
        tests_ok = len([k for k in self.results.keys() if self.results[k]])
        
        print(f"Tests réussis: {tests_ok}/{total_tests}")
        
        if tests_ok >= 6:
            print("✅ PC CONFIGURÉ CORRECTEMENT pour communication IC-R8600")
            print("Le problème est probablement côté IC-R8600")
        else:
            print("⚠️  PROBLÈMES DÉTECTÉS côté PC")
            print("Corrections nécessaires avant de tester l'IC-R8600")
        
        print("\nDétails:")
        for test, result in self.results.items():
            status = "✅" if result else "❌"
            print(f"{status} {test}")

def main():
    """Diagnostic expert complet"""
    print("🔧 DIAGNOSTIC EXPERT PC - TÉLÉCOMMUNICATIONS")
    print("=" * 60)
    print("Vérification complète de la stack réseau Windows")
    print("Cible: IC-R8600 (**************)")
    print("=" * 60)
    
    diagnostic = DiagnosticExpertPC()
    
    # Exécution séquentielle des tests
    tests = [
        diagnostic.test_1_interfaces_reseau,
        diagnostic.test_2_table_routage,
        diagnostic.test_3_arp_table,
        diagnostic.test_4_pare_feu_windows,
        diagnostic.test_5_sockets_udp_disponibles,
        diagnostic.test_6_connectivite_icmp,
        diagnostic.test_7_ports_tcp_udp_cible,
        diagnostic.test_8_processus_reseau,
    ]
    
    for i, test in enumerate(tests, 1):
        try:
            test()
            time.sleep(1)  # Pause entre tests
        except Exception as e:
            print(f"❌ Erreur test {i}: {e}")
    
    # Rapport final
    diagnostic.generer_rapport()

if __name__ == "__main__":
    main()
