#!/usr/bin/env python3
"""
Test rapide de validation du contrôle réseau IC-R8600
Script de diagnostic pour vérifier que tout fonctionne
"""

import sys
import time
import subprocess
from icom_network_handler import ICOMNetworkHandler, NetworkConfig, ConnectionType

def test_ping(host="**************"):
    """Test de ping rapide"""
    print(f"🔍 Test ping vers {host}...")
    
    try:
        if sys.platform.startswith('win'):
            result = subprocess.run(
                ["ping", "-n", "1", "-w", "3000", host],
                capture_output=True,
                text=True,
                timeout=5
            )
        else:
            result = subprocess.run(
                ["ping", "-c", "1", "-W", "3", host],
                capture_output=True,
                text=True,
                timeout=5
            )
        
        if result.returncode == 0:
            print(f"  ✅ Ping réussi vers {host}")
            return True
        else:
            print(f"  ❌ Ping échoué vers {host}")
            return False
            
    except Exception as e:
        print(f"  ❌ Erreur ping: {e}")
        return False

def test_network_connection(host="**************", port=50001):
    """Test de connexion réseau CI-V"""
    print(f"🌐 Test connexion réseau {host}:{port}...")
    
    # Test UDP
    print("  📡 Test UDP...")
    udp_config = NetworkConfig(
        host=host,
        port=port,
        connection_type=ConnectionType.UDP,
        timeout=3.0
    )
    
    udp_handler = ICOMNetworkHandler(udp_config)
    udp_success = udp_handler.connect()
    
    if udp_success:
        print("    ✅ Connexion UDP réussie")
        
        # Test commande simple
        freq = udp_handler.get_frequency()
        if freq:
            print(f"    ✅ Lecture fréquence: {freq} Hz")
        else:
            print("    ⚠️ Lecture fréquence échouée")
        
        udp_handler.disconnect()
    else:
        print("    ❌ Connexion UDP échouée")
    
    # Test TCP
    print("  📡 Test TCP...")
    tcp_config = NetworkConfig(
        host=host,
        port=port,
        connection_type=ConnectionType.TCP,
        timeout=3.0
    )
    
    tcp_handler = ICOMNetworkHandler(tcp_config)
    tcp_success = tcp_handler.connect()
    
    if tcp_success:
        print("    ✅ Connexion TCP réussie")
        
        # Test commande simple
        freq = tcp_handler.get_frequency()
        if freq:
            print(f"    ✅ Lecture fréquence: {freq} Hz")
        else:
            print("    ⚠️ Lecture fréquence échouée")
        
        tcp_handler.disconnect()
    else:
        print("    ❌ Connexion TCP échouée")
    
    return udp_success or tcp_success

def test_basic_commands(host="**************", port=50001):
    """Test des commandes de base"""
    print("🧪 Test commandes de base...")
    
    # Utiliser UDP par défaut
    config = NetworkConfig(
        host=host,
        port=port,
        connection_type=ConnectionType.UDP,
        timeout=3.0
    )
    
    handler = ICOMNetworkHandler(config)
    
    if not handler.connect():
        print("  ❌ Connexion échouée")
        return False
    
    success_count = 0
    total_tests = 0
    
    # Test lecture fréquence
    total_tests += 1
    freq = handler.get_frequency()
    if freq:
        print(f"  ✅ Fréquence actuelle: {freq} Hz")
        success_count += 1
    else:
        print("  ❌ Lecture fréquence échouée")
    
    # Test lecture mode
    total_tests += 1
    mode = handler.get_mode()
    if mode:
        print(f"  ✅ Mode actuel: {mode}")
        success_count += 1
    else:
        print("  ❌ Lecture mode échouée")
    
    # Test changement fréquence (145.5 MHz)
    total_tests += 1
    original_freq = freq
    test_freq = 145500000
    
    if handler.set_frequency(test_freq):
        time.sleep(0.5)
        new_freq = handler.get_frequency()
        if new_freq == test_freq:
            print(f"  ✅ Changement fréquence: {test_freq} Hz")
            success_count += 1
            
            # Restaurer la fréquence originale
            if original_freq:
                handler.set_frequency(original_freq)
        else:
            print(f"  ❌ Changement fréquence échoué: {new_freq} != {test_freq}")
    else:
        print("  ❌ Commande changement fréquence échouée")
    
    # Test changement mode
    total_tests += 1
    original_mode = mode
    test_mode = "FM"
    
    if handler.set_mode(test_mode):
        time.sleep(0.5)
        new_mode = handler.get_mode()
        if new_mode == test_mode:
            print(f"  ✅ Changement mode: {test_mode}")
            success_count += 1
            
            # Restaurer le mode original
            if original_mode:
                handler.set_mode(original_mode)
        else:
            print(f"  ❌ Changement mode échoué: {new_mode} != {test_mode}")
    else:
        print("  ❌ Commande changement mode échouée")
    
    handler.disconnect()
    
    print(f"  📊 Résultat: {success_count}/{total_tests} tests réussis")
    return success_count == total_tests

def test_application_startup():
    """Test de démarrage de l'application"""
    print("🚀 Test démarrage application...")
    
    try:
        # Import des modules principaux
        from main import app
        from icom_network_handler import ICOMNetworkHandler
        
        print("  ✅ Modules importés avec succès")
        return True
        
    except ImportError as e:
        print(f"  ❌ Erreur import: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Erreur: {e}")
        return False

def print_recommendations(results):
    """Affiche les recommandations basées sur les résultats"""
    print("\n💡 RECOMMANDATIONS:")
    
    if results["ping"]:
        print("  ✅ Connectivité réseau OK")
    else:
        print("  ❌ Problème de connectivité réseau")
        print("    🔧 Vérifiez le câble RJ45")
        print("    🔧 Vérifiez l'IP de l'IC-R8600 (**************)")
        print("    🔧 Vérifiez l'IP de votre PC (192.168.37.x)")
    
    if results["network"]:
        print("  ✅ Connexion CI-V réseau fonctionnelle")
    else:
        print("  ❌ Problème de connexion CI-V")
        print("    🔧 Activez 'Remote Control' sur l'IC-R8600")
        print("    🔧 Vérifiez le port CI-V (50001)")
        print("    🔧 Redémarrez l'IC-R8600 après configuration")
    
    if results["commands"]:
        print("  ✅ Commandes CI-V fonctionnelles")
        print("  🎉 Contrôle réseau opérationnel!")
    else:
        print("  ❌ Problème avec les commandes CI-V")
        print("    🔧 Vérifiez la configuration CI-V sur l'IC-R8600")
    
    if results["application"]:
        print("  ✅ Application prête à démarrer")
    else:
        print("  ❌ Problème avec l'application")
        print("    🔧 Vérifiez l'installation des dépendances Python")

def main():
    """Fonction principale de test"""
    print("Test Rapide Contrôle Réseau IC-R8600")
    print("====================================")
    
    # Demander l'IP si différente
    host = input("IP de l'IC-R8600 (défaut: **************): ").strip()
    if not host:
        host = "**************"
    
    port = input("Port CI-V (défaut: 50001): ").strip()
    if not port:
        port = 50001
    else:
        port = int(port)
    
    print(f"\n🎯 Test de {host}:{port}")
    print("-" * 40)
    
    # Exécuter les tests
    results = {
        "ping": test_ping(host),
        "network": test_network_connection(host, port),
        "commands": False,
        "application": test_application_startup()
    }
    
    # Test des commandes seulement si la connexion réseau fonctionne
    if results["network"]:
        results["commands"] = test_basic_commands(host, port)
    
    # Résumé
    print("\n" + "=" * 40)
    print("RÉSUMÉ DES TESTS")
    print("=" * 40)
    
    for test_name, success in results.items():
        status = "✅ OK" if success else "❌ KO"
        print(f"{test_name.capitalize():15}: {status}")
    
    # Recommandations
    print_recommendations(results)
    
    # Score global
    success_count = sum(results.values())
    total_tests = len(results)
    score = (success_count / total_tests) * 100
    
    print(f"\n📊 Score global: {success_count}/{total_tests} ({score:.0f}%)")
    
    if score == 100:
        print("🎉 Parfait! Le contrôle réseau est entièrement fonctionnel!")
    elif score >= 75:
        print("✅ Bon! Le contrôle réseau fonctionne largement.")
    elif score >= 50:
        print("⚠️ Moyen. Quelques problèmes à résoudre.")
    else:
        print("❌ Problèmes importants. Consultez le guide de configuration.")
    
    print(f"\n📖 Guide complet: NETWORK_SETUP_GUIDE.md")
    print(f"🔧 Configuration: python configure_network.py")
    print(f"🧪 Tests avancés: python test_network_protocol.py")

if __name__ == "__main__":
    main()
