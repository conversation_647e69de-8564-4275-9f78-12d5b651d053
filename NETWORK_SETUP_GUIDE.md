# Guide de Configuration Réseau IC-R8600

Ce guide vous explique comment configurer le contrôle réseau de votre ICOM IC-R8600 via câble RJ45 Ethernet.

## 🎯 Objectif

Contrôler l'IC-R8600 via réseau Ethernet (RJ45) au lieu du câble USB, permettant :

- Contrôle à distance via réseau local
- Latence réduite
- Pas de drivers USB nécessaires
- Connexion plus stable

## 📋 Prérequis

### Matériel

- ICOM IC-R8600 avec firmware récent
- Câble Ethernet RJ45 (CAT5e ou supérieur)
- PC avec port Ethernet ou adaptateur USB-Ethernet
- Switch/routeur réseau (optionnel pour connexion directe)

### Logiciel

- Python 3.8+ avec les modules du projet
- Accès administrateur pour configuration réseau

## 🔧 Configuration IC-R8600

### Étape 1: Accès au Menu

1. Allumez l'IC-R8600
2. Appuyez sur **MENU**
3. Naviguez vers **SET** → **Connectors** → **LAN**

### Étape 2: Configuration IP

```
Menu: SET → Connectors → LAN → IP Settings
┌─────────────────────────────────┐
│ IP Address    : **************  │
│ Subnet Mask   : *************   │
│ Gateway       : (vide)          │
│ DHCP          : OFF             │
│ DNS Server    : (vide)          │
└─────────────────────────────────┘
```

### Étape 3: Activation Remote Control

```
Menu: SET → Connectors → LAN → Remote Control
┌─────────────────────────────────┐
│ Remote Control: ON              │
│ CI-V Port     : 50001           │
│ Protocol      : CI-V            │
└─────────────────────────────────┘
```

### Étape 4: Redémarrage

1. Sauvegardez les paramètres
2. Éteignez l'IC-R8600
3. Connectez le câble RJ45
4. Rallumez l'IC-R8600

## 🖥️ Configuration PC

### Option A: IP Statique (Recommandée)

#### Windows

```cmd
# Ouvrir PowerShell en tant qu'administrateur
netsh interface ip set address "Ethernet" static ************* *************
```

#### Linux

```bash
sudo ip addr add *************/24 dev eth0
sudo ip link set eth0 up
```

#### macOS

```bash
sudo ifconfig en0 ************* netmask *************
```

### Option B: Configuration Automatique

Utilisez le script fourni :

```bash
cd backend
python configure_network.py
```

## 🧪 Tests de Connectivité

### Test 1: Ping

```bash
ping **************
```

**Résultat attendu :** Réponses avec temps < 10ms

### Test 2: Scanner Réseau

```bash
cd backend
python advanced_network_scanner.py
```

### Test 3: Test Protocole

```bash
cd backend
python test_network_protocol.py
```

### Test 4: Test Application

```bash
cd backend
python icom_network_handler.py
```

## 🚀 Utilisation

### Démarrage avec Réseau

```bash
cd backend
python main.py
```

L'application tentera automatiquement :

1. **Connexion UDP** sur *************:50001
2. **Connexion TCP** si UDP échoue
3. **Fallback USB** si réseau échoue

### Vérification du Statut

```bash
curl http://localhost:8000/api/connection/status
```

## 🔍 Dépannage

### Problème: Pas de ping

**Causes possibles :**

- Câble RJ45 défectueux
- Configuration IP incorrecte
- IC-R8600 pas redémarré après config

**Solutions :**

1. Vérifiez le câble avec un autre appareil
2. Reconfigurez l'IP sur l'IC-R8600
3. Redémarrez l'IC-R8600

### Problème: Ping OK mais pas de contrôle CI-V

**Causes possibles :**

- Remote Control désactivé
- Port CI-V incorrect
- Firewall bloquant

**Solutions :**

1. Vérifiez `Remote Control: ON`
2. Confirmez `CI-V Port: 50001`
3. Désactivez temporairement le firewall

### Problème: Connexion instable

**Causes possibles :**

- Interférences réseau
- Switch défaillant
- Configuration DHCP conflictuelle

**Solutions :**

1. Utilisez une connexion directe PC ↔ IC-R8600
2. Changez de port sur le switch
3. Vérifiez qu'aucun autre appareil n'utilise *************

### Problème: Latence élevée

**Causes possibles :**

- Réseau surchargé
- Mauvaise qualité du câble
- Configuration réseau sous-optimale

**Solutions :**

1. Utilisez un câble CAT6
2. Connexion directe sans switch
3. Vérifiez la configuration duplex (full-duplex recommandé)

## 📊 Performances Attendues

| Métrique         | UDP        | TCP     | USB      |
| ---------------- | ---------- | ------- | -------- |
| Latence commande | 5-15ms     | 10-25ms | 20-50ms  |
| Débit            | Élevé      | Moyen   | Faible   |
| Stabilité        | Excellente | Bonne   | Variable |
| Distance max     | 100m       | 100m    | 5m       |

## 🔧 Configuration Avancée

### Ports Alternatifs

Si le port 50001 est occupé :

```
IC-R8600: Menu → SET → Connectors → LAN → CI-V Port
Options: 50001, 50002, 50003, 50004, 50005
```

### Réseau Complexe

Pour intégration dans un réseau existant :

```
IP IC-R8600    : *************
IP PC          : ***********0
Passerelle     : ***********
DNS            : *******
```

### Sécurité

- Utilisez un VLAN dédié pour les équipements radio
- Configurez un firewall pour limiter l'accès
- Changez l'IP par défaut si nécessaire

## 📝 Scripts Utiles

### Test Rapide

```bash
# Test complet automatique
cd backend
python -c "
from advanced_network_scanner import ICOMNetworkScanner
scanner = ICOMNetworkScanner('*************')
results = scanner.full_scan()
print('Réseau OK' if results['ping_success'] else 'Réseau KO')
"
```

### Monitoring

```bash
# Surveillance continue
while true; do
    ping -c 1 ************* > /dev/null && echo "$(date): OK" || echo "$(date): KO"
    sleep 5
done
```

## 🆘 Support

### Logs de Debug

```bash
cd backend
python main.py --log-level debug 2>&1 | tee debug.log
```

### Informations Système

```bash
# Windows
ipconfig /all
netstat -an | findstr 50001

# Linux
ip addr show
ss -tuln | grep 50001
```

### Contact

- Consultez les manuels IC-R8600 (PDF dans le projet)
- Vérifiez les logs dans `backend/backend.log`
- Testez avec le logiciel officiel RS-R8600 pour validation

## ✅ Checklist de Validation

- [ ] IC-R8600 allumé et configuré
- [ ] Câble RJ45 connecté
- [ ] IP PC configurée (***********0)
- [ ] Ping vers ************* réussi
- [ ] Port 50001 accessible
- [ ] Remote Control activé sur IC-R8600
- [ ] Application Python démarre sans erreur
- [ ] Commandes CI-V fonctionnelles
- [ ] Fallback USB disponible si nécessaire

---

**Note :** Ce guide suppose une configuration réseau simple. Pour des environnements complexes, adaptez les adresses IP selon votre infrastructure réseau existante.
