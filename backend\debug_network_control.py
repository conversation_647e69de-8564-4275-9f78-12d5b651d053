#!/usr/bin/env python3
"""
Debug approfondi du Network Control IC-R8600
Test de différentes approches et configurations
"""

import socket
import time
import subprocess
import sys

def test_wireshark_capture():
    """Suggère l'utilisation de Wireshark pour capturer le trafic"""
    print("🔍 ANALYSE RÉSEAU RECOMMANDÉE")
    print("=" * 50)
    print("Pour diagnostiquer le problème, nous recommandons d'utiliser Wireshark :")
    print()
    print("1. 📥 Télécharger Wireshark : https://www.wireshark.org/")
    print("2. 🎯 Capturer le trafic sur l'interface Ethernet")
    print("3. 🔍 Filtrer par : ip.addr == **************")
    print("4. 📤 Lancer notre test pendant la capture")
    print("5. 📊 Analyser si les paquets UDP arrivent à l'IC-R8600")
    print()

def test_different_udp_approaches():
    """Test de différentes approches UDP"""
    host = "**************"
    
    print("🧪 TEST D'APPROCHES UDP ALTERNATIVES")
    print("=" * 50)
    
    # Approche 1: UDP avec bind local
    print("\n1. 🔌 Test UDP avec bind local...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.bind(('', 0))  # Bind sur un port local aléatoire
        local_port = sock.getsockname()[1]
        print(f"   Port local: {local_port}")
        
        sock.settimeout(2.0)
        ci_v_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
        
        sock.sendto(ci_v_cmd, (host, 50001))
        print(f"   Envoyé: {ci_v_cmd.hex().upper()}")
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"   ✅ Réponse: {response.hex().upper()}")
            sock.close()
            return True
        except socket.timeout:
            print("   ❌ Timeout")
        
        sock.close()
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Approche 2: UDP avec SO_REUSEADDR
    print("\n2. 🔄 Test UDP avec SO_REUSEADDR...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        sock.settimeout(2.0)
        
        ci_v_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
        sock.sendto(ci_v_cmd, (host, 50001))
        print(f"   Envoyé: {ci_v_cmd.hex().upper()}")
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"   ✅ Réponse: {response.hex().upper()}")
            sock.close()
            return True
        except socket.timeout:
            print("   ❌ Timeout")
        
        sock.close()
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Approche 3: UDP avec broadcast
    print("\n3. 📡 Test UDP broadcast...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        sock.settimeout(2.0)
        
        ci_v_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
        sock.sendto(ci_v_cmd, ('**************', 50001))
        print(f"   Envoyé en broadcast: {ci_v_cmd.hex().upper()}")
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"   ✅ Réponse de {addr}: {response.hex().upper()}")
            sock.close()
            return True
        except socket.timeout:
            print("   ❌ Timeout")
        
        sock.close()
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    return False

def test_rs_r8600_compatibility():
    """Test de compatibilité avec RS-R8600"""
    print("\n🎯 TEST DE COMPATIBILITÉ RS-R8600")
    print("=" * 50)
    
    print("Pour vérifier que l'IC-R8600 fonctionne correctement :")
    print()
    print("1. 📥 Télécharger RS-R8600 officiel d'ICOM")
    print("2. 🔌 Configurer RS-R8600 avec les mêmes paramètres :")
    print("   - IP: **************")
    print("   - Port: 50001")
    print("   - Protocol: UDP")
    print("3. 🧪 Tester la connexion avec RS-R8600")
    print("4. ✅ Si RS-R8600 fonctionne, le problème vient de notre code")
    print("5. ❌ Si RS-R8600 ne fonctionne pas, problème de configuration IC-R8600")
    print()

def test_alternative_ports():
    """Test des ports alternatifs"""
    host = "**************"
    
    print("\n🔍 TEST DES PORTS ALTERNATIFS")
    print("=" * 50)
    
    # Ports à tester (certains IC-R8600 utilisent des ports différents)
    test_ports = [
        (50001, "Control Port (configuré)"),
        (50002, "Serial Port (configuré)"), 
        (50003, "Audio Port (configuré)"),
        (4532, "Port CI-V standard ICOM"),
        (4533, "Port CI-V alternatif"),
        (8080, "Port HTTP alternatif"),
        (8000, "Port de contrôle alternatif"),
        (23, "Telnet (fonctionne déjà)"),
    ]
    
    for port, desc in test_ports:
        print(f"\n🔌 Test port {port} ({desc})")
        
        # Test UDP
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(1.0)
            
            ci_v_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
            sock.sendto(ci_v_cmd, (host, port))
            
            try:
                response, addr = sock.recvfrom(1024)
                print(f"   ✅ UDP réponse: {response.hex().upper()}")
                sock.close()
                return port
            except socket.timeout:
                print("   ❌ UDP timeout")
            
            sock.close()
        except Exception as e:
            print(f"   ❌ UDP erreur: {e}")
        
        # Test TCP (sauf pour les ports UDP connus)
        if port not in [50001, 50002, 50003]:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1.0)
                
                result = sock.connect_ex((host, port))
                if result == 0:
                    print(f"   ✅ TCP connecté")
                    
                    # Tester CI-V sur TCP
                    ci_v_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
                    sock.send(ci_v_cmd)
                    
                    try:
                        response = sock.recv(1024)
                        print(f"   ✅ TCP CI-V réponse: {response.hex().upper()}")
                        sock.close()
                        return port
                    except socket.timeout:
                        print("   ❌ TCP CI-V timeout")
                else:
                    print("   ❌ TCP refusé")
                
                sock.close()
            except Exception as e:
                print(f"   ❌ TCP erreur: {e}")
    
    return None

def check_ic_r8600_manual():
    """Vérifications basées sur le manuel IC-R8600"""
    print("\n📖 VÉRIFICATIONS MANUEL IC-R8600")
    print("=" * 50)
    
    print("Selon le manuel IC-R8600, vérifiez :")
    print()
    print("1. 🔧 Configuration réseau complète :")
    print("   MENU → SET → Connectors → Network")
    print("   - DHCP: OFF (pour IP fixe)")
    print("   - IP Address: **************")
    print("   - Subnet Mask: *************")
    print("   - Default Gateway: ************")
    print("   - Control Port UDP: 50001")
    print("   - Serial Port UDP: 50002")
    print("   - Audio Port UDP: 50003")
    print("   - Network Control: ON")
    print()
    print("2. 🔄 Redémarrage obligatoire après changement")
    print()
    print("3. 🔘 Bouton LOCAL sur le panneau avant :")
    print("   - Le voyant LOCAL doit être ÉTEINT")
    print("   - Si allumé, appuyer sur LOCAL pour le désactiver")
    print()
    print("4. 🌐 Test de connectivité réseau :")
    print("   - Ping doit fonctionner")
    print("   - Port Telnet 23 doit être ouvert")
    print()
    print("5. 🔌 Câblage :")
    print("   - Câble RJ45 droit (pas croisé)")
    print("   - Connecté au port LAN arrière de l'IC-R8600")
    print("   - Voyants de liaison sur le switch/routeur")

if __name__ == "__main__":
    print("=" * 70)
    print("🔍 DEBUG APPROFONDI NETWORK CONTROL IC-R8600")
    print("=" * 70)
    
    # Test 1: Approches UDP alternatives
    udp_success = test_different_udp_approaches()
    
    # Test 2: Ports alternatifs
    working_port = test_alternative_ports()
    
    # Test 3: Suggestions d'analyse
    test_wireshark_capture()
    
    # Test 4: Compatibilité RS-R8600
    test_rs_r8600_compatibility()
    
    # Test 5: Vérifications manuel
    check_ic_r8600_manual()
    
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ DU DEBUG")
    print("=" * 70)
    
    if udp_success:
        print("✅ Une approche UDP alternative fonctionne")
    elif working_port:
        print(f"✅ Port alternatif fonctionnel trouvé: {working_port}")
    else:
        print("❌ Aucune communication UDP/TCP CI-V détectée")
        print()
        print("🔧 ACTIONS RECOMMANDÉES (par ordre de priorité) :")
        print()
        print("1. 📖 Vérifier TOUTES les configurations du manuel")
        print("2. 🔄 Redémarrer complètement l'IC-R8600")
        print("3. 🧪 Tester avec RS-R8600 officiel")
        print("4. 🔍 Capturer le trafic avec Wireshark")
        print("5. 📞 Contacter le support ICOM si nécessaire")
    
    print("=" * 70)
