#!/usr/bin/env python3
"""
Test final de l'application avec Telnet CI-V
Version sans emojis pour éviter les problèmes d'encodage
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from icom_network_handler import ICOMNetworkHandler, NetworkConfig
import time

def test_application_telnet():
    """Test final de l'application avec Telnet"""
    print("=" * 60)
    print("TEST FINAL APPLICATION IC-R8600 VIA TELNET")
    print("=" * 60)
    
    # Configuration Telnet
    config = NetworkConfig(
        host="**************",
        telnet_port=23,
        timeout=5.0
    )
    
    # <PERSON><PERSON><PERSON> le handler
    handler = ICOMNetworkHandler(config)
    
    print("1. Connexion à l'IC-R8600...")
    if handler.connect():
        print("   [OK] Connexion Telnet établie")
        print("   [OK] IC-R8600 contrôlable via CI-V")
    else:
        print("   [ERREUR] Connexion échouée")
        return False
    
    print("\n2. Test de lecture de fréquence...")
    try:
        frequency = handler.get_frequency()
        if frequency:
            print(f"   [OK] Fréquence actuelle: {frequency:.6f} MHz")
        else:
            print("   [ERREUR] Impossible de lire la fréquence")
    except Exception as e:
        print(f"   [ERREUR] Exception: {e}")
    
    print("\n3. Test de lecture de mode...")
    try:
        mode = handler.get_mode()
        if mode:
            print(f"   [OK] Mode actuel: {mode}")
        else:
            print("   [ERREUR] Impossible de lire le mode")
    except Exception as e:
        print(f"   [ERREUR] Exception: {e}")
    
    print("\n4. Test de changement de fréquence...")
    try:
        test_freq = 145.500  # MHz
        if handler.set_frequency(test_freq):
            print(f"   [OK] Fréquence changée vers {test_freq} MHz")
            
            # Vérifier le changement
            time.sleep(0.5)
            new_freq = handler.get_frequency()
            if new_freq and abs(new_freq - test_freq) < 0.001:
                print(f"   [OK] Changement confirmé: {new_freq:.6f} MHz")
            else:
                print(f"   [ATTENTION] Fréquence lue: {new_freq:.6f} MHz")
        else:
            print("   [ERREUR] Impossible de changer la fréquence")
    except Exception as e:
        print(f"   [ERREUR] Exception: {e}")
    
    print("\n5. Déconnexion...")
    handler.disconnect()
    print("   [OK] Déconnexion propre")
    
    print("\n" + "=" * 60)
    print("RESULTAT: APPLICATION FONCTIONNELLE VIA TELNET !")
    print("=" * 60)
    print("L'IC-R8600 est maintenant contrôlable via:")
    print("- Protocol: TCP Telnet (port 23)")
    print("- Commandes: CI-V encapsulées")
    print("- Interface: Application web sur http://localhost:8000")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = test_application_telnet()
    if success:
        print("\nL'application est prête à être utilisée !")
        print("Lancez 'python main.py' pour démarrer le serveur web.")
    else:
        print("\nDes problèmes subsistent. Vérifiez la configuration.")
