#!/usr/bin/env python3
"""
Diagnostic avancé pour identifier le problème exact
"""

import socket
import subprocess
import time

def test_network_layer():
    """Test de la couche réseau"""
    print("🌐 TEST COUCHE RÉSEAU")
    print("-" * 30)
    
    # Test ARP
    try:
        result = subprocess.run(['arp', '-a'], capture_output=True, text=True)
        if '**************' in result.stdout:
            print("✅ IC-R8600 dans la table ARP")
            # Extraire l'adresse MAC
            for line in result.stdout.split('\n'):
                if '**************' in line:
                    print(f"   {line.strip()}")
        else:
            print("❌ IC-R8600 pas dans la table ARP")
    except:
        print("❌ Impossible de vérifier ARP")

def test_port_range():
    """Test d'une plage de ports étendue"""
    print("\n🔍 TEST PLAGE DE PORTS ÉTENDUE")
    print("-" * 40)
    
    host = "**************"
    
    # Test ports UDP dans une plage plus large
    port_ranges = [
        range(50000, 50010),  # Ports ICOM standards
        range(23, 24),        # Telnet
        range(80, 81),        # HTTP
        range(8000, 8010),    # Ports alternatifs
    ]
    
    open_ports = []
    
    for port_range in port_ranges:
        for port in port_range:
            try:
                # Test UDP
                sock_udp = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                sock_udp.settimeout(1.0)
                sock_udp.sendto(b'\x00\x01', (host, port))
                
                try:
                    response, addr = sock_udp.recvfrom(1024)
                    print(f"✅ Port {port} UDP: RÉPONSE - {response.hex().upper()}")
                    open_ports.append(f"{port}/UDP")
                except socket.timeout:
                    pass
                
                sock_udp.close()
                
                # Test TCP
                sock_tcp = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock_tcp.settimeout(1.0)
                result = sock_tcp.connect_ex((host, port))
                
                if result == 0:
                    print(f"✅ Port {port} TCP: OUVERT")
                    open_ports.append(f"{port}/TCP")
                
                sock_tcp.close()
                
            except Exception as e:
                pass
    
    if open_ports:
        print(f"\nPorts détectés: {open_ports}")
    else:
        print("\n❌ Aucun port ouvert détecté")

def test_icmp_ping():
    """Test ping détaillé"""
    print("\n📡 TEST PING DÉTAILLÉ")
    print("-" * 25)
    
    try:
        result = subprocess.run(['ping', '-n', '10', '**************'], 
                              capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            print("✅ Ping réussi")
            
            # Analyser les statistiques
            lines = result.stdout.split('\n')
            for line in lines:
                if 'Paquets' in line or 'perte' in line or 'Minimum' in line:
                    print(f"   {line.strip()}")
        else:
            print("❌ Ping échoué")
            print(result.stdout)
            
    except Exception as e:
        print(f"❌ Erreur ping: {e}")

def test_network_interface():
    """Test de l'interface réseau"""
    print("\n🔌 TEST INTERFACE RÉSEAU")
    print("-" * 30)
    
    try:
        result = subprocess.run(['ipconfig'], capture_output=True, text=True)
        
        # Chercher l'interface 192.168.37.x
        lines = result.stdout.split('\n')
        in_ethernet = False
        
        for line in lines:
            if 'Ethernet' in line:
                in_ethernet = True
                print(f"Interface: {line.strip()}")
            elif in_ethernet and '192.168.37' in line:
                print(f"✅ {line.strip()}")
            elif in_ethernet and line.strip() == '':
                in_ethernet = False
                
    except Exception as e:
        print(f"❌ Erreur interface: {e}")

def test_firewall():
    """Test du pare-feu"""
    print("\n🛡️ TEST PARE-FEU")
    print("-" * 20)
    
    try:
        # Test si le pare-feu Windows bloque
        result = subprocess.run(['netsh', 'advfirewall', 'show', 'allprofiles', 'state'], 
                              capture_output=True, text=True)
        
        if 'ON' in result.stdout:
            print("⚠️ Pare-feu Windows activé")
            print("   Peut bloquer les connexions UDP")
        else:
            print("✅ Pare-feu Windows désactivé")
            
    except Exception as e:
        print(f"❌ Impossible de vérifier le pare-feu: {e}")

def test_manual_telnet():
    """Test Telnet manuel avec analyse"""
    print("\n📞 TEST TELNET MANUEL")
    print("-" * 25)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        sock.connect(('**************', 23))
        
        print("✅ Connexion Telnet établie")
        
        # Envoyer des commandes spécifiques IC-R8600
        commands = [
            b'*IDN?\r\n',
            b'*RST\r\n',
            b'SYST:ERR?\r\n',
            b'CONF:NETW?\r\n',
            b'STAT:NETW?\r\n',
        ]
        
        for cmd in commands:
            print(f"Envoi: {cmd}")
            sock.sendall(cmd)
            time.sleep(1)
            
            try:
                response = sock.recv(1024)
                if response:
                    print(f"Réponse: {response}")
                    print(f"Hex: {response.hex().upper()}")
            except socket.timeout:
                print("Pas de réponse")
        
        sock.close()
        
    except Exception as e:
        print(f"❌ Erreur Telnet: {e}")

def main():
    """Diagnostic complet"""
    print("🔍 DIAGNOSTIC AVANCÉ IC-R8600")
    print("=" * 50)
    
    test_network_layer()
    test_icmp_ping()
    test_network_interface()
    test_firewall()
    test_port_range()
    test_manual_telnet()
    
    print("\n" + "=" * 50)
    print("📋 RÉSUMÉ DIAGNOSTIC")
    print("=" * 50)
    print("Si aucun port UDP n'est ouvert :")
    print("1. Network Control n'est PAS activé sur l'IC-R8600")
    print("2. Ou firmware incompatible")
    print("3. Ou problème matériel réseau")
    print("\nSi ports TCP ouverts mais pas UDP :")
    print("1. Configuration partielle")
    print("2. Redémarrage nécessaire")
    print("3. Mode REMOTE non activé")

if __name__ == "__main__":
    main()
