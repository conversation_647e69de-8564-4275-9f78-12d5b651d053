#!/usr/bin/env python3
"""
Test des commandes UDP pour vérifier l'effet réel sur l'IC-R8600
"""

import socket
import time
import sys

def test_udp_power_commands():
    """Test des commandes power via UDP"""
    host = "**************"
    port = 50001  # Port UDP de contrôle
    
    print("TEST COMMANDES POWER UDP")
    print("=" * 50)
    print("IMPORTANT: Observez l'IC-R8600 pendant ce test !")
    print("Assurez-vous que Network Control est ON sur l'IC-R8600")
    print("=" * 50)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(5.0)
        
        # Commandes CI-V à tester
        commands = [
            # Format: (commande, description, délai d'observation)
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x19, 0x00, 0xFD]), "Lecture statut power", 2),
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x00, 0xFD]), "Power OFF", 5),
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x19, 0x00, 0xFD]), "Vérification power OFF", 2),
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x01, 0xFD]), "Power ON", 5),
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x19, 0x00, 0xFD]), "Vérification power ON", 2),
        ]
        
        for i, (cmd, desc, delay) in enumerate(commands):
            print(f"\n{i+1}. {desc}")
            print(f"   Commande UDP: {cmd.hex().upper()}")
            print(f"   OBSERVEZ L'IC-R8600 MAINTENANT !")
            
            # Envoyer la commande via UDP
            sock.sendto(cmd, (host, port))
            
            # Attendre la réponse
            time.sleep(0.5)
            try:
                response, addr = sock.recvfrom(1024)
                print(f"   Réponse UDP: {response.hex().upper()}")
                
                # Analyser la réponse
                if b'\xFE\xFE' in response:
                    fe_pos = response.find(b'\xFE\xFE')
                    fd_pos = response.find(b'\xFD', fe_pos)
                    if fd_pos >= 0:
                        ci_v_data = response[fe_pos:fd_pos+1]
                        if len(ci_v_data) >= 6:
                            cmd_byte = ci_v_data[4]
                            if cmd_byte == 0xFB:
                                print("   -> ACK: Commande acceptée ✅")
                            elif cmd_byte == 0xFA:
                                print("   -> NAK: Commande refusée ❌")
                            elif cmd_byte == 0x19 and len(ci_v_data) >= 7:
                                power_status = ci_v_data[6]
                                status_text = "ON" if power_status == 0x01 else "OFF"
                                print(f"   -> Statut power: {status_text}")
                            else:
                                print(f"   -> Réponse: 0x{cmd_byte:02X}")
                
            except socket.timeout:
                print("   -> Pas de réponse UDP")
            
            # Délai d'observation
            print(f"   Attente {delay}s pour observer l'effet sur l'IC-R8600...")
            time.sleep(delay)
        
        sock.close()
        print("\nTest UDP terminé")
        
    except Exception as e:
        print(f"Erreur UDP: {e}")

def test_frequency_commands():
    """Test des commandes de fréquence via UDP"""
    host = "**************"
    port = 50001
    
    print("\n" + "=" * 50)
    print("TEST COMMANDES FRÉQUENCE UDP")
    print("=" * 50)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(5.0)
        
        # Test fréquence 145.500 MHz (format BCD)
        # 145500000 Hz = 145500000 en BCD inversé
        freq_bcd = [0x00, 0x00, 0x50, 0x45, 0x01]  # 145.500 MHz en BCD
        freq_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x05] + freq_bcd + [0xFD])
        
        print("Test changement de fréquence à 145.500 MHz")
        print(f"Commande: {freq_cmd.hex().upper()}")
        print("OBSERVEZ L'AFFICHAGE DE FRÉQUENCE SUR L'IC-R8600 !")
        
        sock.sendto(freq_cmd, (host, port))
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"Réponse: {response.hex().upper()}")
        except socket.timeout:
            print("Pas de réponse")
        
        print("Attente 5s pour observer le changement...")
        time.sleep(5)
        
        # Test lecture de fréquence
        read_freq_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
        print("\nLecture de la fréquence actuelle")
        print(f"Commande: {read_freq_cmd.hex().upper()}")
        
        sock.sendto(read_freq_cmd, (host, port))
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"Réponse: {response.hex().upper()}")
            
            if len(response) >= 11:
                freq_data = response[6:11]
                print(f"Données fréquence: {freq_data.hex().upper()}")
        except socket.timeout:
            print("Pas de réponse")
        
        sock.close()
        
    except Exception as e:
        print(f"Erreur test fréquence: {e}")

def test_via_api():
    """Test via l'API locale"""
    import requests
    
    print("\n" + "=" * 50)
    print("TEST VIA API LOCALE")
    print("=" * 50)
    
    try:
        # Test Power OFF
        print("Test Power OFF via API...")
        response = requests.post("http://localhost:8000/api/command", 
                               json={"power_on": False})
        print(f"Réponse API: {response.json()}")
        
        print("Observez l'IC-R8600 pendant 5 secondes...")
        time.sleep(5)
        
        # Test Power ON
        print("\nTest Power ON via API...")
        response = requests.post("http://localhost:8000/api/command", 
                               json={"power_on": True})
        print(f"Réponse API: {response.json()}")
        
        print("Observez l'IC-R8600 pendant 5 secondes...")
        time.sleep(5)
        
        # Test fréquence
        print("\nTest changement fréquence via API...")
        response = requests.post("http://localhost:8000/api/command", 
                               json={"frequency": 145500000})
        print(f"Réponse API: {response.json()}")
        
        print("Observez l'affichage de fréquence pendant 5 secondes...")
        time.sleep(5)
        
    except Exception as e:
        print(f"Erreur test API: {e}")

if __name__ == "__main__":
    print("DIAGNOSTIC COMPLET UDP IC-R8600")
    print("Observez attentivement l'IC-R8600 pendant les tests !")
    print("Assurez-vous que Network Control est activé !")
    print()
    
    # Test 1: Commandes UDP directes
    test_udp_power_commands()
    
    input("\nAppuyez sur Entrée pour tester les commandes de fréquence...")
    test_frequency_commands()
    
    input("\nAppuyez sur Entrée pour tester via l'API...")
    test_via_api()
    
    print("\n" + "=" * 50)
    print("DIAGNOSTIC TERMINÉ")
    print("=" * 50)
    print("Si les commandes UDP n'ont eu aucun effet :")
    print("1. Vérifiez que Network Control est ON sur l'IC-R8600")
    print("2. Redémarrez l'IC-R8600 après avoir activé Network Control")
    print("3. Vérifiez que l'IP ************** est correcte")
    print("4. Testez avec le logiciel officiel RS-R8600")
