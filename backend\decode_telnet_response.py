#!/usr/bin/env python3
"""
Décodage de la réponse Telnet CI-V de l'IC-R8600
"""

def decode_hex_response():
    """Décode la réponse hexadécimale reçue"""
    
    # Réponse reçue du port 23 avec commande CI-V
    hex_response = "1B5B306D0D0A57656C636F6D6520746F2049432D5238363030210D0A0D0A1B371B5B3F323356C1B5B3530313B353031481B5B366E1B381B5B3F3235680D1B5B306D1B5B316D49432D52383630302023201B5B306D1B5B5B4A0D1B5B313143"
    
    print("🔍 DÉCODAGE DE LA RÉPONSE TELNET CI-V")
    print("=" * 50)
    
    # Convertir en bytes
    response_bytes = bytes.fromhex(hex_response)
    
    print(f"📊 Longueur: {len(response_bytes)} bytes")
    print(f"📄 Hex: {hex_response}")
    print()
    
    # Décoder en ASCII
    try:
        ascii_text = response_bytes.decode('ascii', errors='ignore')
        print("📝 Texte ASCII décodé:")
        print(repr(ascii_text))
        print()
        print("📝 Texte lisible:")
        print(ascii_text)
        print()
    except Exception as e:
        print(f"❌ Erreur décodage ASCII: {e}")
    
    # Chercher des patterns CI-V
    print("🔍 Recherche de patterns CI-V...")
    
    # Pattern CI-V standard: FE FE XX XX ... FD
    fe_positions = []
    for i in range(len(response_bytes) - 1):
        if response_bytes[i] == 0xFE and response_bytes[i+1] == 0xFE:
            fe_positions.append(i)
    
    if fe_positions:
        print(f"✅ Patterns FE FE trouvés aux positions: {fe_positions}")
        
        for pos in fe_positions:
            # Chercher le FD de fin
            for end_pos in range(pos + 2, min(pos + 20, len(response_bytes))):
                if response_bytes[end_pos] == 0xFD:
                    ci_v_data = response_bytes[pos:end_pos+1]
                    print(f"🎯 Commande CI-V trouvée: {ci_v_data.hex().upper()}")
                    
                    # Décoder la commande CI-V
                    if len(ci_v_data) >= 6:
                        src_addr = ci_v_data[2]
                        dst_addr = ci_v_data[3]
                        command = ci_v_data[4:-1]
                        
                        print(f"   📍 Adresse source: 0x{src_addr:02X}")
                        print(f"   📍 Adresse destination: 0x{dst_addr:02X}")
                        print(f"   📋 Commande: {command.hex().upper()}")
                    break
    else:
        print("❌ Aucun pattern CI-V FE FE trouvé")
    
    # Analyser si c'est juste du texte Telnet
    if b"Welcome to IC-R8600" in response_bytes:
        print("✅ Message de bienvenue IC-R8600 détecté")
    
    if b"IC-R8600 #" in response_bytes:
        print("✅ Prompt de commande IC-R8600 détecté")

def test_telnet_civ_direct():
    """Test direct de CI-V via Telnet"""
    import socket
    import time
    
    print("\n🧪 TEST CI-V DIRECT VIA TELNET")
    print("=" * 50)
    
    host = "**************"
    port = 23
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        sock.connect((host, port))
        
        # Lire le message de bienvenue
        welcome = sock.recv(1024)
        print("📥 Message de bienvenue reçu")
        
        # Envoyer commande CI-V directement
        ci_v_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
        print(f"📤 Envoi CI-V: {ci_v_cmd.hex().upper()}")
        
        sock.send(ci_v_cmd)
        time.sleep(1)
        
        # Lire la réponse
        response = sock.recv(1024)
        print(f"📥 Réponse: {response.hex().upper()}")
        
        # Décoder la réponse
        if len(response) >= 6:
            # Chercher une réponse CI-V dans la réponse
            for i in range(len(response) - 5):
                if (response[i] == 0xFE and response[i+1] == 0xFE and 
                    response[i+4] == 0xE0):  # Réponse vers nous
                    
                    # Chercher le FD de fin
                    for j in range(i+5, min(i+20, len(response))):
                        if response[j] == 0xFD:
                            ci_v_response = response[i:j+1]
                            print(f"🎯 Réponse CI-V: {ci_v_response.hex().upper()}")
                            
                            # Décoder la fréquence si c'est une réponse de fréquence
                            if len(ci_v_response) >= 11 and ci_v_response[4] == 0x03:
                                freq_data = ci_v_response[5:-1]
                                print(f"📻 Données fréquence: {freq_data.hex().upper()}")
                                
                                # Convertir en fréquence (format BCD ICOM)
                                try:
                                    freq_hz = 0
                                    for byte in reversed(freq_data):
                                        freq_hz = freq_hz * 100 + (byte >> 4) * 10 + (byte & 0x0F)
                                    freq_mhz = freq_hz / 1000000
                                    print(f"🎯 Fréquence: {freq_mhz:.6f} MHz")
                                except:
                                    print("⚠️ Erreur décodage fréquence")
                            
                            sock.close()
                            return True
        
        print("⚠️ Pas de réponse CI-V valide trouvée")
        sock.close()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
    
    return False

if __name__ == "__main__":
    # Décoder la réponse précédente
    decode_hex_response()
    
    # Tester CI-V direct
    civ_success = test_telnet_civ_direct()
    
    print("\n" + "=" * 50)
    print("📊 CONCLUSION")
    print("=" * 50)
    
    if civ_success:
        print("🎉 CI-V fonctionne via Telnet (port 23) !")
        print("💡 Solution: Adapter l'application pour utiliser TCP port 23")
        print("🔧 L'IC-R8600 utilise Telnet comme interface CI-V")
    else:
        print("⚠️ CI-V via Telnet nécessite plus d'investigation")
        print("💡 Le port 23 est fonctionnel mais protocole à clarifier")
    
    print("=" * 50)
