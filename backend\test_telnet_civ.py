#!/usr/bin/env python3
"""
Test de connexion Telnet vers IC-R8600 pour commandes CI-V
Le port 23 est ouvert, testons cette voie de communication
"""

import socket
import time
import sys

def test_telnet_connection():
    """Test de connexion Telnet vers IC-R8600"""
    host = "**************"
    port = 23
    
    print(f"🔌 Test de connexion Telnet vers {host}:{port}")
    
    try:
        # Création du socket TCP pour Telnet
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10.0)
        
        print("📡 Connexion Telnet...")
        sock.connect((host, port))
        print("✅ Connexion Telnet établie !")
        
        # Lire le message de bienvenue
        try:
            welcome = sock.recv(1024)
            if welcome:
                print(f"📥 Message de bienvenue: {welcome.decode('ascii', errors='ignore')}")
        except:
            pass
        
        # Test d'envoi de commande CI-V en hexadécimal
        print("📤 Test d'envoi de commande CI-V...")
        
        # Commande CI-V en format hexadécimal pour Telnet
        ci_v_hex = "FEFE94E003FD"  # Lecture fréquence
        command = ci_v_hex + "\r\n"
        
        sock.send(command.encode())
        print(f"Envoyé Telnet: {command.strip()}")
        
        # Attendre la réponse
        print("📥 Attente de la réponse...")
        time.sleep(1)
        
        response = sock.recv(1024)
        
        if response:
            response_str = response.decode('ascii', errors='ignore')
            print(f"✅ Réponse reçue: {response_str}")
            
            # Vérifier si c'est une réponse CI-V
            if "FEFE" in response_str:
                print("✅ Réponse CI-V détectée via Telnet !")
                return True
            else:
                print("⚠️ Réponse non-CI-V")
        else:
            print("❌ Aucune réponse reçue")
        
        # Test avec d'autres commandes Telnet standard
        print("\n📤 Test de commandes Telnet standard...")
        
        test_commands = [
            "help\r\n",
            "?\r\n", 
            "status\r\n",
            "freq\r\n"
        ]
        
        for cmd in test_commands:
            print(f"Envoi: {cmd.strip()}")
            sock.send(cmd.encode())
            time.sleep(0.5)
            
            try:
                resp = sock.recv(1024)
                if resp:
                    resp_str = resp.decode('ascii', errors='ignore')
                    print(f"Réponse: {resp_str.strip()}")
            except:
                print("Pas de réponse")
        
        sock.close()
        print("🔌 Connexion Telnet fermée")
        return True
        
    except ConnectionRefusedError:
        print("❌ Connexion Telnet refusée")
        return False
        
    except socket.timeout:
        print("❌ Timeout Telnet")
        return False
        
    except Exception as e:
        print(f"❌ Erreur Telnet: {e}")
        return False

def test_udp_with_different_addresses():
    """Test UDP avec différentes adresses CI-V"""
    host = "**************"
    port = 50001
    
    print(f"\n🧪 Test UDP avec différentes adresses CI-V...")
    
    # Différentes adresses à tester
    addresses = [
        (0x94, "IC-R8600 (0x94)"),
        (0x96, "IC-R8600 alternatif (0x96)"),
        (0x00, "Broadcast (0x00)")
    ]
    
    for addr, desc in addresses:
        print(f"\n🔌 Test avec {desc}")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(3.0)
            
            # Commande CI-V avec adresse spécifique
            ci_v_command = bytes([0xFE, 0xFE, addr, 0xE0, 0x03, 0xFD])
            
            sock.sendto(ci_v_command, (host, port))
            print(f"Envoyé UDP: {ci_v_command.hex().upper()}")
            
            try:
                response, addr_resp = sock.recvfrom(1024)
                if response:
                    print(f"✅ Réponse reçue: {response.hex().upper()}")
                    sock.close()
                    return True
            except socket.timeout:
                print("⏱️ Timeout")
            
            sock.close()
            
        except Exception as e:
            print(f"❌ Erreur: {e}")
    
    return False

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 TEST TELNET + UDP IC-R8600")
    print("=" * 60)
    
    # Test 1: Telnet (port 23 ouvert)
    telnet_ok = test_telnet_connection()
    
    # Test 2: UDP avec différentes adresses
    udp_ok = test_udp_with_different_addresses()
    
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    print(f"🔌 Telnet (port 23): {'✅ OK' if telnet_ok else '❌ ÉCHEC'}")
    print(f"📡 UDP CI-V: {'✅ OK' if udp_ok else '❌ ÉCHEC'}")
    
    if telnet_ok:
        print("\n✅ Telnet fonctionne - Interface de commande disponible")
    
    if udp_ok:
        print("\n✅ UDP CI-V fonctionne - Contrôle direct possible")
    
    if not telnet_ok and not udp_ok:
        print("\n⚠️ Aucun protocole ne fonctionne")
        print("🔧 Vérifiez la configuration Network Control")
    
    print("=" * 60)
