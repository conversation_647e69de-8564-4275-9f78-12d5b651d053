#!/usr/bin/env python3
"""
Test direct de l'API pour vérifier les boutons
"""

import requests
import json

def test_power_command():
    """Test de la commande power via l'API"""
    url = "http://localhost:8000/api/command"
    
    print("Test commande Power OFF...")
    
    # Test Power OFF
    data = {"power_on": False}
    
    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            print("✅ Commande Power OFF envoyée avec succès !")
        else:
            print(f"❌ Erreur: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_options():
    """Test de la requête OPTIONS"""
    url = "http://localhost:8000/api/command"
    
    print("\nTest requête OPTIONS...")
    
    try:
        response = requests.options(url)
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ OPTIONS fonctionne !")
        else:
            print(f"❌ Erreur OPTIONS: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erreur OPTIONS: {e}")

if __name__ == "__main__":
    test_options()
    test_power_command()
