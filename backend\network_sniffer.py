#!/usr/bin/env python3
"""
Analyseur de trafic réseau pour reverse engineering du protocole ICOM
Capture et analyse le trafic entre PC et IC-R8600
"""

import socket
import threading
import time
import struct
import binascii
from datetime import datetime

class NetworkSniffer:
    """Analyseur de trafic réseau ICOM"""
    
    def __init__(self, target_ip="**************"):
        self.target_ip = target_ip
        self.running = False
        self.captured_packets = []
        
        # Ports à surveiller
        self.ports_to_monitor = [23, 50001, 50002, 50003, 80, 443, 8080]
        
        # Sockets d'écoute
        self.listen_sockets = []

    def start_monitoring(self):
        """Démarre la surveillance réseau"""
        print(f"Démarrage surveillance réseau vers {self.target_ip}")
        print("Ports surveillés:", self.ports_to_monitor)
        
        self.running = True
        
        # Créer des threads d'écoute pour chaque port
        for port in self.ports_to_monitor:
            # UDP
            thread_udp = threading.Thread(
                target=self._monitor_udp_port, 
                args=(port,), 
                daemon=True
            )
            thread_udp.start()
            
            # TCP
            thread_tcp = threading.Thread(
                target=self._monitor_tcp_port, 
                args=(port,), 
                daemon=True
            )
            thread_tcp.start()
        
        print("Surveillance active. Utilisez maintenant le logiciel RS-R8600...")
        print("Appuyez sur Ctrl+C pour arrêter")

    def _monitor_udp_port(self, port):
        """Surveille un port UDP"""
        try:
            # Socket d'écoute UDP
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            sock.bind(('', port))
            sock.settimeout(1.0)
            
            print(f"Écoute UDP sur port {port}")
            
            while self.running:
                try:
                    data, addr = sock.recvfrom(4096)
                    if addr[0] == self.target_ip or addr[0] == '127.0.0.1':
                        self._log_packet("UDP", port, addr, data, "RECEIVED")
                except socket.timeout:
                    continue
                except Exception as e:
                    if self.running:
                        print(f"Erreur UDP port {port}: {e}")
                    break
            
            sock.close()
            
        except Exception as e:
            print(f"Impossible d'écouter UDP port {port}: {e}")

    def _monitor_tcp_port(self, port):
        """Surveille un port TCP"""
        try:
            # Socket d'écoute TCP
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            sock.bind(('', port))
            sock.listen(5)
            sock.settimeout(1.0)
            
            print(f"Écoute TCP sur port {port}")
            
            while self.running:
                try:
                    client_sock, addr = sock.accept()
                    if addr[0] == self.target_ip or addr[0] == '127.0.0.1':
                        # Traiter la connexion TCP
                        thread = threading.Thread(
                            target=self._handle_tcp_connection,
                            args=(client_sock, addr, port),
                            daemon=True
                        )
                        thread.start()
                except socket.timeout:
                    continue
                except Exception as e:
                    if self.running:
                        print(f"Erreur TCP port {port}: {e}")
                    break
            
            sock.close()
            
        except Exception as e:
            print(f"Impossible d'écouter TCP port {port}: {e}")

    def _handle_tcp_connection(self, client_sock, addr, port):
        """Gère une connexion TCP"""
        try:
            client_sock.settimeout(1.0)
            
            while self.running:
                try:
                    data = client_sock.recv(4096)
                    if not data:
                        break
                    
                    self._log_packet("TCP", port, addr, data, "RECEIVED")
                    
                except socket.timeout:
                    continue
                except Exception as e:
                    break
            
            client_sock.close()
            
        except Exception as e:
            print(f"Erreur connexion TCP {addr}: {e}")

    def _log_packet(self, protocol, port, addr, data, direction):
        """Enregistre un paquet capturé"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        
        packet_info = {
            'timestamp': timestamp,
            'protocol': protocol,
            'port': port,
            'address': addr,
            'direction': direction,
            'data': data,
            'hex': data.hex().upper(),
            'size': len(data)
        }
        
        self.captured_packets.append(packet_info)
        
        # Affichage en temps réel
        print(f"\n[{timestamp}] {protocol}:{port} {direction} from {addr[0]}")
        print(f"Size: {len(data)} bytes")
        print(f"Hex:  {data.hex().upper()}")
        
        # Tentative de décodage
        self._analyze_packet(data)

    def _analyze_packet(self, data):
        """Analyse un paquet pour identifier des patterns"""
        if len(data) == 0:
            return
        
        # Recherche de patterns connus
        patterns = {
            b'\xFE\xFE': "CI-V Command",
            b'\x70\x00\x00\x00': "ICOM Magic Header",
            b'\x00\x00\x00\x00': "Possible Magic",
            b'ICOM': "ICOM String",
            b'RS-BA1': "RS-BA1 String"
        }
        
        found_patterns = []
        for pattern, description in patterns.items():
            if pattern in data:
                pos = data.find(pattern)
                found_patterns.append(f"{description} at offset {pos}")
        
        if found_patterns:
            print(f"Patterns: {', '.join(found_patterns)}")
        
        # Analyse ASCII
        try:
            ascii_data = data.decode('ascii', errors='ignore')
            if ascii_data.strip():
                print(f"ASCII: {repr(ascii_data)}")
        except:
            pass

    def stop_monitoring(self):
        """Arrête la surveillance"""
        print("\nArrêt de la surveillance...")
        self.running = False

    def save_capture(self, filename="icom_capture.txt"):
        """Sauvegarde la capture"""
        with open(filename, 'w') as f:
            f.write("CAPTURE TRAFIC RÉSEAU ICOM IC-R8600\n")
            f.write("=" * 50 + "\n\n")
            
            for packet in self.captured_packets:
                f.write(f"[{packet['timestamp']}] {packet['protocol']}:{packet['port']} "
                       f"{packet['direction']} from {packet['address'][0]}\n")
                f.write(f"Size: {packet['size']} bytes\n")
                f.write(f"Hex: {packet['hex']}\n")
                
                # Formatage hex lisible
                hex_data = packet['hex']
                formatted_hex = ' '.join(hex_data[i:i+2] for i in range(0, len(hex_data), 2))
                f.write(f"Formatted: {formatted_hex}\n")
                f.write("-" * 50 + "\n\n")
        
        print(f"Capture sauvegardée dans {filename}")

def main():
    """Fonction principale"""
    print("ANALYSEUR DE TRAFIC RÉSEAU ICOM IC-R8600")
    print("=" * 50)
    print("INSTRUCTIONS:")
    print("1. Lancez ce script")
    print("2. Démarrez le logiciel officiel RS-R8600")
    print("3. Connectez-vous à l'IC-R8600 via RS-R8600")
    print("4. Effectuez des commandes (Power ON/OFF, changement fréquence, etc.)")
    print("5. Observez la capture du trafic réseau")
    print("6. Appuyez sur Ctrl+C pour arrêter et sauvegarder")
    print("=" * 50)
    
    sniffer = NetworkSniffer("**************")
    
    try:
        sniffer.start_monitoring()
        
        # Boucle principale
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        sniffer.stop_monitoring()
        sniffer.save_capture()
        
        print("\nAnalyse terminée!")
        print(f"Paquets capturés: {len(sniffer.captured_packets)}")
        
        if sniffer.captured_packets:
            print("\nRésumé des paquets:")
            protocols = {}
            for packet in sniffer.captured_packets:
                key = f"{packet['protocol']}:{packet['port']}"
                protocols[key] = protocols.get(key, 0) + 1
            
            for proto, count in protocols.items():
                print(f"  {proto}: {count} paquets")

if __name__ == "__main__":
    main()
