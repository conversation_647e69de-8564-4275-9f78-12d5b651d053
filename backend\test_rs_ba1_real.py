#!/usr/bin/env python3
"""
Test du VRAI protocole ICOM RS-BA1 pour IC-R8600
Basé sur le reverse engineering de kappanhang et NetworkIcom
"""

import time
import logging
from icom_rs_ba1_real import ICOM_RSBA1_Real, RSBA1Config

# Configuration du logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_real_rs_ba1_protocol():
    """Test du VRAI protocole RS-BA1"""
    print("=" * 70)
    print("TEST DU VRAI PROTOCOLE ICOM RS-BA1 POUR IC-R8600")
    print("=" * 70)
    print("PROTOCOLE BASÉ SUR LE REVERSE ENGINEERING DE:")
    print("- kappan<PERSON> (nonoo/kappanhang)")
    print("- NetworkIcom (microenh/NetworkIcom)")
    print("- wfview (eliggett/wfview)")
    print("=" * 70)
    print("CONFIGURATION REQUISE:")
    print("1. IC-R8600 allumé")
    print("2. Network Control: ON")
    print("3. IP: **************")
    print("4. Ports: 50001, 50002, 50003")
    print("5. Username: beer")
    print("6. Password: beerbeer")
    print("=" * 70)
    
    # Configuration
    config = RSBA1Config(
        host="**************",
        control_port=50001,
        serial_port=50002,
        audio_port=50003,
        username="beer",
        password="beerbeer"
    )
    
    # Création du handler
    icom = ICOM_RSBA1_Real(config)
    
    try:
        # Connexion
        print("\n1. Test de connexion au VRAI protocole RS-BA1...")
        print("   Handshake Are You There / I Am Here...")
        print("   Handshake Are You Ready / I Am Ready...")
        print("   Login avec credentials...")
        print("   Token exchange...")
        print("   Ouverture canal série...")
        print("   Activation CI-V...")
        
        if icom.connect():
            print("✅ Connexion RS-BA1 RÉELLE réussie !")
            
            # Afficher les détails de la connexion
            status = icom.get_status()
            print(f"   My ID: {status['my_id']}")
            print(f"   Radio ID: {status['radio_id']}")
            print(f"   Token: {status['token']}")
            print(f"   Logged in: {status['logged_in']}")
        else:
            print("❌ Échec de connexion RS-BA1 RÉELLE")
            return
        
        # Test Power OFF
        print("\n2. Test Power OFF...")
        print("   OBSERVEZ L'IC-R8600 - IL DOIT S'ÉTEINDRE !")
        if icom.power_off():
            print("✅ Commande Power OFF envoyée via RS-BA1 RÉEL")
        else:
            print("❌ Échec commande Power OFF")
        
        # Attente pour observation
        print("   Attente 8 secondes pour observer l'effet...")
        time.sleep(8)
        
        # Test Power ON
        print("\n3. Test Power ON...")
        print("   OBSERVEZ L'IC-R8600 - IL DOIT S'ALLUMER !")
        if icom.power_on():
            print("✅ Commande Power ON envoyée via RS-BA1 RÉEL")
        else:
            print("❌ Échec commande Power ON")
        
        # Attente pour observation
        print("   Attente 8 secondes pour observer l'effet...")
        time.sleep(8)
        
        # Test fréquence
        print("\n4. Test changement fréquence à 145.500 MHz...")
        print("   OBSERVEZ L'AFFICHAGE DE FRÉQUENCE SUR L'IC-R8600 !")
        if icom.set_frequency(145500000):
            print("✅ Commande fréquence envoyée via RS-BA1 RÉEL")
        else:
            print("❌ Échec commande fréquence")
        
        # Attente pour observation
        print("   Attente 5 secondes pour observer le changement...")
        time.sleep(5)
        
        # Test mode
        print("\n5. Test changement mode à FM...")
        print("   OBSERVEZ L'AFFICHAGE DE MODE SUR L'IC-R8600 !")
        if icom.set_mode("FM"):
            print("✅ Commande mode envoyée via RS-BA1 RÉEL")
        else:
            print("❌ Échec commande mode")
        
        # Attente pour observation
        print("   Attente 5 secondes pour observer le changement...")
        time.sleep(5)
        
        # Test lecture de fréquence
        print("\n6. Test lecture de fréquence...")
        freq = icom.get_frequency()
        print(f"   Fréquence lue: {freq:.6f} MHz")
        
        # Test lecture de mode
        print("\n7. Test lecture de mode...")
        mode = icom.get_mode()
        print(f"   Mode lu: {mode}")
        
        # Affichage du statut final
        print("\n8. Statut final de la connexion:")
        status = icom.get_status()
        for key, value in status.items():
            print(f"   {key}: {value}")
        
        # Déconnexion
        print("\n9. Déconnexion...")
        icom.disconnect()
        print("✅ Déconnexion RS-BA1 RÉELLE effectuée")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Nettoyage
        if icom.connected:
            icom.disconnect()

def test_multiple_frequencies():
    """Test de plusieurs fréquences pour vérifier la stabilité"""
    print("\n" + "=" * 70)
    print("TEST FRÉQUENCES MULTIPLES")
    print("=" * 70)
    
    config = RSBA1Config(host="**************")
    icom = ICOM_RSBA1_Real(config)
    
    try:
        if not icom.connect():
            print("❌ Échec de connexion pour test multiple")
            return
        
        print("✅ Connexion établie pour test multiple")
        
        # Test de plusieurs fréquences
        frequencies = [
            (145500000, "145.500 MHz - Répéteur local"),
            (146000000, "146.000 MHz - Simplex"),
            (144000000, "144.000 MHz - CW"),
            (145750000, "145.750 MHz - Packet"),
            (433500000, "433.500 MHz - UHF"),
            (28500000, "28.500 MHz - 10m"),
        ]
        
        for i, (freq, description) in enumerate(frequencies):
            print(f"\nTest {i+1}/{len(frequencies)}: {description}")
            print("   OBSERVEZ LE CHANGEMENT SUR L'IC-R8600 !")
            
            if icom.set_frequency(freq):
                print("   ✅ Commande envoyée")
                
                # Vérifier la lecture
                read_freq = icom.get_frequency()
                print(f"   Fréquence lue: {read_freq:.6f} MHz")
            else:
                print("   ❌ Échec")
            
            time.sleep(3)
        
        icom.disconnect()
        print("\n✅ Test fréquences multiples terminé")
        
    except Exception as e:
        print(f"❌ Erreur test multiple: {e}")
    finally:
        if icom.connected:
            icom.disconnect()

def test_modes():
    """Test de tous les modes"""
    print("\n" + "=" * 70)
    print("TEST MODES MULTIPLES")
    print("=" * 70)
    
    config = RSBA1Config(host="**************")
    icom = ICOM_RSBA1_Real(config)
    
    try:
        if not icom.connect():
            print("❌ Échec de connexion pour test modes")
            return
        
        print("✅ Connexion établie pour test modes")
        
        # Test de tous les modes
        modes = ["FM", "AM", "USB", "LSB", "CW", "WFM"]
        
        for i, mode in enumerate(modes):
            print(f"\nTest mode {i+1}/{len(modes)}: {mode}")
            print("   OBSERVEZ LE CHANGEMENT SUR L'IC-R8600 !")
            
            if icom.set_mode(mode):
                print("   ✅ Commande envoyée")
                
                # Vérifier la lecture
                read_mode = icom.get_mode()
                print(f"   Mode lu: {read_mode}")
            else:
                print("   ❌ Échec")
            
            time.sleep(2)
        
        icom.disconnect()
        print("\n✅ Test modes terminé")
        
    except Exception as e:
        print(f"❌ Erreur test modes: {e}")
    finally:
        if icom.connected:
            icom.disconnect()

def test_protocol_stability():
    """Test de stabilité du protocole"""
    print("\n" + "=" * 70)
    print("TEST STABILITÉ PROTOCOLE")
    print("=" * 70)
    
    config = RSBA1Config(host="**************")
    
    for round_num in range(3):
        print(f"\nRound {round_num + 1}/3 de test de stabilité...")
        
        icom = ICOM_RSBA1_Real(config)
        
        try:
            if icom.connect():
                print(f"  ✅ Connexion {round_num + 1} réussie")
                
                # Test rapide
                icom.set_frequency(145500000 + round_num * 100000)
                time.sleep(2)
                
                icom.disconnect()
                print(f"  ✅ Déconnexion {round_num + 1} réussie")
            else:
                print(f"  ❌ Échec connexion {round_num + 1}")
                
        except Exception as e:
            print(f"  ❌ Erreur round {round_num + 1}: {e}")
        finally:
            if icom.connected:
                icom.disconnect()
        
        time.sleep(2)  # Pause entre les rounds
    
    print("\n✅ Test stabilité terminé")

if __name__ == "__main__":
    print("DIAGNOSTIC COMPLET DU VRAI PROTOCOLE RS-BA1")
    print("Observez attentivement l'IC-R8600 pendant les tests !")
    print("Ce protocole est basé sur le reverse engineering de projets open source")
    
    # Test principal
    test_real_rs_ba1_protocol()
    
    # Tests supplémentaires
    choice = input("\nVoulez-vous tester les fréquences multiples ? (o/n): ")
    if choice.lower() == 'o':
        test_multiple_frequencies()
    
    choice = input("\nVoulez-vous tester tous les modes ? (o/n): ")
    if choice.lower() == 'o':
        test_modes()
    
    choice = input("\nVoulez-vous tester la stabilité du protocole ? (o/n): ")
    if choice.lower() == 'o':
        test_protocol_stability()
    
    print("\n" + "=" * 70)
    print("DIAGNOSTIC RS-BA1 RÉEL TERMINÉ")
    print("=" * 70)
    print("RÉSULTATS ATTENDUS :")
    print("- Si les commandes ont un effet sur l'IC-R8600 → SUCCÈS TOTAL !")
    print("- Si connexion OK mais pas d'effet → Vérifier Network Control")
    print("- Si échec de connexion → Problème réseau ou credentials")
    print("- Vérifiez les logs DEBUG pour l'analyse détaillée")
    print("\nCe protocole est basé sur le reverse engineering de:")
    print("- kappanhang par nonoo (Go)")
    print("- NetworkIcom par microenh (Swift)")
    print("- wfview par eliggett (C++)")
    print("Il devrait fonctionner même avec la version firmware 1.33 !")
