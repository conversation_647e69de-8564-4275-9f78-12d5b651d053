#!/usr/bin/env python3
"""
Contrôle IC-R8600 via UDP - Utilise les ports configurés
Control Port: 50002, Audio Port: 50003, Serial Port: 50004
"""

import socket
import time
import logging
import threading
from typing import Optional, Dict, Any, List

class ICR8600UDPControl:
    """Contrôle IC-R8600 via UDP sur les ports configurés"""
    
    def __init__(self, host="**************"):
        self.host = host
        self.logger = logging.getLogger(__name__)
        
        # Ports configurés sur l'IC-R8600
        self.CONTROL_PORT = 50002  # Port de contrôle
        self.AUDIO_PORT = 50003    # Port audio
        self.SERIAL_PORT = 50004   # Port série
        
        # Socket UDP pour contrôle
        self.control_socket = None
        self.connected = False
        
        # Adresses CI-V
        self.ICOM_ADDRESS = 0x94
        self.CONTROLLER_ADDRESS = 0xE0
        
        # État du récepteur
        self.current_frequency = 145.5
        self.current_mode = "FM"
        self.power_state = True
        
        # Modes supportés
        self.modes = {
            'LSB': 0x00, 'USB': 0x01, 'AM': 0x02, 'CW': 0x03,
            'FM': 0x05, 'WFM': 0x06, 'CWR': 0x07, 'RTTY': 0x08
        }

    def connect(self) -> bool:
        """Connexion UDP pour contrôle IC-R8600"""
        try:
            self.logger.info(f"Connexion UDP IC-R8600 vers {self.host}:{self.CONTROL_PORT}")
            
            # Créer socket UDP
            self.control_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.control_socket.settimeout(5.0)
            
            # Test de connectivité
            if not self._test_udp_connectivity():
                return False
            
            self.connected = True
            self.logger.info("Contrôle UDP IC-R8600 activé")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur connexion UDP: {e}")
            self.disconnect()
            return False

    def _test_udp_connectivity(self) -> bool:
        """Test de connectivité UDP"""
        try:
            # Test avec commande CI-V simple (Status)
            test_cmd = self._build_ci_v_command([0x19, 0x00])
            
            self.control_socket.sendto(test_cmd, (self.host, self.CONTROL_PORT))
            self.logger.info(f"Test UDP envoyé sur port {self.CONTROL_PORT}")
            
            # Essayer de recevoir une réponse
            try:
                response, addr = self.control_socket.recvfrom(1024)
                self.logger.info(f"Réponse UDP reçue: {response.hex().upper()}")
                return True
            except socket.timeout:
                self.logger.info("Pas de réponse UDP (normal pour certaines commandes)")
                return True  # Considérer comme succès même sans réponse
                
        except Exception as e:
            self.logger.error(f"Erreur test UDP: {e}")
            return False

    def _build_ci_v_command(self, command: List[int], data: List[int] = None) -> bytes:
        """Construit une commande CI-V complète"""
        cmd = [0xFE, 0xFE, self.ICOM_ADDRESS, self.CONTROLLER_ADDRESS]
        cmd.extend(command)
        if data:
            cmd.extend(data)
        cmd.append(0xFD)
        return bytes(cmd)

    def _send_udp_command(self, ci_v_command: List[int]) -> bool:
        """Envoie une commande CI-V via UDP"""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                if not self.control_socket:
                    if not self.connect():
                        continue
                
                # Construire la commande CI-V
                ci_v_bytes = self._build_ci_v_command(ci_v_command)
                
                # Envoyer via UDP
                self.control_socket.sendto(ci_v_bytes, (self.host, self.CONTROL_PORT))
                self.logger.info(f"UDP envoyé (tentative {attempt+1}): {ci_v_bytes.hex().upper()}")
                
                # Attendre un peu pour traitement
                time.sleep(0.5)
                
                return True
                
            except Exception as e:
                self.logger.error(f"Erreur UDP (tentative {attempt+1}): {e}")
                
                if attempt < max_retries - 1:
                    time.sleep(1.0)
                    continue
                else:
                    return False
        
        return False

    def disconnect(self):
        """Déconnexion"""
        self.logger.info("Déconnexion UDP IC-R8600")
        
        if self.control_socket:
            try:
                self.control_socket.close()
            except:
                pass
            self.control_socket = None
        
        self.connected = False

    # Méthodes de contrôle IC-R8600
    def power_on(self) -> bool:
        """Allume l'IC-R8600"""
        success = self._send_udp_command([0x18, 0x01])
        if success:
            self.power_state = True
            self.logger.info("Power ON envoyé via UDP")
        return success

    def power_off(self) -> bool:
        """Éteint l'IC-R8600"""
        success = self._send_udp_command([0x18, 0x00])
        if success:
            self.power_state = False
            self.logger.info("Power OFF envoyé via UDP")
        return success

    def set_frequency(self, freq_hz: int) -> bool:
        """Définit la fréquence"""
        try:
            # Conversion en BCD
            freq_str = f"{freq_hz:010d}"
            bcd_bytes = []
            for i in range(4, -1, -1):
                digit1 = int(freq_str[i*2])
                digit2 = int(freq_str[i*2 + 1])
                bcd_bytes.append((digit1 << 4) | digit2)

            success = self._send_udp_command([0x05] + bcd_bytes)
            if success:
                self.current_frequency = freq_hz / 1000000.0
                self.logger.info(f"Fréquence {self.current_frequency:.6f} MHz définie via UDP")
            return success
            
        except Exception as e:
            self.logger.error(f"Erreur set_frequency: {e}")
            return False

    def set_mode(self, mode: str) -> bool:
        """Définit le mode"""
        if mode not in self.modes:
            return False
        
        success = self._send_udp_command([0x06, self.modes[mode]])
        if success:
            self.current_mode = mode
            self.logger.info(f"Mode {mode} défini via UDP")
        return success

    def get_frequency(self) -> Optional[int]:
        """Lit la fréquence"""
        return int(self.current_frequency * 1000000)

    def get_mode(self) -> Optional[str]:
        """Lit le mode"""
        return self.current_mode

    def get_status(self) -> Dict[str, Any]:
        """Récupère l'état"""
        return {
            "connected": self.connected,
            "host": self.host,
            "connection_type": "IC-R8600_UDP_Control",
            "control_port": self.CONTROL_PORT,
            "audio_port": self.AUDIO_PORT,
            "serial_port": self.SERIAL_PORT,
            "power_state": self.power_state,
            "frequency": self.get_frequency(),
            "mode": self.current_mode,
            "firmware_compatible": "v1.33+"
        }

def test_udp_control():
    """Test du contrôle UDP"""
    print("🎯 TEST CONTRÔLE UDP IC-R8600")
    print("=" * 60)
    print("Utilise les ports configurés:")
    print("- Control Port: 50002")
    print("- Audio Port: 50003")
    print("- Serial Port: 50004")
    print("=" * 60)
    
    # Configuration logging
    logging.basicConfig(level=logging.INFO)
    
    controller = ICR8600UDPControl("**************")
    
    try:
        # Connexion
        if controller.connect():
            print("✅ Connexion UDP établie")
            
            # Test Power OFF
            print("\nTest Power OFF - OBSERVEZ L'IC-R8600 !")
            if controller.power_off():
                print("✅ Power OFF envoyé via UDP")
            else:
                print("❌ Échec Power OFF")
            
            time.sleep(5)
            
            # Test Power ON
            print("\nTest Power ON - OBSERVEZ L'IC-R8600 !")
            if controller.power_on():
                print("✅ Power ON envoyé via UDP")
            else:
                print("❌ Échec Power ON")
            
            time.sleep(5)
            
            # Test fréquence
            print("\nTest fréquence 145.500 MHz - OBSERVEZ L'AFFICHAGE !")
            if controller.set_frequency(145500000):
                print("✅ Fréquence envoyée via UDP")
            else:
                print("❌ Échec fréquence")
            
            time.sleep(3)
            
            # Test mode
            print("\nTest mode FM - OBSERVEZ L'AFFICHAGE !")
            if controller.set_mode("FM"):
                print("✅ Mode envoyé via UDP")
            else:
                print("❌ Échec mode")
            
            # Statut
            status = controller.get_status()
            print(f"\nStatut: {status}")
            
            controller.disconnect()
            print("\n✅ Test UDP terminé")
            
        else:
            print("❌ Échec connexion UDP")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
    finally:
        controller.disconnect()

if __name__ == "__main__":
    test_udp_control()
