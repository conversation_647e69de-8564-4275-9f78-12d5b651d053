<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Boutons IC-R8600</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .button {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .button:hover {
            background: #45a049;
        }
        .button.off {
            background: #f44336;
        }
        .status {
            background: #333;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .log {
            background: #222;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 Test Boutons IC-R8600</h1>
    
    <div class="status">
        <h3>Statut Actuel</h3>
        <div id="status">Chargement...</div>
    </div>
    
    <div>
        <h3>Contrôles</h3>
        <button class="button" id="powerBtn" onclick="togglePower()">Power OFF</button>
        <button class="button" onclick="testFrequency()">Test Fréquence (146.5 MHz)</button>
        <button class="button" onclick="testMode()">Test Mode (AM)</button>
        <button class="button" onclick="refreshStatus()">Actualiser Statut</button>
    </div>
    
    <div class="log">
        <h3>Log des Commandes</h3>
        <div id="log"></div>
    </div>

    <script>
        let currentStatus = { power_on: false };
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        async function sendCommand(data) {
            try {
                log(`📤 Envoi: ${JSON.stringify(data)}`);
                
                const response = await fetch('http://localhost:8000/api/command', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const result = await response.json();
                log(`✅ Réponse: ${JSON.stringify(result)}`);
                
                // Actualiser le statut après la commande
                setTimeout(refreshStatus, 500);
                
            } catch (error) {
                log(`❌ Erreur: ${error.message}`);
            }
        }
        
        async function refreshStatus() {
            try {
                const response = await fetch('http://localhost:8000/api/status');
                const status = await response.json();
                
                currentStatus = status;
                
                document.getElementById('status').innerHTML = `
                    <strong>Connecté:</strong> ${status.connected ? 'Oui' : 'Non'}<br>
                    <strong>Power:</strong> ${status.power_on ? 'ON' : 'OFF'}<br>
                    <strong>Fréquence:</strong> ${(status.frequency / 1000000).toFixed(3)} MHz<br>
                    <strong>Mode:</strong> ${status.mode}<br>
                    <strong>RSSI:</strong> ${status.rssi} dBm<br>
                    <strong>Type:</strong> ${status.connection_type}
                `;
                
                // Mettre à jour le bouton power
                const powerBtn = document.getElementById('powerBtn');
                powerBtn.textContent = status.power_on ? 'Power OFF' : 'Power ON';
                powerBtn.className = status.power_on ? 'button off' : 'button';
                
                log(`📊 Statut mis à jour`);
                
            } catch (error) {
                log(`❌ Erreur statut: ${error.message}`);
            }
        }
        
        function togglePower() {
            const newState = !currentStatus.power_on;
            sendCommand({ power_on: newState });
        }
        
        function testFrequency() {
            sendCommand({ frequency: 146500000 }); // 146.5 MHz
        }
        
        function testMode() {
            sendCommand({ mode: 'AM' });
        }
        
        // Actualiser le statut au chargement
        refreshStatus();
        
        // Actualiser automatiquement toutes les 5 secondes
        setInterval(refreshStatus, 5000);
        
        log('🚀 Interface de test chargée');
    </script>
</body>
</html>
