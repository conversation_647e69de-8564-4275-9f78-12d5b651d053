#!/usr/bin/env python3
"""
Test de connexion TCP directe vers IC-R8600
Vérifie la connectivité réseau et l'encapsulation CI-V
"""

import socket
import time
import sys

def test_port_scan():
    """Scan des ports ouverts sur l'IC-R8600"""
    host = "**************"
    common_ports = [23, 80, 443, 50001, 50002, 8080, 8000]

    print(f"🔍 Scan des ports sur {host}")
    open_ports = []

    for port in common_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2.0)
            result = sock.connect_ex((host, port))
            sock.close()

            if result == 0:
                print(f"✅ Port {port} ouvert")
                open_ports.append(port)
            else:
                print(f"❌ Port {port} fermé")
        except:
            print(f"❌ Port {port} inaccessible")

    return open_ports

def test_udp_connection(port=50001):
    """Test de connexion UDP vers IC-R8600"""
    host = "**************"

    print(f"🔌 Test de connexion UDP vers {host}:{port}")

    try:
        # Création du socket UDP
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(5.0)

        print("📡 Test de communication UDP...")

        # Test d'envoi de commande CI-V
        print("📤 Test d'envoi de commande CI-V...")

        # Commande CI-V : FE FE 94 E0 03 FD (lecture fréquence)
        ci_v_command = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])

        sock.sendto(ci_v_command, (host, port))
        print(f"Envoyé UDP: {ci_v_command.hex().upper()}")

        # Attendre la réponse
        print("📥 Attente de la réponse...")
        response, addr = sock.recvfrom(1024)

        if response:
            print(f"✅ Réponse reçue de {addr}: {response.hex().upper()}")
            print(f"📊 Longueur: {len(response)} bytes")

            # Analyser la réponse CI-V
            if len(response) >= 6:
                if response[0:2] == bytes([0xFE, 0xFE]) and response[-1] == 0xFD:
                    print("✅ Format CI-V valide détecté")
                    print("🎯 Le récepteur répond aux commandes CI-V via UDP !")

                    # Décoder l'adresse source
                    if len(response) >= 4:
                        src_addr = response[2]
                        dst_addr = response[3]
                        print(f"📍 Adresse source: 0x{src_addr:02X}")
                        print(f"📍 Adresse destination: 0x{dst_addr:02X}")
                else:
                    print("⚠️ Format de réponse non-CI-V")
            else:
                print("⚠️ Réponse trop courte")
        else:
            print("❌ Aucune réponse reçue")

        sock.close()
        print("🔌 Socket UDP fermé")
        return True

    except socket.timeout:
        print("❌ Timeout UDP - pas de réponse")
        print("🔧 Vérifiez:")
        print("   - Network Control: ON sur l'IC-R8600")
        print("   - Port UDP 50001 configuré")
        print("   - IC-R8600 redémarré après configuration")
        return False

    except Exception as e:
        print(f"❌ Erreur UDP: {e}")
        return False

def test_ping():
    """Test de ping réseau"""
    host = "**************"
    print(f"🏓 Test de ping vers {host}")
    
    import subprocess
    try:
        result = subprocess.run(['ping', '-n', '1', host], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ Ping réussi - IC-R8600 accessible sur le réseau")
            return True
        else:
            print("❌ Ping échoué - IC-R8600 non accessible")
            return False
    except Exception as e:
        print(f"❌ Erreur ping: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 TEST DE CONNEXION IC-R8600 via TCP/IP")
    print("=" * 60)

    # Test 1: Ping réseau
    ping_ok = test_ping()
    print()

    # Test 2: Scan des ports
    open_ports = test_port_scan()
    print()

    # Test 3: Connexion UDP sur les ports configurés
    udp_ok = False
    udp_ports = [50001, 50002, 50003]  # Ports configurés sur l'IC-R8600

    for port in udp_ports:
        print(f"🔌 Test UDP du port {port}...")
        if test_udp_connection(port):
            udp_ok = True
            break
        print()

    print()

    # Résumé
    print("=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    print(f"🏓 Ping réseau: {'✅ OK' if ping_ok else '❌ ÉCHEC'}")
    print(f"🔍 Ports TCP ouverts: {open_ports if open_ports else 'Aucun'}")
    print(f"🔌 Connexion CI-V UDP: {'✅ OK' if udp_ok else '❌ ÉCHEC'}")

    if ping_ok and udp_ok:
        print("\n🎉 IC-R8600 prêt pour le contrôle via réseau UDP !")
        print("🚀 Vous pouvez lancer l'application")
    elif ping_ok and not udp_ok:
        print("\n⚠️ IC-R8600 accessible mais Network Control désactivé")
        print("🔧 Activez Network Control dans les menus de l'IC-R8600:")
        print("   MENU → SET → Connectors → Network → Network Control: ON")
        print("   Puis redémarrez l'IC-R8600")
    else:
        print("\n❌ IC-R8600 non accessible sur le réseau")
        print("🔧 Vérifiez la configuration réseau")

    print("=" * 60)
