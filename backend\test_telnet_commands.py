#!/usr/bin/env python3
"""
Test des commandes Telnet spécifiques à l'IC-R8600
Exploration de l'interface de commande Telnet
"""

import socket
import time
import sys

def test_telnet_commands():
    """Test des commandes Telnet IC-R8600"""
    host = "**************"
    port = 23
    
    print(f"🔌 Connexion Telnet vers {host}:{port}")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10.0)
        
        sock.connect((host, port))
        print("✅ Connexion Telnet établie !")
        
        # Lire le message de bienvenue
        welcome = sock.recv(1024)
        print(f"📥 Bienvenue: {welcome.decode('ascii', errors='ignore').strip()}")
        
        # Commandes Telnet à tester pour l'IC-R8600
        test_commands = [
            # Commandes d'aide
            "help",
            "?",
            "commands",
            "list",
            
            # Commandes de statut
            "status",
            "info",
            "version",
            "model",
            
            # Commandes de fréquence
            "freq",
            "frequency",
            "get freq",
            "get frequency",
            "rf",
            
            # Commandes de mode
            "mode",
            "get mode",
            "modulation",
            
            # Commandes de volume
            "volume",
            "vol",
            "get volume",
            "af",
            
            # Commandes CI-V
            "civ",
            "ci-v",
            "remote",
            "control",
            
            # Commandes système
            "system",
            "config",
            "settings",
            "network",
            
            # Commandes spécifiques ICOM
            "icom",
            "ic-r8600",
            "receiver",
            "radio"
        ]
        
        print(f"\n📤 Test de {len(test_commands)} commandes...")
        
        successful_commands = []
        
        for i, cmd in enumerate(test_commands):
            print(f"\n[{i+1}/{len(test_commands)}] Test: {cmd}")
            
            # Envoyer la commande
            sock.send(f"{cmd}\r\n".encode())
            time.sleep(0.3)
            
            # Lire la réponse
            try:
                response = sock.recv(2048)
                if response:
                    resp_str = response.decode('ascii', errors='ignore').strip()
                    
                    # Filtrer les réponses utiles
                    if ("command not found" not in resp_str.lower() and 
                        "IC-R8600 #" not in resp_str and
                        len(resp_str) > 10):
                        
                        print(f"✅ Réponse utile: {resp_str[:100]}...")
                        successful_commands.append((cmd, resp_str))
                    else:
                        print(f"❌ {resp_str}")
                else:
                    print("❌ Pas de réponse")
                    
            except socket.timeout:
                print("⏱️ Timeout")
            except Exception as e:
                print(f"❌ Erreur: {e}")
        
        print(f"\n📊 Commandes réussies: {len(successful_commands)}")
        
        if successful_commands:
            print("\n✅ Commandes fonctionnelles trouvées:")
            for cmd, resp in successful_commands:
                print(f"  - {cmd}: {resp[:50]}...")
        
        sock.close()
        return len(successful_commands) > 0
        
    except Exception as e:
        print(f"❌ Erreur connexion Telnet: {e}")
        return False

def test_civ_over_telnet():
    """Test d'envoi de commandes CI-V via Telnet avec différents formats"""
    host = "**************"
    port = 23
    
    print(f"\n🧪 Test CI-V via Telnet avec différents formats...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        
        sock.connect((host, port))
        
        # Lire le prompt
        sock.recv(1024)
        
        # Différents formats de commandes CI-V à tester
        civ_formats = [
            # Format hexadécimal direct
            "FEFE94E003FD",
            
            # Format avec espaces
            "FE FE 94 E0 03 FD",
            
            # Format avec préfixe
            "civ FEFE94E003FD",
            "ci-v FEFE94E003FD",
            "send FEFE94E003FD",
            
            # Format avec commande
            "remote FEFE94E003FD",
            "control FEFE94E003FD",
            
            # Format binaire encodé
            "\\xFE\\xFE\\x94\\xE0\\x03\\xFD",
        ]
        
        print(f"📤 Test de {len(civ_formats)} formats CI-V...")
        
        for i, fmt in enumerate(civ_formats):
            print(f"\n[{i+1}] Format: {fmt}")
            
            sock.send(f"{fmt}\r\n".encode())
            time.sleep(0.5)
            
            try:
                response = sock.recv(1024)
                if response:
                    resp_str = response.decode('ascii', errors='ignore').strip()
                    if "command not found" not in resp_str.lower():
                        print(f"✅ Réponse: {resp_str}")
                    else:
                        print(f"❌ {resp_str}")
                else:
                    print("❌ Pas de réponse")
            except:
                print("❌ Erreur lecture")
        
        sock.close()
        
    except Exception as e:
        print(f"❌ Erreur test CI-V: {e}")

if __name__ == "__main__":
    print("=" * 70)
    print("🧪 EXPLORATION INTERFACE TELNET IC-R8600")
    print("=" * 70)
    
    # Test 1: Commandes Telnet standard
    commands_found = test_telnet_commands()
    
    # Test 2: Formats CI-V via Telnet
    test_civ_over_telnet()
    
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ")
    print("=" * 70)
    
    if commands_found:
        print("✅ Interface Telnet fonctionnelle avec commandes spécifiques")
        print("🎯 Possibilité de contrôle via Telnet")
    else:
        print("⚠️ Interface Telnet basique uniquement")
        print("🔧 Recherche d'autres méthodes de contrôle nécessaire")
    
    print("=" * 70)
