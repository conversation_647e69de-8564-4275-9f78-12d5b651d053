#!/usr/bin/env python3
"""
Test rapide VirtualHere - IC-R8600 sur COM6
"""

import serial
import serial.tools.list_ports
import time

def test_com6_ic_r8600():
    """Test rapide sur COM6"""
    print("🎯 TEST RAPIDE IC-R8600 VirtualHere")
    print("=" * 50)
    print("Port détecté: COM6 (CP2102 USB to UART Bridge)")
    print("=" * 50)
    
    try:
        # Test connexion COM6
        print("Connexion sur COM6...")
        ser = serial.Serial('COM6', 19200, timeout=2)
        print("✅ Connexion série établie")
        
        # Test commande CI-V
        print("\nTest commande CI-V (lecture fréquence)...")
        cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
        print(f"Envoi: {cmd.hex().upper()}")
        
        ser.reset_input_buffer()
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(50)
        if response:
            print(f"✅ Réponse: {response.hex().upper()}")
            print("✅ Communication IC-R8600 via VirtualHere: SUCCÈS")
        else:
            print("⚠️  Pas de réponse")
        
        # Test contrôle fréquence
        print("\nTest contrôle fréquence 145.500 MHz...")
        print("⚠️  OBSERVEZ L'AFFICHAGE IC-R8600 !")
        
        freq_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x05, 0x00, 0x00, 0x50, 0x45, 0x01, 0xFD])
        ser.write(freq_cmd)
        time.sleep(2)
        
        result = input("La fréquence 145.500 MHz s'affiche-t-elle ? (o/n): ")
        if result.lower() == 'o':
            print("✅ Contrôle fréquence: SUCCÈS")
        
        ser.close()
        
        print("\n🎉 VirtualHere IC-R8600 opérationnel !")
        print("Prêt pour l'application FastAPI")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    test_com6_ic_r8600()
