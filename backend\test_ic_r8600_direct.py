#!/usr/bin/env python3
"""
Test direct de communication avec IC-R8600
Vérification de l'impact réel sur le récepteur
"""

import socket
import time
import logging

def test_telnet_connection():
    """Test de connexion Telnet basique"""
    print("🔍 TEST 1: Connexion Telnet basique")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        sock.connect(("**************", 23))
        print("✅ Connexion Telnet établie sur port 23")
        sock.close()
        return True
    except Exception as e:
        print(f"❌ Erreur connexion Telnet: {e}")
        return False

def test_ci_v_power_commands():
    """Test des commandes Power ON/OFF avec observation"""
    print("\n🔍 TEST 2: Commandes Power CI-V")
    print("⚠️  OBSERVEZ L'IC-R8600 PENDANT CE TEST !")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10.0)
        sock.connect(("**************", 23))
        
        # Configuration Telnet
        init_commands = [
            b'*CLS\r\n',
            b'SYST:COMM:SER:ECHO OFF\r\n',
        ]
        
        for cmd in init_commands:
            sock.sendall(cmd)
            time.sleep(0.2)
            try:
                sock.recv(1024)
            except socket.timeout:
                pass
        
        print("\n📡 Envoi Power OFF...")
        power_off = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x00, 0xFD])
        sock.sendall(power_off)
        print(f"Commande envoyée: {power_off.hex().upper()}")
        print("👀 L'IC-R8600 s'éteint-il ? (Attendez 5 secondes)")
        time.sleep(5)
        
        print("\n📡 Envoi Power ON...")
        power_on = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x01, 0xFD])
        sock.sendall(power_on)
        print(f"Commande envoyée: {power_on.hex().upper()}")
        print("👀 L'IC-R8600 s'allume-t-il ? (Attendez 5 secondes)")
        time.sleep(5)
        
        sock.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur test Power: {e}")
        return False

def test_frequency_command():
    """Test de changement de fréquence"""
    print("\n🔍 TEST 3: Commande de fréquence")
    print("⚠️  OBSERVEZ L'AFFICHAGE DE FRÉQUENCE SUR L'IC-R8600 !")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10.0)
        sock.connect(("**************", 23))
        
        # Configuration Telnet
        sock.sendall(b'SYST:COMM:SER:ECHO OFF\r\n')
        time.sleep(0.5)
        try:
            sock.recv(1024)
        except socket.timeout:
            pass
        
        # Fréquence 145.500 MHz = 145500000 Hz
        # En BCD: 01 45 50 00 00 (inversé)
        print("\n📡 Envoi fréquence 145.500 MHz...")
        freq_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x05, 0x00, 0x00, 0x50, 0x45, 0x01, 0xFD])
        sock.sendall(freq_cmd)
        print(f"Commande envoyée: {freq_cmd.hex().upper()}")
        print("👀 La fréquence change-t-elle sur l'affichage ? (Attendez 3 secondes)")
        time.sleep(3)
        
        # Fréquence 88.500 MHz = 88500000 Hz  
        # En BCD: 00 88 50 00 00 (inversé)
        print("\n📡 Envoi fréquence 88.500 MHz...")
        freq_cmd2 = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x05, 0x00, 0x00, 0x50, 0x88, 0x00, 0xFD])
        sock.sendall(freq_cmd2)
        print(f"Commande envoyée: {freq_cmd2.hex().upper()}")
        print("👀 La fréquence change-t-elle vers 88.500 MHz ? (Attendez 3 secondes)")
        time.sleep(3)
        
        sock.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur test fréquence: {e}")
        return False

def test_mode_command():
    """Test de changement de mode"""
    print("\n🔍 TEST 4: Commande de mode")
    print("⚠️  OBSERVEZ LE MODE SUR L'IC-R8600 !")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10.0)
        sock.connect(("**************", 23))
        
        # Configuration Telnet
        sock.sendall(b'SYST:COMM:SER:ECHO OFF\r\n')
        time.sleep(0.5)
        try:
            sock.recv(1024)
        except socket.timeout:
            pass
        
        print("\n📡 Envoi mode FM...")
        mode_fm = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x06, 0x05, 0xFD])
        sock.sendall(mode_fm)
        print(f"Commande envoyée: {mode_fm.hex().upper()}")
        print("👀 Le mode passe-t-il en FM ? (Attendez 2 secondes)")
        time.sleep(2)
        
        print("\n📡 Envoi mode AM...")
        mode_am = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x06, 0x02, 0xFD])
        sock.sendall(mode_am)
        print(f"Commande envoyée: {mode_am.hex().upper()}")
        print("👀 Le mode passe-t-il en AM ? (Attendez 2 secondes)")
        time.sleep(2)
        
        sock.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur test mode: {e}")
        return False

def check_ic_r8600_status():
    """Vérification de l'état de l'IC-R8600"""
    print("\n🔍 VÉRIFICATION PRÉALABLE")
    print("Avant de commencer les tests, vérifiez :")
    print("1. ✅ IC-R8600 est allumé")
    print("2. ✅ Câble RJ45 connecté")
    print("3. ✅ IP ************** configurée")
    print("4. ✅ Vous pouvez voir l'écran de l'IC-R8600")
    
    response = input("\nTous les points sont-ils OK ? (o/n): ").lower()
    return response == 'o' or response == 'oui'

def main():
    """Test principal de communication IC-R8600"""
    print("🎯 TEST DIRECT DE COMMUNICATION IC-R8600")
    print("=" * 60)
    print("Ce test vérifie si les commandes ont un impact réel")
    print("sur votre récepteur IC-R8600")
    print("=" * 60)
    
    # Vérification préalable
    if not check_ic_r8600_status():
        print("❌ Vérifiez la configuration avant de continuer")
        return
    
    # Tests de communication
    tests = [
        ("Connexion Telnet", test_telnet_connection),
        ("Commandes Power", test_ci_v_power_commands),
        ("Commande Fréquence", test_frequency_command),
        ("Commande Mode", test_mode_command),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
        
        if test_name != "Connexion Telnet":
            input("\nAppuyez sur Entrée pour continuer au test suivant...")
    
    # Résultats
    print(f"\n{'='*60}")
    print("📊 RÉSULTATS DES TESTS")
    print(f"{'='*60}")
    
    for test_name, result in results:
        status = "✅ SUCCÈS" if result else "❌ ÉCHEC"
        print(f"{test_name}: {status}")
    
    # Diagnostic
    print(f"\n{'='*60}")
    print("🔍 DIAGNOSTIC")
    print(f"{'='*60}")
    
    if all(result for _, result in results):
        print("✅ Tous les tests réussis !")
        print("Si aucun impact n'est visible sur l'IC-R8600:")
        print("1. Vérifiez que l'IC-R8600 est en mode LOCAL OFF")
        print("2. Vérifiez les paramètres réseau dans le menu")
        print("3. Redémarrez l'IC-R8600")
    else:
        print("❌ Certains tests ont échoué")
        print("Problèmes possibles:")
        print("1. IC-R8600 non accessible sur **************")
        print("2. Port Telnet 23 fermé")
        print("3. Configuration réseau incorrecte")
        print("4. Firmware v1.33 avec limitations")

if __name__ == "__main__":
    main()
