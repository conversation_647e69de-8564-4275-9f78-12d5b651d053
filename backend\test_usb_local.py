#!/usr/bin/env python3
"""
Test USB Local IC-R8600
Vérification avant déploiement réseau
"""

import serial
import serial.tools.list_ports
import time

def test_usb_detection():
    """Test 1: Détection USB de l'IC-R8600"""
    print("🔍 TEST 1: DÉTECTION USB IC-R8600")
    print("=" * 50)
    
    ports = serial.tools.list_ports.comports()
    icom_ports = []
    
    print("Ports COM détectés:")
    for port in ports:
        print(f"  {port.device}: {port.description}")
        
        # Recherche IC-R8600
        desc = port.description.upper()
        if any(keyword in desc for keyword in ['ICOM', 'IC-R8600', 'R8600']):
            icom_ports.append(port)
            print(f"  ✅ IC-R8600 DÉTECTÉ: {port.device}")
    
    if not icom_ports:
        print("❌ IC-R8600 non détecté !")
        print("\nVérifications:")
        print("1. IC-R8600 allumé ?")
        print("2. Câble USB connecté ?")
        print("3. Drivers ICOM installés ?")
        return None
    
    return icom_ports[0].device

def test_communication(port):
    """Test 2: Communication CI-V"""
    print(f"\n🔍 TEST 2: COMMUNICATION CI-V sur {port}")
    print("=" * 50)
    
    try:
        # Connexion série
        ser = serial.Serial(port, 19200, timeout=2)
        print(f"✅ Connexion série établie sur {port}")
        
        # Test commandes CI-V
        commands = [
            ("Status", [0xFE, 0xFE, 0x94, 0xE0, 0x19, 0x00, 0xFD]),
            ("Lecture fréquence", [0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD]),
            ("Lecture mode", [0xFE, 0xFE, 0x94, 0xE0, 0x04, 0xFD]),
        ]
        
        for cmd_name, cmd_bytes in commands:
            print(f"\nTest {cmd_name}:")
            print(f"  Envoi: {bytes(cmd_bytes).hex().upper()}")
            
            ser.reset_input_buffer()
            ser.write(bytes(cmd_bytes))
            time.sleep(0.5)
            
            response = ser.read(50)
            if response:
                print(f"  ✅ Réponse: {response.hex().upper()}")
            else:
                print(f"  ⚠️  Pas de réponse")
        
        ser.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur communication: {e}")
        return False

def test_frequency_control(port):
    """Test 3: Contrôle de fréquence"""
    print(f"\n🔍 TEST 3: CONTRÔLE FRÉQUENCE sur {port}")
    print("=" * 50)
    print("⚠️  OBSERVEZ L'AFFICHAGE DE L'IC-R8600 !")
    
    try:
        ser = serial.Serial(port, 19200, timeout=2)
        
        # Test fréquences
        frequencies = [
            (145.500, [0xFE, 0xFE, 0x94, 0xE0, 0x05, 0x00, 0x00, 0x50, 0x45, 0x01, 0xFD]),
            (88.500, [0xFE, 0xFE, 0x94, 0xE0, 0x05, 0x00, 0x00, 0x50, 0x88, 0x00, 0xFD]),
        ]
        
        for freq_mhz, cmd_bytes in frequencies:
            print(f"\nDéfinition fréquence {freq_mhz} MHz:")
            print(f"  Commande: {bytes(cmd_bytes).hex().upper()}")
            
            ser.write(bytes(cmd_bytes))
            time.sleep(2)
            
            response = input(f"  La fréquence {freq_mhz} MHz s'affiche-t-elle ? (o/n): ")
            if response.lower() == 'o':
                print(f"  ✅ Fréquence {freq_mhz} MHz confirmée")
            else:
                print(f"  ❌ Fréquence {freq_mhz} MHz non confirmée")
        
        ser.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur contrôle fréquence: {e}")
        return False

def main():
    """Test complet USB local"""
    print("🎯 TEST COMPLET USB LOCAL IC-R8600")
    print("=" * 60)
    print("Test avant déploiement réseau VirtualHere")
    print("=" * 60)
    
    # Test 1: Détection
    port = test_usb_detection()
    if not port:
        print("\n❌ ÉCHEC: IC-R8600 non détecté")
        input("Appuyez sur Entrée pour quitter...")
        return
    
    # Test 2: Communication
    if not test_communication(port):
        print("\n❌ ÉCHEC: Communication impossible")
        input("Appuyez sur Entrée pour quitter...")
        return
    
    # Test 3: Contrôle
    if not test_frequency_control(port):
        print("\n❌ ÉCHEC: Contrôle impossible")
        input("Appuyez sur Entrée pour quitter...")
        return
    
    # Résultat final
    print("\n" + "=" * 60)
    print("✅ SUCCÈS: IC-R8600 USB LOCAL OPÉRATIONNEL")
    print("=" * 60)
    print(f"Port détecté: {port}")
    print("Communication: OK")
    print("Contrôle: OK")
    print("\n🚀 Prêt pour déploiement VirtualHere !")
    print("=" * 60)
    
    input("Appuyez sur Entrée pour continuer...")

if __name__ == "__main__":
    main()
