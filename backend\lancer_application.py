#!/usr/bin/env python3
"""
Script de lancement guidé pour l'application IC-R8600
Vérifie la connectivité et guide l'utilisateur
"""

import subprocess
import sys
import time
import socket

def print_banner():
    """Affiche la bannière de l'application"""
    print("=" * 70)
    print("🎯 CONTRÔLEUR IC-R8600 via CÂBLE RJ45")
    print("=" * 70)
    print("📡 Mode: UDP uniquement (pas de simulation)")
    print("🔌 Connexion: Câble réseau RJ45 requis")
    print("🎛️ Contrôle: CI-V encapsulé dans UDP")
    print("=" * 70)

def test_network_connectivity():
    """Test de connectivité réseau"""
    host = "**************"
    print(f"\n🏓 Test de connectivité vers {host}...")
    
    try:
        result = subprocess.run(['ping', '-n', '1', host], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ IC-R8600 accessible sur le réseau")
            return True
        else:
            print("❌ IC-R8600 non accessible")
            return False
    except Exception as e:
        print(f"❌ Erreur ping: {e}")
        return False

def test_network_control():
    """Test du Network Control UDP"""
    host = "**************"
    port = 50001
    print(f"\n🔌 Test du Network Control UDP sur {host}:{port}...")

    try:
        # Test UDP avec commande CI-V
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(3.0)

        # Commande CI-V de test
        ci_v_command = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
        sock.sendto(ci_v_command, (host, port))

        # Attendre la réponse
        response, addr = sock.recvfrom(1024)
        sock.close()

        if response and len(response) >= 6:
            if response[0:2] == bytes([0xFE, 0xFE]) and response[-1] == 0xFD:
                print("✅ Network Control activé et fonctionnel")
                return True

        print("❌ Network Control non fonctionnel")
        return False

    except socket.timeout:
        print("❌ Network Control non activé (timeout)")
        return False
    except Exception as e:
        print(f"❌ Erreur test Network Control: {e}")
        return False

def show_configuration_guide():
    """Affiche le guide de configuration"""
    print("\n" + "=" * 70)
    print("🔧 CONFIGURATION REQUISE")
    print("=" * 70)
    print("1. 🔌 IC-R8600 allumé et connecté via câble RJ45")
    print("2. 📡 Configuration réseau IC-R8600:")
    print("   MENU → SET → Connectors → Network")
    print("   ├── IP Address: **************")
    print("   ├── Control Port UDP: 50001")
    print("   ├── Serial Port UDP: 50002")
    print("   ├── Audio Port UDP: 50003")
    print("   └── Network Control: ON ← IMPORTANT !")
    print("3. 🔄 Redémarrer l'IC-R8600 après configuration")
    print("=" * 70)

def wait_for_network_control():
    """Attend que le Network Control soit activé"""
    print("\n⏳ Attente de l'activation du Network Control...")
    print("💡 Activez Network Control sur l'IC-R8600 puis appuyez sur Entrée")

    max_attempts = 30
    for attempt in range(max_attempts):
        try:
            input(f"\n[Tentative {attempt + 1}/{max_attempts}] Appuyez sur Entrée pour tester...")
        except KeyboardInterrupt:
            print("\n❌ Annulé par l'utilisateur")
            return False

        if test_network_control():
            print("✅ Network Control détecté !")
            return True

        print("⚠️ Network Control toujours non activé")

        if attempt < max_attempts - 1:
            continue_test = input("Continuer les tests ? (o/N): ").lower()
            if continue_test != 'o':
                break

    return False

def launch_application():
    """Lance l'application principale"""
    print("\n🚀 Lancement de l'application...")
    print("📊 Backend API: http://localhost:8000")
    print("🌐 Interface Web: http://localhost:5173")
    print("\n" + "=" * 70)
    
    try:
        # Lancer l'application
        subprocess.run([sys.executable, "main.py"], check=True)
    except KeyboardInterrupt:
        print("\n⏹️ Application arrêtée par l'utilisateur")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Erreur lors du lancement: {e}")
        return False
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        return False
    
    return True

def main():
    """Fonction principale"""
    print_banner()
    
    # Test 1: Connectivité réseau
    if not test_network_connectivity():
        show_configuration_guide()
        print("\n❌ Impossible de continuer sans connectivité réseau")
        print("🔧 Vérifiez la configuration et relancez le script")
        return 1
    
    # Test 2: Network Control
    if not test_network_control():
        print("\n⚠️ Network Control non activé")
        show_configuration_guide()

        # Proposer d'attendre l'activation
        wait_choice = input("\nAttendre l'activation du Network Control ? (o/N): ").lower()
        if wait_choice == 'o':
            if not wait_for_network_control():
                print("\n❌ Network Control non activé - Arrêt")
                return 1
        else:
            print("\n❌ Network Control requis - Arrêt")
            return 1
    
    # Tout est OK, lancer l'application
    print("\n✅ Tous les tests passés !")
    print("🎯 Prêt pour le lancement de l'application")
    
    launch_choice = input("\nLancer l'application maintenant ? (O/n): ").lower()
    if launch_choice != 'n':
        return 0 if launch_application() else 1
    else:
        print("\n📋 Pour lancer manuellement:")
        print("   python main.py")
        return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⏹️ Script interrompu par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")
        sys.exit(1)
