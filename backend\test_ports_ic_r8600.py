#!/usr/bin/env python3
"""
Test des ports réseau IC-R8600
Vérification de l'ouverture des ports 50001, 50002, 50003, 50004
"""

import socket
import time
import subprocess
import sys

def ping_test():
    """Test de ping vers l'IC-R8600"""
    print("🔍 TEST 1: Connectivité réseau (ping)")
    try:
        result = subprocess.run(['ping', '-n', '4', '**************'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ Ping réussi vers **************")
            return True
        else:
            print("❌ Ping échoué")
            print(result.stdout)
            return False
    except Exception as e:
        print(f"❌ Erreur ping: {e}")
        return False

def test_tcp_port(port):
    """Test d'un port TCP"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3.0)
        result = sock.connect_ex(('**************', port))
        sock.close()
        return result == 0
    except:
        return False

def test_udp_port(port):
    """Test d'un port UDP"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(3.0)
        
        # Envoyer un paquet de test
        test_data = b'\xFE\xFE\x94\xE0\x19\x00\xFD'  # Commande status CI-V
        sock.sendto(test_data, ('**************', port))
        
        # Essayer de recevoir une réponse
        try:
            response, addr = sock.recvfrom(1024)
            sock.close()
            return True, f"Réponse reçue: {response.hex().upper()}"
        except socket.timeout:
            sock.close()
            return True, "Pas de réponse (normal)"
            
    except Exception as e:
        return False, f"Erreur: {e}"

def test_all_ports():
    """Test de tous les ports IC-R8600"""
    print("\n🔍 TEST 2: Ports TCP")
    tcp_ports = [23, 50001]  # Telnet et éventuel TCP
    
    for port in tcp_ports:
        if test_tcp_port(port):
            print(f"✅ Port TCP {port}: OUVERT")
        else:
            print(f"❌ Port TCP {port}: FERMÉ")
    
    print("\n🔍 TEST 3: Ports UDP")
    udp_ports = [50001, 50002, 50003, 50004]  # Ports configurés
    
    for port in udp_ports:
        is_open, message = test_udp_port(port)
        status = "✅ OUVERT" if is_open else "❌ FERMÉ"
        print(f"{status} Port UDP {port}: {message}")

def test_network_config():
    """Test de configuration réseau avancée"""
    print("\n🔍 TEST 4: Configuration réseau avancée")
    
    try:
        # Test avec netstat pour voir les connexions
        print("Vérification des connexions réseau...")
        result = subprocess.run(['netstat', '-an'], capture_output=True, text=True)
        
        # Chercher les ports 50001-50004
        lines = result.stdout.split('\n')
        found_ports = []
        
        for line in lines:
            if '**************' in line:
                for port in ['50001', '50002', '50003', '50004']:
                    if f':{port}' in line:
                        found_ports.append(port)
                        print(f"✅ Connexion active détectée sur port {port}")
        
        if not found_ports:
            print("❌ Aucune connexion active détectée vers les ports IC-R8600")
            
    except Exception as e:
        print(f"❌ Erreur vérification réseau: {e}")

def check_firewall():
    """Vérification du pare-feu Windows"""
    print("\n🔍 TEST 5: Vérification pare-feu")
    try:
        # Vérifier les règles de pare-feu pour les ports
        result = subprocess.run(['netsh', 'advfirewall', 'firewall', 'show', 'rule', 'name=all'], 
                              capture_output=True, text=True)
        
        if '50001' in result.stdout or '50002' in result.stdout:
            print("✅ Règles pare-feu détectées pour les ports IC-R8600")
        else:
            print("⚠️  Aucune règle pare-feu spécifique détectée")
            print("Cela peut être normal si le pare-feu est désactivé")
            
    except Exception as e:
        print(f"❌ Erreur vérification pare-feu: {e}")

def main():
    """Test principal des ports réseau"""
    print("🌐 TEST COMPLET DES PORTS RÉSEAU IC-R8600")
    print("=" * 60)
    print("IP cible: **************")
    print("Ports à tester: 23, 50001, 50002, 50003, 50004")
    print("=" * 60)
    
    # Tests séquentiels
    tests = [
        ping_test,
        test_all_ports,
        test_network_config,
        check_firewall
    ]
    
    for test_func in tests:
        try:
            test_func()
            time.sleep(1)
        except Exception as e:
            print(f"❌ Erreur dans {test_func.__name__}: {e}")
    
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ ET RECOMMANDATIONS")
    print("=" * 60)
    print("Si les ports UDP 50002-50004 sont FERMÉS:")
    print("1. Vérifiez MENU → SET → Network → Network Control: ON")
    print("2. Redémarrez l'IC-R8600 après changement")
    print("3. Vérifiez que le bouton LOCAL est éteint")
    print("4. Essayez de changer l'adresse IP de l'IC-R8600")
    print("\nSi les ports sont OUVERTS mais pas de réponse:")
    print("1. Problème d'adresse CI-V (essayer 0x94, 0x96, 0x98)")
    print("2. Firmware v1.33 avec limitations spécifiques")
    print("3. Configuration interne IC-R8600 incomplète")

if __name__ == "__main__":
    main()
