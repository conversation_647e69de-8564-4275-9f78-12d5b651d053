#!/usr/bin/env python3
"""
Test des deux adresses CI-V : 94h (locale) et DFh (remote)
"""

import serial
import time

def test_address(address_hex, address_name):
    """Test une adresse CI-V spécifique"""
    print(f"\n🔍 TEST ADRESSE {address_name} ({address_hex:02X}h)")
    print("=" * 50)
    
    try:
        ser = serial.Serial('COM6', 19200, timeout=2)
        print(f"✅ COM6 ouvert")
        
        # Test 1: Lecture fréquence
        cmd_read = bytes([0xFE, 0xFE, address_hex, 0xE0, 0x03, 0xFD])
        print(f"Test lecture fréquence: {cmd_read.hex().upper()}")
        
        ser.reset_input_buffer()
        ser.write(cmd_read)
        time.sleep(1)
        
        response = ser.read(50)
        if response:
            print(f"✅ Réponse lecture: {response.hex().upper()}")
        else:
            print(f"⚠️  Pas de réponse lecture")
        
        # Test 2: Power OFF
        cmd_power_off = bytes([0xFE, 0xFE, address_hex, 0xE0, 0x18, 0x00, 0xFD])
        print(f"Test Power OFF: {cmd_power_off.hex().upper()}")
        print("⚠️  OBSERVEZ L'IC-R8600 !")
        
        ser.reset_input_buffer()
        ser.write(cmd_power_off)
        time.sleep(2)
        
        response = ser.read(50)
        if response:
            print(f"✅ Réponse Power OFF: {response.hex().upper()}")
        else:
            print(f"⚠️  Pas de réponse Power OFF")
        
        # Demander à l'utilisateur
        result = input(f"L'IC-R8600 a-t-il réagi avec l'adresse {address_name} ? (o/n): ")
        
        if result.lower() == 'o':
            print(f"🎉 SUCCÈS avec adresse {address_name} ({address_hex:02X}h) !")
            
            # Test Power ON pour rallumer
            cmd_power_on = bytes([0xFE, 0xFE, address_hex, 0xE0, 0x18, 0x01, 0xFD])
            print(f"Rallumage: {cmd_power_on.hex().upper()}")
            ser.write(cmd_power_on)
            time.sleep(1)
            
            ser.close()
            return True
        else:
            print(f"❌ Échec avec adresse {address_name}")
            ser.close()
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Test des deux adresses"""
    print("🎯 TEST ADRESSES CI-V IC-R8600")
    print("=" * 60)
    print("Configuration actuelle:")
    print("- Port: COM6 (VirtualHere)")
    print("- CI-V Address: 94h")
    print("- Remote Address: DFh")
    print("- CI-V USB Port: Link to Remote")
    print("=" * 60)
    
    # Test adresse locale 94h
    success_94h = test_address(0x94, "LOCALE")
    
    if success_94h:
        print("\n🎉 SOLUTION TROUVÉE : Utilisez adresse 94h (locale)")
        return
    
    # Test adresse remote DFh
    success_dfh = test_address(0xDF, "REMOTE")
    
    if success_dfh:
        print("\n🎉 SOLUTION TROUVÉE : Utilisez adresse DFh (remote)")
        return
    
    print("\n❌ AUCUNE ADRESSE NE FONCTIONNE")
    print("Vérifications supplémentaires nécessaires:")
    print("1. CI-V Echo Back: OFF ?")
    print("2. IC-R8600 en mode Remote ?")
    print("3. Autre configuration CI-V ?")

if __name__ == "__main__":
    main()
