#!/usr/bin/env python3
"""
Test rapide après configuration Network Control
"""

import socket
import time

def quick_port_test():
    """Test rapide des ports après configuration"""
    print("🔍 TEST RAPIDE APRÈS CONFIGURATION")
    print("=" * 50)
    
    host = "**************"
    ports = [50001, 50002, 50003]
    
    print("Test des ports UDP ICOM...")
    
    for port in ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(2.0)
            
            # Envoyer un paquet de test
            test_packet = b'\xFE\xFE\x94\xE0\x19\x00\xFD'
            sock.sendto(test_packet, (host, port))
            
            try:
                response, addr = sock.recvfrom(1024)
                print(f"✅ Port {port}: OUVERT - Réponse reçue !")
                print(f"   Réponse: {response.hex().upper()}")
            except socket.timeout:
                print(f"❌ Port {port}: Pas de réponse")
            
            sock.close()
            
        except Exception as e:
            print(f"❌ Port {port}: Erreur - {e}")

def test_power_command():
    """Test d'une commande power"""
    print("\n🔋 TEST COMMANDE POWER")
    print("-" * 30)
    
    host = "**************"
    port = 50002  # Port série
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(3.0)
        
        # Commande Power OFF
        power_off = b'\xFE\xFE\x94\xE0\x18\x00\xFD'
        print("Envoi commande Power OFF...")
        print("OBSERVEZ L'IC-R8600 !")
        
        sock.sendto(power_off, (host, port))
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"✅ Réponse reçue: {response.hex().upper()}")
            
            if b'\xFB' in response:
                print("✅ ACK - Commande acceptée !")
            elif b'\xFA' in response:
                print("❌ NAK - Commande refusée")
                
        except socket.timeout:
            print("❌ Pas de réponse")
        
        sock.close()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    print("TEST APRÈS CONFIGURATION NETWORK CONTROL")
    print("Assurez-vous d'avoir :")
    print("1. Activé Network Control sur l'IC-R8600")
    print("2. Redémarré l'IC-R8600")
    print("3. Appuyé sur [LOCAL] pour mode REMOTE")
    print()
    
    quick_port_test()
    
    input("\nAppuyez sur Entrée pour tester une commande...")
    test_power_command()
    
    print("\nSi les ports sont maintenant ouverts :")
    print("✅ Configuration réussie !")
    print("✅ Relancez l'application principale")
    print("\nSi les ports sont toujours fermés :")
    print("❌ Vérifiez à nouveau la configuration")
    print("❌ Consultez le manuel IC-R8600")
