#!/usr/bin/env python3
"""
Test debug pour voir exactement ce que retourne l'API
"""
import requests
import json

API_BASE = "http://localhost:8000"

def test_command_debug():
    """Test avec debug complet"""
    print("🔍 TEST DEBUG COMMANDE")
    print("=" * 50)
    
    # Test changement de fréquence
    command_data = {
        "frequency": 88.5,
        "mode": "FM",
        "rf_gain": 128
    }
    
    try:
        print(f"📤 Envoi: {json.dumps(command_data, indent=2)}")
        response = requests.post(f"{API_BASE}/api/command", json=command_data)
        print(f"📥 Status HTTP: {response.status_code}")
        print(f"📥 Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📥 Réponse JSON:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # Analyse de la réponse
            if result.get('success'):
                print("✅ SUCCESS = TRUE")
                print(f"✅ Message: {result.get('message')}")
                print(f"✅ Simulation: {result.get('simulation_mode', 'Non spécifié')}")
            else:
                print("❌ SUCCESS = FALSE")
                print(f"❌ Message: {result.get('message')}")
        else:
            print(f"❌ Erreur HTTP: {response.status_code}")
            print(f"❌ Contenu: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_status():
    """Test du statut"""
    print("\n🔍 TEST STATUS")
    print("=" * 50)
    
    try:
        response = requests.get(f"{API_BASE}/api/status")
        print(f"📥 Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"📥 Status JSON:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"❌ Erreur: {response.text}")
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_status()
    test_command_debug()
