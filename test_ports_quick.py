#!/usr/bin/env python3
"""
Test rapide des ports IC-R8600
"""

import socket
import time

def test_port(host, port, protocol="TCP"):
    """Test un port spécifique"""
    try:
        if protocol == "TCP":
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2.0)
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        else:  # UDP
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(1.0)
            # Envoyer une commande CI-V de test
            test_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
            sock.sendto(test_cmd, (host, port))
            try:
                response, addr = sock.recvfrom(1024)
                sock.close()
                return True
            except socket.timeout:
                sock.close()
                return False
    except Exception:
        return False

def main():
    host = "**************"
    
    print("TEST RAPIDE DES PORTS IC-R8600")
    print("=" * 40)
    
    # Ports à tester
    ports_tcp = [23, 80, 443, 8080]
    ports_udp = [50001, 50002, 50003]
    
    print("Ports TCP:")
    for port in ports_tcp:
        status = "OUVERT" if test_port(host, port, "TCP") else "FERME"
        print(f"  Port {port}: {status}")
    
    print("\nPorts UDP (avec commande CI-V):")
    for port in ports_udp:
        status = "REPOND" if test_port(host, port, "UDP") else "PAS DE REPONSE"
        print(f"  Port {port}: {status}")

if __name__ == "__main__":
    main()
