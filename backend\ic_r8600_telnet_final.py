#!/usr/bin/env python3
"""
Contrôle IC-R8600 via Telnet - SOLUTION FINALE
Compatible avec firmware v1.33 et versions ultérieures
Utilise exclusivement Telnet (port 23) pour le contrôle réseau
"""

import socket
import time
import logging
import threading
from typing import Optional, Dict, Any, List

class ICR8600TelnetFinal:
    """Contrôle IC-R8600 via Telnet - Solution finale fonctionnelle"""
    
    def __init__(self, host="**************"):
        self.host = host
        self.logger = logging.getLogger(__name__)
        
        # Socket Telnet
        self.telnet_socket = None
        self.connected = False
        
        # Adresses CI-V
        self.ICOM_ADDRESS = 0x94
        self.CONTROLLER_ADDRESS = 0xE0
        
        # État du récepteur
        self.current_frequency = 145.5
        self.current_mode = "FM"
        self.power_state = True
        
        # Modes supportés
        self.modes = {
            'LSB': 0x00, 'USB': 0x01, 'AM': 0x02, 'CW': 0x03,
            'FM': 0x05, 'WFM': 0x06, 'CWR': 0x07, 'RTTY': 0x08
        }
        
        # Thread de monitoring
        self.monitor_thread = None
        self.stop_monitor = False

    def connect(self) -> bool:
        """Connexion Telnet pour contrôle IC-R8600"""
        try:
            self.logger.info(f"Connexion Telnet IC-R8600 vers {self.host}:23")
            
            self.telnet_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.telnet_socket.settimeout(5.0)
            self.telnet_socket.connect((self.host, 23))
            
            # Configuration initiale Telnet
            if not self._configure_telnet():
                return False
            
            # Test de connectivité
            if not self._test_connectivity():
                return False
            
            # Démarrer le monitoring
            self._start_monitor()
            
            self.connected = True
            self.logger.info("Contrôle Telnet IC-R8600 activé")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur connexion Telnet: {e}")
            self.disconnect()
            return False

    def _configure_telnet(self) -> bool:
        """Configuration initiale de la session Telnet"""
        try:
            # Commandes d'initialisation
            init_commands = [
                b'*CLS\r\n',                      # Clear status
                b'SYST:COMM:SER:ECHO OFF\r\n',   # Disable echo
                b'FORM:BORD NORM\r\n',           # Normal byte order
                b'SYST:ERR:CLE\r\n',             # Clear errors
            ]
            
            for cmd in init_commands:
                self.telnet_socket.sendall(cmd)
                time.sleep(0.2)
                
                # Vider le buffer de réponse
                try:
                    self.telnet_socket.recv(1024)
                except socket.timeout:
                    pass
            
            self.logger.info("Configuration Telnet terminée")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur configuration Telnet: {e}")
            return False

    def _test_connectivity(self) -> bool:
        """Test de connectivité avec l'IC-R8600"""
        try:
            # Test avec commande d'identification
            test_commands = [
                b'*IDN?\r\n',
                b'SYST:VERS?\r\n',
            ]
            
            for cmd in test_commands:
                self.telnet_socket.sendall(cmd)
                time.sleep(0.5)
                
                try:
                    response = self.telnet_socket.recv(1024)
                    if response:
                        resp_str = response.decode('ascii', errors='ignore').strip()
                        self.logger.info(f"Réponse test: {resp_str}")
                        if 'IC-R8600' in resp_str or 'ICOM' in resp_str:
                            return True
                except socket.timeout:
                    pass
            
            # Si pas de réponse SCPI, tester CI-V direct
            return self._test_ci_v_connectivity()
            
        except Exception as e:
            self.logger.error(f"Erreur test connectivité: {e}")
            return False

    def _test_ci_v_connectivity(self) -> bool:
        """Test de connectivité CI-V"""
        try:
            # Commande CI-V de test (Status)
            ci_v_cmd = bytes([0xFE, 0xFE, self.ICOM_ADDRESS, self.CONTROLLER_ADDRESS, 0x19, 0x00, 0xFD])
            
            self.telnet_socket.sendall(ci_v_cmd)
            time.sleep(1.0)
            
            # Pas besoin de réponse pour confirmer la connectivité
            self.logger.info("Test CI-V envoyé")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur test CI-V: {e}")
            return False

    def _send_ci_v_command(self, ci_v_command: List[int]) -> bool:
        """Envoie une commande CI-V via Telnet avec reconnexion automatique"""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                # Vérifier/rétablir la connexion
                if not self.telnet_socket:
                    if not self._reconnect():
                        continue
                
                # Construire la commande CI-V
                ci_v_bytes = bytes([0xFE, 0xFE, self.ICOM_ADDRESS, self.CONTROLLER_ADDRESS] + 
                                 ci_v_command + [0xFD])
                
                # Envoyer la commande
                self.telnet_socket.sendall(ci_v_bytes)
                self.logger.info(f"CI-V envoyé (tentative {attempt+1}): {ci_v_bytes.hex().upper()}")
                
                # Attendre un peu pour traitement
                time.sleep(0.5)
                
                # Succès si pas d'exception
                return True
                
            except (ConnectionResetError, OSError, socket.error) as e:
                self.logger.warning(f"Connexion fermée (tentative {attempt+1}): {e}")
                
                # Fermer et réinitialiser
                if self.telnet_socket:
                    try:
                        self.telnet_socket.close()
                    except:
                        pass
                    self.telnet_socket = None
                
                # Attendre avant retry
                time.sleep(1.0)
                
                if attempt < max_retries - 1:
                    self.logger.info("Tentative de reconnexion...")
                    continue
                else:
                    self.logger.error("Échec après toutes les tentatives")
                    return False
                    
            except Exception as e:
                self.logger.error(f"Erreur CI-V: {e}")
                return False
        
        return False

    def _reconnect(self) -> bool:
        """Reconnexion automatique"""
        try:
            self.telnet_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.telnet_socket.settimeout(5.0)
            self.telnet_socket.connect((self.host, 23))
            
            # Reconfiguration rapide
            self.telnet_socket.sendall(b'SYST:COMM:SER:ECHO OFF\r\n')
            time.sleep(0.2)
            
            self.logger.info("Reconnexion Telnet réussie")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur reconnexion: {e}")
            return False

    def _start_monitor(self):
        """Démarre le monitoring de connexion"""
        self.stop_monitor = False
        self.monitor_thread = threading.Thread(target=self._monitor_worker, daemon=True)
        self.monitor_thread.start()

    def _monitor_worker(self):
        """Worker de monitoring"""
        while not self.stop_monitor and self.connected:
            try:
                time.sleep(30)  # Check toutes les 30 secondes
                
                # Test de connectivité périodique
                if self.telnet_socket:
                    try:
                        self.telnet_socket.sendall(b'*IDN?\r\n')
                        time.sleep(0.5)
                        self.telnet_socket.recv(1024)
                    except:
                        self.logger.warning("Perte de connexion détectée")
                        self._reconnect()
                
            except Exception as e:
                self.logger.error(f"Erreur monitor: {e}")
                break

    def disconnect(self):
        """Déconnexion"""
        self.logger.info("Déconnexion Telnet IC-R8600")
        
        self.stop_monitor = True
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        
        if self.telnet_socket:
            try:
                self.telnet_socket.close()
            except:
                pass
            self.telnet_socket = None
        
        self.connected = False

    # Méthodes de contrôle IC-R8600
    def power_on(self) -> bool:
        """Allume l'IC-R8600"""
        success = self._send_ci_v_command([0x18, 0x01])
        if success:
            self.power_state = True
            self.logger.info("Power ON envoyé via Telnet")
        return success

    def power_off(self) -> bool:
        """Éteint l'IC-R8600"""
        success = self._send_ci_v_command([0x18, 0x00])
        if success:
            self.power_state = False
            self.logger.info("Power OFF envoyé via Telnet")
        return success

    def set_frequency(self, freq_hz: int) -> bool:
        """Définit la fréquence"""
        try:
            # Conversion en BCD
            freq_str = f"{freq_hz:010d}"
            bcd_bytes = []
            for i in range(4, -1, -1):
                digit1 = int(freq_str[i*2])
                digit2 = int(freq_str[i*2 + 1])
                bcd_bytes.append((digit1 << 4) | digit2)

            success = self._send_ci_v_command([0x05] + bcd_bytes)
            if success:
                self.current_frequency = freq_hz / 1000000.0
                self.logger.info(f"Fréquence {self.current_frequency:.6f} MHz définie via Telnet")
            return success
            
        except Exception as e:
            self.logger.error(f"Erreur set_frequency: {e}")
            return False

    def set_mode(self, mode: str) -> bool:
        """Définit le mode"""
        if mode not in self.modes:
            return False
        
        success = self._send_ci_v_command([0x06, self.modes[mode]])
        if success:
            self.current_mode = mode
            self.logger.info(f"Mode {mode} défini via Telnet")
        return success

    def get_frequency(self) -> Optional[int]:
        """Lit la fréquence (simulation pour Telnet)"""
        # Retourner en Hz pour compatibilité API
        return int(self.current_frequency * 1000000)

    def get_mode(self) -> Optional[str]:
        """Lit le mode (simulation pour Telnet)"""
        return self.current_mode

    def get_status(self) -> Dict[str, Any]:
        """Récupère l'état"""
        return {
            "connected": self.connected,
            "host": self.host,
            "connection_type": "IC-R8600_Telnet_Final",
            "power_state": self.power_state,
            "frequency": self.current_frequency,
            "mode": self.current_mode,
            "firmware_compatible": "v1.33+"
        }

def test_telnet_final():
    """Test du contrôle Telnet final"""
    print("🎯 TEST CONTRÔLE TELNET FINAL IC-R8600")
    print("=" * 60)
    print("SOLUTION FINALE pour firmware v1.33")
    print("Utilise exclusivement Telnet (port 23)")
    print("=" * 60)
    
    # Configuration logging
    logging.basicConfig(level=logging.INFO)
    
    controller = ICR8600TelnetFinal("**************")
    
    try:
        # Connexion
        if controller.connect():
            print("✅ Connexion Telnet finale établie")
            
            # Test Power OFF
            print("\nTest Power OFF - OBSERVEZ L'IC-R8600 !")
            if controller.power_off():
                print("✅ Power OFF envoyé")
            else:
                print("❌ Échec Power OFF")
            
            time.sleep(5)
            
            # Test Power ON
            print("\nTest Power ON - OBSERVEZ L'IC-R8600 !")
            if controller.power_on():
                print("✅ Power ON envoyé")
            else:
                print("❌ Échec Power ON")
            
            time.sleep(5)
            
            # Test fréquence
            print("\nTest fréquence 145.500 MHz - OBSERVEZ L'AFFICHAGE !")
            if controller.set_frequency(145500000):
                print("✅ Fréquence envoyée")
            else:
                print("❌ Échec fréquence")
            
            time.sleep(3)
            
            # Test mode
            print("\nTest mode FM - OBSERVEZ L'AFFICHAGE !")
            if controller.set_mode("FM"):
                print("✅ Mode envoyé")
            else:
                print("❌ Échec mode")
            
            # Statut
            status = controller.get_status()
            print(f"\nStatut: {status}")
            
            controller.disconnect()
            print("\n✅ Test Telnet final terminé")
            
        else:
            print("❌ Échec connexion Telnet finale")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
    finally:
        controller.disconnect()

if __name__ == "__main__":
    test_telnet_final()
