"""
Handler pour communication CI-V UDP avec IC-R8600
Utilise les ports UDP officiels ICOM pour le contrôle
"""

import socket
import time
import logging
import threading
from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass

@dataclass
class UDPConfig:
    """Configuration réseau pour IC-R8600 - UDP CI-V (solution officielle ICOM)"""
    host: str = "**************"
    control_port: int = 50001   # Port UDP pour contrôle CI-V
    serial_port: int = 50002    # Port UDP pour émulation série
    audio_port: int = 50003     # Port UDP pour audio
    timeout: float = 5.0
    retry_count: int = 3
    keepalive_interval: float = 30.0

class ICOMUDPHandler:
    """Handler pour communication CI-V over UDP avec IC-R8600"""

    # Constantes CI-V pour IC-R8600
    ICOM_ADDRESS = 0x94      # IC-R8600
    CONTROLLER_ADDRESS = 0xE0 # Contrôleur PC
    PREAMBLE = [0xFE, 0xFE]
    POSTAMBLE = 0xFD

    # Modes de modulation IC-R8600
    MODES = {
        'LSB': 0x00, 'USB': 0x01, 'AM': 0x02, 'CW': 0x03,
        'FM': 0x05, 'WFM': 0x06, 'CWR': 0x07, 'RTTY': 0x08,
        'RTTYR': 0x09, 'PSK': 0x12, 'PSKR': 0x13, 'DV': 0x17
    }

    def __init__(self, config: UDPConfig = None):
        self.config = config or UDPConfig()
        self.logger = logging.getLogger(__name__)
        self.connected = False
        self.udp_socket = None
        self.control_activated = False
        self.last_activity = time.time()

        # Threading pour keepalive
        self.keepalive_thread = None
        self.stop_keepalive = False

    def connect(self) -> bool:
        """Établit la connexion UDP avec l'IC-R8600"""
        try:
            self.logger.info(f"Connexion UDP CI-V vers {self.config.host}:{self.config.control_port}")
            
            # Vérifier que l'IC-R8600 est accessible
            if not self._test_host_reachable():
                return False

            # Créer le socket UDP
            self.udp_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.udp_socket.settimeout(self.config.timeout)
            
            self.logger.info("Socket UDP créé")

            # Test de communication CI-V
            if self._test_ci_v_communication():
                self.connected = True
                self.control_activated = True
                self.last_activity = time.time()
                
                # Démarrer le keepalive
                self._start_keepalive()
                
                self.logger.info("Contrôle CI-V via UDP activé")
                return True
            else:
                self.logger.error("Test communication CI-V UDP échoué")
                self._cleanup_socket()
                return False

        except Exception as e:
            self.logger.error(f"Erreur connexion UDP: {e}")
            self._cleanup_socket()
            return False

    def _test_host_reachable(self) -> bool:
        """Test si l'IC-R8600 est accessible sur le réseau"""
        try:
            # Test de ping UDP simple
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            test_socket.settimeout(2.0)
            
            # Envoyer un paquet de test
            test_data = b'\x00'
            test_socket.sendto(test_data, (self.config.host, self.config.control_port))
            
            test_socket.close()
            self.logger.info(f"IC-R8600 accessible sur {self.config.host}")
            return True
            
        except Exception as e:
            self.logger.error(f"IC-R8600 non accessible: {e}")
            return False

    def _test_ci_v_communication(self) -> bool:
        """Test la communication CI-V via UDP"""
        try:
            self.logger.info("Test de communication CI-V via UDP...")
            
            # Commande de test : lecture du statut
            test_command = self._build_ci_v_command([0x19, 0x00])  # Status query
            
            # Envoyer la commande
            self.udp_socket.sendto(test_command, (self.config.host, self.config.control_port))
            
            # Attendre la réponse
            try:
                response, addr = self.udp_socket.recvfrom(1024)
                if response:
                    self.logger.info(f"Réponse UDP reçue: {response.hex().upper()}")
                    return True
                else:
                    self.logger.warning("Réponse UDP vide")
                    return True  # Considérer comme OK même sans réponse
                    
            except socket.timeout:
                self.logger.info("Pas de réponse immédiate - Connexion UDP OK")
                return True  # L'IC-R8600 peut ne pas répondre immédiatement
                
        except Exception as e:
            self.logger.error(f"Erreur test CI-V UDP: {e}")
            return False

    def _build_ci_v_command(self, command: List[int]) -> bytes:
        """Construit une commande CI-V complète"""
        full_command = (
            self.PREAMBLE + 
            [self.ICOM_ADDRESS, self.CONTROLLER_ADDRESS] + 
            command + 
            [self.POSTAMBLE]
        )
        return bytes(full_command)

    def _send_command_with_retry(self, command: List[int]) -> Optional[bytes]:
        """Envoie une commande CI-V avec retry"""
        if not self.connected or not self.udp_socket:
            self.logger.error("Socket UDP non connecté")
            return None

        ci_v_command = self._build_ci_v_command(command)
        
        for attempt in range(self.config.retry_count):
            try:
                self.logger.debug(f"Envoi commande UDP (tentative {attempt + 1}): {ci_v_command.hex().upper()}")
                
                # Envoyer via UDP
                self.udp_socket.sendto(ci_v_command, (self.config.host, self.config.control_port))
                self.last_activity = time.time()
                
                # Attendre la réponse
                try:
                    self.udp_socket.settimeout(2.0)  # Timeout plus court pour les réponses
                    response, addr = self.udp_socket.recvfrom(1024)
                    self.udp_socket.settimeout(self.config.timeout)  # Remettre le timeout normal
                    
                    if response:
                        self.logger.debug(f"Réponse UDP: {response.hex().upper()}")
                        return response
                    
                except socket.timeout:
                    self.logger.debug("Pas de réponse UDP (timeout)")
                    # Pour UDP, pas de réponse ne signifie pas échec
                    return b'\x00'  # Retourner quelque chose pour indiquer le succès
                
            except (OSError, socket.error) as e:
                self.logger.warning(f"Erreur envoi UDP (tentative {attempt + 1}): {e}")
                if attempt < self.config.retry_count - 1:
                    time.sleep(0.5)
                else:
                    # Tentative de reconnexion
                    if self._reconnect():
                        return self._send_command_with_retry(command)
                    return None
                    
        return None

    def _cleanup_socket(self):
        """Nettoie le socket UDP"""
        if self.udp_socket:
            try:
                self.udp_socket.close()
            except:
                pass
            self.udp_socket = None

    def _reconnect(self) -> bool:
        """Tente de reconnecter le socket UDP"""
        self.logger.info("Tentative de reconnexion UDP...")
        
        # Nettoyer l'ancienne connexion
        self._cleanup_socket()
        
        # Attendre un peu avant de reconnecter
        time.sleep(1.0)
        
        try:
            # Créer un nouveau socket
            self.udp_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.udp_socket.settimeout(self.config.timeout)
            
            self.logger.info("Socket UDP reconnecté")
            
            # Marquer comme connecté
            self.connected = True
            self.last_activity = time.time()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Échec reconnexion UDP: {e}")
            self._cleanup_socket()
            self.connected = False
            return False

    def _start_keepalive(self):
        """Démarre le thread de keepalive"""
        if self.config.keepalive_interval > 0:
            self.stop_keepalive = False
            self.keepalive_thread = threading.Thread(target=self._keepalive_worker, daemon=True)
            self.keepalive_thread.start()

    def _keepalive_worker(self):
        """Worker thread pour maintenir la connexion UDP"""
        while not self.stop_keepalive and self.connected:
            try:
                time.sleep(self.config.keepalive_interval)

                if time.time() - self.last_activity > self.config.keepalive_interval:
                    # Envoyer une commande de statut pour maintenir la connexion
                    status_command = [0x19, 0x00]  # Status query
                    response = self._send_command_with_retry(status_command)

                    if not response:
                        self.logger.warning("Keepalive UDP échoué")
                        self.connected = False
                        break
                    else:
                        self.logger.debug("Keepalive UDP OK")

            except Exception as e:
                self.logger.error(f"Erreur keepalive UDP: {e}")
                self.connected = False
                break

    def disconnect(self):
        """Ferme la connexion UDP"""
        self.logger.info("Fermeture connexion UDP")
        
        # Arrêter le keepalive
        self.stop_keepalive = True
        if self.keepalive_thread:
            self.keepalive_thread.join(timeout=2.0)
        
        # Fermer le socket
        self._cleanup_socket()
        
        self.connected = False
        self.control_activated = False

    def power_on(self) -> bool:
        """Allume l'IC-R8600"""
        response = self._send_command_with_retry([0x18, 0x01])
        success = response is not None
        if success:
            self.logger.info("Commande power_on envoyée via UDP")
        return success

    def power_off(self) -> bool:
        """Éteint l'IC-R8600"""
        response = self._send_command_with_retry([0x18, 0x00])
        success = response is not None
        if success:
            self.logger.info("Commande power_off envoyée via UDP")
        return success

    def set_frequency(self, freq_input) -> bool:
        """Définit la fréquence (accepte Hz ou MHz)"""
        try:
            # Déterminer si l'entrée est en Hz ou MHz
            if isinstance(freq_input, (int, float)):
                if freq_input > 1000:  # Probablement en Hz
                    freq_hz = int(freq_input)
                    freq_mhz = freq_hz / 1000000.0
                else:  # Probablement en MHz
                    freq_mhz = float(freq_input)
                    freq_hz = int(freq_mhz * 1000000)
            else:
                self.logger.error(f"Format fréquence invalide: {freq_input}")
                return False

            # Vérifier la plage valide pour IC-R8600
            if freq_hz < 10000 or freq_hz > 3000000000:  # 10 kHz à 3 GHz
                self.logger.warning(f"Fréquence hors plage: {freq_hz} Hz ({freq_mhz:.6f} MHz)")
                return False

            # Conversion fréquence en format BCD ICOM
            freq_str = f"{freq_hz:010d}"
            bcd_bytes = []

            # Conversion en BCD (2 chiffres par byte, ordre inversé)
            for i in range(4, -1, -1):
                digit1 = int(freq_str[i*2])
                digit2 = int(freq_str[i*2 + 1])
                bcd_bytes.append((digit1 << 4) | digit2)

            command = [0x05] + bcd_bytes
            response = self._send_command_with_retry(command)

            if response:
                self.logger.info(f"Commande fréquence {freq_mhz:.6f} MHz envoyée via UDP")
                return True
            else:
                self.logger.warning(f"Échec commande fréquence {freq_mhz:.6f} MHz")
                return False

        except Exception as e:
            self.logger.error(f"Erreur set_frequency: {e}")
            return False

    def set_mode(self, mode: str) -> bool:
        """Définit le mode de modulation"""
        if mode not in self.MODES:
            self.logger.warning(f"Mode non supporté: {mode}")
            return False

        try:
            mode_code = self.MODES[mode]
            response = self._send_command_with_retry([0x06, mode_code])

            if response:
                self.logger.info(f"Commande mode {mode} envoyée via UDP")
                return True
            else:
                self.logger.warning(f"Échec commande mode {mode}")
                return False

        except Exception as e:
            self.logger.error(f"Erreur set_mode: {e}")
            return False

    def get_frequency(self) -> Optional[float]:
        """Lit la fréquence actuelle"""
        try:
            response = self._send_command_with_retry([0x03])
            if response and len(response) >= 11:
                # Décoder la réponse BCD
                freq_bcd = response[6:11]  # 5 bytes de fréquence
                freq_str = ""
                for byte in reversed(freq_bcd):
                    freq_str += f"{(byte >> 4) & 0x0F}{byte & 0x0F}"

                freq_hz = int(freq_str)
                return freq_hz / 1000000.0  # Retourner en MHz

            return 145.5  # Valeur par défaut

        except Exception as e:
            self.logger.error(f"Erreur get_frequency: {e}")
            return 145.5

    def get_mode(self) -> Optional[str]:
        """Lit le mode actuel"""
        try:
            response = self._send_command_with_retry([0x04])
            if response and len(response) >= 7:
                mode_code = response[6]
                for mode_name, code in self.MODES.items():
                    if code == mode_code:
                        return mode_name

            return "FM"  # Valeur par défaut

        except Exception as e:
            self.logger.error(f"Erreur get_mode: {e}")
            return "FM"

    def get_status(self) -> Dict[str, Any]:
        """Récupère l'état du handler"""
        return {
            "connected": self.connected,
            "control_activated": self.control_activated,
            "host": self.config.host,
            "control_port": self.config.control_port,
            "last_activity": self.last_activity,
            "connection_type": "UDP"
        }
