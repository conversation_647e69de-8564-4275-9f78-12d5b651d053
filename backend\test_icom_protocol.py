#!/usr/bin/env python3
"""
Test du protocole réseau ICOM pour IC-R8600
"""

import time
import logging
from icom_network_protocol import ICOMNetworkProtocol, ICOMNetworkConfig

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_icom_protocol():
    """Test du protocole réseau ICOM"""
    print("=" * 50)
    print("TEST PROTOCOLE RÉSEAU ICOM POUR IC-R8600")
    print("=" * 50)
    print("Assurez-vous que:")
    print("1. L'IC-R8600 est allumé")
    print("2. Network Control est activé (MENU → SET → Network → Network Control: ON)")
    print("3. L'IC-R8600 a été redémarré après activation")
    print("4. L'IP ************** est correcte")
    print("=" * 50)
    
    # Configuration
    config = ICOMNetworkConfig(
        host="**************",
        control_port=50001,
        serial_port=50002,
        audio_port=50003
    )
    
    # Création du handler
    icom = ICOMNetworkProtocol(config)
    
    try:
        # Connexion
        print("\n1. Test de connexion au protocole ICOM...")
        if icom.connect():
            print("✅ Connexion réussie !")
        else:
            print("❌ Échec de connexion")
            return
        
        # Test Power OFF
        print("\n2. Test Power OFF...")
        print("   OBSERVEZ L'IC-R8600 !")
        if icom.power_off():
            print("✅ Commande Power OFF envoyée")
        else:
            print("❌ Échec commande Power OFF")
        
        # Attente pour observation
        print("   Attente 5 secondes...")
        time.sleep(5)
        
        # Test Power ON
        print("\n3. Test Power ON...")
        print("   OBSERVEZ L'IC-R8600 !")
        if icom.power_on():
            print("✅ Commande Power ON envoyée")
        else:
            print("❌ Échec commande Power ON")
        
        # Attente pour observation
        print("   Attente 5 secondes...")
        time.sleep(5)
        
        # Test fréquence
        print("\n4. Test changement fréquence à 145.500 MHz...")
        print("   OBSERVEZ L'AFFICHAGE DE L'IC-R8600 !")
        if icom.set_frequency(145500000):
            print("✅ Commande fréquence envoyée")
        else:
            print("❌ Échec commande fréquence")
        
        # Attente pour observation
        print("   Attente 5 secondes...")
        time.sleep(5)
        
        # Test mode
        print("\n5. Test changement mode à FM...")
        print("   OBSERVEZ L'AFFICHAGE DE L'IC-R8600 !")
        if icom.set_mode("FM"):
            print("✅ Commande mode envoyée")
        else:
            print("❌ Échec commande mode")
        
        # Attente pour observation
        print("   Attente 5 secondes...")
        time.sleep(5)
        
        # Déconnexion
        print("\n6. Déconnexion...")
        icom.disconnect()
        print("✅ Déconnexion effectuée")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
    finally:
        # Nettoyage
        if icom.connected:
            icom.disconnect()

def test_alternative_protocol():
    """Test avec des paramètres alternatifs"""
    print("\n" + "=" * 50)
    print("TEST PROTOCOLE ALTERNATIF")
    print("=" * 50)
    
    # Configurations alternatives à tester
    configs = [
        # Test avec port 50000
        ICOMNetworkConfig(host="**************", control_port=50000, serial_port=50000),
        
        # Test avec port 50001 mais sans authentification
        ICOMNetworkConfig(host="**************", control_port=50001, serial_port=50001),
        
        # Test avec port 23 (Telnet)
        ICOMNetworkConfig(host="**************", control_port=23, serial_port=23),
    ]
    
    for i, config in enumerate(configs):
        print(f"\nTest configuration alternative {i+1}:")
        print(f"Host: {config.host}, Control: {config.control_port}, Serial: {config.serial_port}")
        
        icom = ICOMNetworkProtocol(config)
        
        try:
            if icom.connect():
                print("✅ Connexion réussie !")
                
                # Test Power OFF
                print("Test Power OFF...")
                if icom.power_off():
                    print("✅ Commande Power OFF envoyée")
                else:
                    print("❌ Échec commande Power OFF")
                
                time.sleep(3)
                icom.disconnect()
            else:
                print("❌ Échec de connexion")
                
        except Exception as e:
            print(f"❌ Erreur: {e}")
        finally:
            if icom.connected:
                icom.disconnect()

if __name__ == "__main__":
    print("DIAGNOSTIC PROTOCOLE RÉSEAU ICOM POUR IC-R8600")
    print("Observez attentivement l'IC-R8600 pendant les tests !")
    
    # Test principal
    test_icom_protocol()
    
    # Test alternatif
    input("\nAppuyez sur Entrée pour tester des configurations alternatives...")
    test_alternative_protocol()
    
    print("\n" + "=" * 50)
    print("DIAGNOSTIC TERMINÉ")
    print("=" * 50)
    print("Si aucune commande n'a eu d'effet :")
    print("1. Vérifiez que Network Control est ON sur l'IC-R8600")
    print("2. Redémarrez l'IC-R8600 après avoir activé Network Control")
    print("3. Vérifiez que l'IP ************** est correcte")
    print("4. Consultez la documentation ICOM pour le protocole exact")
