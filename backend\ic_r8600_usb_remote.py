#!/usr/bin/env python3
"""
Contrôle IC-R8600 via USB distant (VirtualHere)
Module FastAPI optimisé pour liaison FH avec gestion robuste
"""

import serial
import serial.tools.list_ports
import time
import logging
import threading
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
import asyncio
from contextlib import asynccontextmanager

@dataclass
class ICR8600Status:
    """État de l'IC-R8600"""
    connected: bool = False
    port: Optional[str] = None
    frequency: int = 145500000  # Hz
    mode: str = "FM"
    signal_strength: int = 0
    rssi: float = -120.0
    last_update: float = 0.0

class ICR8600USBRemote:
    """Contrôle IC-R8600 via USB distant (VirtualHere)"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.serial_connection = None
        self.status = ICR8600Status()
        self.connection_lock = threading.Lock()
        
        # Configuration série optimisée pour FH
        self.BAUD_RATE = 19200
        self.TIMEOUT = 3.0  # Timeout plus long pour FH
        self.RETRY_COUNT = 3
        self.RETRY_DELAY = 1.0
        
        # Adresses CI-V
        self.ICOM_ADDRESS = 0x94
        self.CONTROLLER_ADDRESS = 0xE0
        
        # Modes supportés
        self.modes = {
            'LSB': 0x00, 'USB': 0x01, 'AM': 0x02, 'CW': 0x03,
            'FM': 0x05, 'WFM': 0x06, 'CWR': 0x07, 'RTTY': 0x08
        }
        
        # Thread de monitoring
        self.monitor_thread = None
        self.stop_monitor = False
    
    def find_icom_port(self) -> Optional[str]:
        """Trouve automatiquement le port COM de l'IC-R8600"""
        try:
            ports = serial.tools.list_ports.comports()
            
            # Recherche prioritaire par description
            for port in ports:
                desc = port.description.upper()
                if any(keyword in desc for keyword in ['ICOM', 'IC-R8600', 'R8600']):
                    self.logger.info(f"Port IC-R8600 trouvé: {port.device} ({port.description})")
                    return port.device
            
            # Recherche secondaire par USB Serial
            for port in ports:
                desc = port.description.upper()
                if 'USB SERIAL' in desc or 'SERIAL PORT' in desc:
                    self.logger.info(f"Port USB Serial trouvé: {port.device} ({port.description})")
                    return port.device
            
            self.logger.warning("Aucun port IC-R8600 détecté")
            return None
            
        except Exception as e:
            self.logger.error(f"Erreur recherche port: {e}")
            return None
    
    def connect(self, port: Optional[str] = None) -> bool:
        """Connexion robuste avec gestion FH"""
        with self.connection_lock:
            try:
                # Auto-détection du port si non spécifié
                if not port:
                    port = self.find_icom_port()
                    if not port:
                        return False
                
                # Fermer connexion existante
                self.disconnect()
                
                # Tentatives de connexion avec retry
                for attempt in range(self.RETRY_COUNT):
                    try:
                        self.logger.info(f"Connexion IC-R8600 sur {port} (tentative {attempt + 1})")
                        
                        self.serial_connection = serial.Serial(
                            port=port,
                            baudrate=self.BAUD_RATE,
                            timeout=self.TIMEOUT,
                            parity=serial.PARITY_NONE,
                            stopbits=serial.STOPBITS_ONE,
                            bytesize=serial.EIGHTBITS
                        )
                        
                        # Test de communication
                        if self._test_communication():
                            self.status.connected = True
                            self.status.port = port
                            self.status.last_update = time.time()
                            
                            # Démarrer monitoring
                            self._start_monitoring()
                            
                            self.logger.info(f"IC-R8600 connecté sur {port}")
                            return True
                        else:
                            self.serial_connection.close()
                            self.serial_connection = None
                            
                    except Exception as e:
                        self.logger.warning(f"Tentative {attempt + 1} échouée: {e}")
                        if self.serial_connection:
                            try:
                                self.serial_connection.close()
                            except:
                                pass
                            self.serial_connection = None
                        
                        if attempt < self.RETRY_COUNT - 1:
                            time.sleep(self.RETRY_DELAY)
                
                self.logger.error("Échec connexion IC-R8600 après toutes les tentatives")
                return False
                
            except Exception as e:
                self.logger.error(f"Erreur connexion: {e}")
                return False
    
    def _test_communication(self) -> bool:
        """Test de communication CI-V"""
        try:
            if not self.serial_connection:
                return False
            
            # Vider les buffers
            self.serial_connection.reset_input_buffer()
            self.serial_connection.reset_output_buffer()
            
            # Commande de test (lecture fréquence)
            test_cmd = self._build_ci_v_command([0x03])
            self.serial_connection.write(test_cmd)
            
            # Attendre réponse
            time.sleep(0.5)
            response = self.serial_connection.read(20)
            
            if len(response) >= 6:  # Réponse CI-V minimale
                self.logger.info(f"Test communication réussi: {response.hex().upper()}")
                return True
            else:
                self.logger.warning("Pas de réponse au test de communication")
                return False
                
        except Exception as e:
            self.logger.error(f"Erreur test communication: {e}")
            return False
    
    def _build_ci_v_command(self, command: List[int], data: List[int] = None) -> bytes:
        """Construit une commande CI-V"""
        cmd = [0xFE, 0xFE, self.ICOM_ADDRESS, self.CONTROLLER_ADDRESS]
        cmd.extend(command)
        if data:
            cmd.extend(data)
        cmd.append(0xFD)
        return bytes(cmd)
    
    def _send_command(self, command: List[int], data: List[int] = None) -> Optional[bytes]:
        """Envoie une commande CI-V avec gestion robuste FH"""
        with self.connection_lock:
            if not self.serial_connection or not self.status.connected:
                # Tentative de reconnexion automatique
                if not self.connect():
                    return None
            
            for attempt in range(self.RETRY_COUNT):
                try:
                    # Construire et envoyer commande
                    ci_v_cmd = self._build_ci_v_command(command, data)
                    
                    self.serial_connection.reset_input_buffer()
                    self.serial_connection.write(ci_v_cmd)
                    
                    # Attendre réponse
                    time.sleep(0.3)
                    response = self.serial_connection.read(50)
                    
                    if response:
                        self.status.last_update = time.time()
                        self.logger.debug(f"Commande envoyée: {ci_v_cmd.hex().upper()}")
                        self.logger.debug(f"Réponse reçue: {response.hex().upper()}")
                        return response
                    
                except Exception as e:
                    self.logger.warning(f"Erreur envoi commande (tentative {attempt + 1}): {e}")
                    
                    # Reconnexion si erreur de communication
                    if "device reports readiness" in str(e) or "access is denied" in str(e):
                        self.status.connected = False
                        if attempt < self.RETRY_COUNT - 1:
                            time.sleep(self.RETRY_DELAY)
                            self.connect()
                    
                    if attempt < self.RETRY_COUNT - 1:
                        time.sleep(0.5)
            
            return None
    
    def disconnect(self):
        """Déconnexion propre"""
        with self.connection_lock:
            self.stop_monitor = True
            
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=2.0)
            
            if self.serial_connection:
                try:
                    self.serial_connection.close()
                except:
                    pass
                self.serial_connection = None
            
            self.status.connected = False
            self.status.port = None
            self.logger.info("IC-R8600 déconnecté")
    
    def _start_monitoring(self):
        """Démarre le monitoring de connexion"""
        self.stop_monitor = False
        self.monitor_thread = threading.Thread(target=self._monitor_connection, daemon=True)
        self.monitor_thread.start()
    
    def _monitor_connection(self):
        """Monitoring de connexion pour FH"""
        while not self.stop_monitor and self.status.connected:
            try:
                time.sleep(10)  # Check toutes les 10 secondes
                
                # Test de connectivité périodique
                if time.time() - self.status.last_update > 30:  # 30s sans activité
                    if not self._test_communication():
                        self.logger.warning("Perte de connexion détectée, reconnexion...")
                        self.status.connected = False
                        self.connect(self.status.port)
                
            except Exception as e:
                self.logger.error(f"Erreur monitoring: {e}")
                break
    
    # API Methods
    def set_frequency(self, freq_hz: int) -> bool:
        """Définit la fréquence"""
        try:
            # Conversion en BCD
            freq_str = f"{freq_hz:010d}"
            bcd_bytes = []
            for i in range(4, -1, -1):
                digit1 = int(freq_str[i*2])
                digit2 = int(freq_str[i*2 + 1])
                bcd_bytes.append((digit1 << 4) | digit2)
            
            response = self._send_command([0x05], bcd_bytes)
            if response:
                self.status.frequency = freq_hz
                self.logger.info(f"Fréquence définie: {freq_hz / 1000000:.6f} MHz")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Erreur set_frequency: {e}")
            return False
    
    def get_frequency(self) -> Optional[int]:
        """Lit la fréquence actuelle"""
        try:
            response = self._send_command([0x03])
            if response and len(response) >= 11:
                # Décoder BCD de la réponse
                bcd_data = response[5:10]
                freq_str = ""
                for byte in reversed(bcd_data):
                    freq_str += f"{(byte >> 4) & 0x0F}{byte & 0x0F}"
                
                freq_hz = int(freq_str)
                self.status.frequency = freq_hz
                return freq_hz
            
            return self.status.frequency
            
        except Exception as e:
            self.logger.error(f"Erreur get_frequency: {e}")
            return self.status.frequency
    
    def set_mode(self, mode: str) -> bool:
        """Définit le mode"""
        if mode not in self.modes:
            return False
        
        try:
            response = self._send_command([0x06], [self.modes[mode]])
            if response:
                self.status.mode = mode
                self.logger.info(f"Mode défini: {mode}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Erreur set_mode: {e}")
            return False
    
    def get_signal_strength(self) -> int:
        """Lit la force du signal (S-meter)"""
        try:
            response = self._send_command([0x15, 0x02])
            if response and len(response) >= 8:
                # Décoder S-meter
                s_value = response[6] if len(response) > 6 else 0
                self.status.signal_strength = s_value
                return s_value
            
            return self.status.signal_strength
            
        except Exception as e:
            self.logger.error(f"Erreur get_signal_strength: {e}")
            return self.status.signal_strength
    
    def get_rssi(self) -> float:
        """Calcule RSSI approximatif"""
        s_meter = self.get_signal_strength()
        # Conversion approximative S-meter vers RSSI
        rssi = -120 + (s_meter * 6)  # Approximation
        self.status.rssi = rssi
        return rssi
    
    def get_status(self) -> Dict[str, Any]:
        """Récupère l'état complet"""
        return {
            "connected": self.status.connected,
            "port": self.status.port,
            "connection_type": "USB_Remote_VirtualHere",
            "frequency": self.status.frequency,
            "mode": self.status.mode,
            "signal_strength": self.status.signal_strength,
            "rssi": self.status.rssi,
            "last_update": self.status.last_update,
            "uptime": time.time() - self.status.last_update if self.status.connected else 0
        }

# Instance globale
ic_r8600 = ICR8600USBRemote()

@asynccontextmanager
async def lifespan(app):
    """Gestion du cycle de vie de l'application"""
    # Startup
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    logger.info("Démarrage contrôle IC-R8600 USB distant")
    
    # Tentative de connexion automatique
    if ic_r8600.connect():
        logger.info("IC-R8600 connecté automatiquement")
    else:
        logger.warning("IC-R8600 non connecté au démarrage")
    
    yield
    
    # Shutdown
    ic_r8600.disconnect()
    logger.info("Arrêt contrôle IC-R8600")
