#!/usr/bin/env python3
"""
Implémentation du VRAI protocole ICOM RS-BA1
Basé sur le reverse engineering de kappanhang et NetworkIcom
Compatible avec IC-R8600 via câble RJ45
"""

import socket
import time
import struct
import threading
import random
import hashlib
import logging
from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass

@dataclass
class RSBA1Config:
    """Configuration pour le protocole RS-BA1 réel"""
    host: str = "**************"
    control_port: int = 50001
    serial_port: int = 50002
    audio_port: int = 50003
    username: str = "beer"
    password: str = "beerbeer"
    timeout: float = 5.0

class ICOM_RSBA1_Real:
    """Implémentation du VRAI protocole ICOM RS-BA1"""
    
    # Constantes du protocole RS-BA1 (reverse engineered)
    MAGIC_HEADER = b'\x00\x00\x00\x00'
    
    # Types de paquets (basés sur kappanhang)
    PKT_TYPE_ARE_YOU_THERE = 0x03
    PKT_TYPE_I_AM_HERE = 0x04
    PKT_TYPE_ARE_YOU_READY = 0x06
    PKT_TYPE_I_AM_READY = 0x07
    PKT_TYPE_LOGIN = 0x80
    PKT_TYPE_LOGIN_RESPONSE = 0x81
    PKT_TYPE_TOKEN_ACK = 0x82
    PKT_TYPE_CAPABILITIES = 0x83
    PKT_TYPE_CONN_INFO = 0x90
    PKT_TYPE_DISCONNECT = 0x05
    PKT_TYPE_PING = 0x07
    PKT_TYPE_IDLE = 0x01
    PKT_TYPE_RETRANSMIT_REQUEST = 0x01
    PKT_TYPE_CI_V = 0xc1
    PKT_TYPE_OPEN_CLOSE = 0x05
    
    # Constantes CI-V
    ICOM_ADDRESS = 0x94  # IC-R8600
    CONTROLLER_ADDRESS = 0xE0
    
    def __init__(self, config: RSBA1Config = None):
        self.config = config or RSBA1Config()
        self.logger = logging.getLogger(__name__)
        
        # Sockets
        self.control_socket = None
        self.serial_socket = None
        
        # État de la connexion
        self.connected = False
        self.logged_in = False
        
        # IDs de session (générés aléatoirement)
        self.my_id = random.randint(0x10000000, 0xFFFFFFFF)
        self.radio_id = None
        self.token = None
        
        # Séquences
        self.control_seq = 0
        self.serial_seq = 0
        self.ping_seq = 0
        
        # Threading
        self.ping_thread = None
        self.stop_ping = False

    def connect(self) -> bool:
        """Connexion complète selon le protocole RS-BA1 réel"""
        try:
            self.logger.info(f"Connexion protocole RS-BA1 réel vers {self.config.host}")
            
            # Étape 1: Connexion Control Port
            if not self._connect_control_port():
                return False
            
            # Étape 2: Connexion Serial Port
            if not self._connect_serial_port():
                return False
            
            # Étape 3: Démarrer le ping
            self._start_ping()
            
            self.connected = True
            self.logger.info("Protocole RS-BA1 réel activé - IC-R8600 contrôlable")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur connexion RS-BA1 réel: {e}")
            self.disconnect()
            return False

    def _connect_control_port(self) -> bool:
        """Connexion au port de contrôle selon le protocole réel"""
        try:
            self.control_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.control_socket.settimeout(self.config.timeout)
            
            # Étape 1: Are You There?
            if not self._handshake_are_you_there():
                return False
            
            # Étape 2: Are You Ready?
            if not self._handshake_are_you_ready():
                return False
            
            # Étape 3: Login
            if not self._perform_login():
                return False
            
            self.logger.info("Port de contrôle RS-BA1 connecté")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur connexion port contrôle: {e}")
            return False

    def _handshake_are_you_there(self) -> bool:
        """Handshake Are You There / I Am Here"""
        try:
            # Construire le paquet Are You There
            packet = self._build_control_packet(
                self.PKT_TYPE_ARE_YOU_THERE,
                struct.pack('<I', self.my_id)
            )
            
            self.logger.debug(f"Envoi Are You There: {packet.hex().upper()}")
            
            # Envoyer jusqu'à recevoir une réponse
            for attempt in range(5):
                self.control_socket.sendto(packet, (self.config.host, self.config.control_port))
                
                try:
                    response, addr = self.control_socket.recvfrom(1024)
                    self.logger.debug(f"Réponse I Am Here: {response.hex().upper()}")
                    
                    if len(response) >= 16 and response[4] == self.PKT_TYPE_I_AM_HERE:
                        # Extraire l'ID radio
                        self.radio_id = struct.unpack('<I', response[8:12])[0]
                        self.logger.info(f"Radio ID obtenu: {self.radio_id:08X}")
                        return True
                        
                except socket.timeout:
                    self.logger.debug(f"Timeout Are You There (tentative {attempt+1})")
                    continue
            
            self.logger.error("Échec handshake Are You There")
            return False
            
        except Exception as e:
            self.logger.error(f"Erreur Are You There: {e}")
            return False

    def _handshake_are_you_ready(self) -> bool:
        """Handshake Are You Ready / I Am Ready"""
        try:
            # Construire le paquet Are You Ready
            packet = self._build_control_packet(
                self.PKT_TYPE_ARE_YOU_READY,
                struct.pack('<II', self.my_id, self.radio_id)
            )
            
            self.logger.debug(f"Envoi Are You Ready: {packet.hex().upper()}")
            
            # Envoyer jusqu'à recevoir une réponse
            for attempt in range(5):
                self.control_socket.sendto(packet, (self.config.host, self.config.control_port))
                
                try:
                    response, addr = self.control_socket.recvfrom(1024)
                    self.logger.debug(f"Réponse I Am Ready: {response.hex().upper()}")
                    
                    if len(response) >= 16 and response[4] == self.PKT_TYPE_I_AM_READY:
                        self.logger.info("Handshake Are You Ready réussi")
                        return True
                        
                except socket.timeout:
                    self.logger.debug(f"Timeout Are You Ready (tentative {attempt+1})")
                    continue
            
            self.logger.error("Échec handshake Are You Ready")
            return False
            
        except Exception as e:
            self.logger.error(f"Erreur Are You Ready: {e}")
            return False

    def _perform_login(self) -> bool:
        """Authentification selon le protocole RS-BA1"""
        try:
            # Générer un token request
            token_request = random.randint(0x0000, 0xFFFF)
            
            # Encoder le nom d'utilisateur et mot de passe
            username_encoded = self._encode_credentials(self.config.username)
            password_encoded = self._encode_credentials(self.config.password)
            
            # Construire le paquet de login
            login_data = bytearray()
            login_data.extend(struct.pack('<I', self.my_id))
            login_data.extend(struct.pack('<I', self.radio_id))
            login_data.extend(struct.pack('<H', token_request))
            login_data.extend(struct.pack('<H', len(username_encoded)))
            login_data.extend(username_encoded)
            login_data.extend(struct.pack('<H', len(password_encoded)))
            login_data.extend(password_encoded)
            login_data.extend(b'kappanhang\x00')  # Nom du client
            
            packet = self._build_control_packet(self.PKT_TYPE_LOGIN, bytes(login_data))
            
            self.logger.debug(f"Envoi Login: {packet.hex().upper()}")
            self.control_socket.sendto(packet, (self.config.host, self.config.control_port))
            
            # Attendre la réponse de login
            try:
                response, addr = self.control_socket.recvfrom(1024)
                self.logger.debug(f"Réponse Login: {response.hex().upper()}")
                
                if len(response) >= 20 and response[4] == self.PKT_TYPE_LOGIN_RESPONSE:
                    # Extraire le token
                    self.token = struct.unpack('<I', response[16:20])[0]
                    self.logger.info(f"Token obtenu: {self.token:08X}")
                    
                    # Envoyer Token ACK
                    return self._send_token_ack()
                else:
                    self.logger.error("Format de réponse login invalide")
                    return False
                    
            except socket.timeout:
                self.logger.error("Timeout login")
                return False
                
        except Exception as e:
            self.logger.error(f"Erreur login: {e}")
            return False

    def _send_token_ack(self) -> bool:
        """Envoie l'accusé de réception du token"""
        try:
            ack_data = struct.pack('<III', self.my_id, self.radio_id, self.token)
            packet = self._build_control_packet(self.PKT_TYPE_TOKEN_ACK, ack_data)
            
            self.logger.debug(f"Envoi Token ACK: {packet.hex().upper()}")
            self.control_socket.sendto(packet, (self.config.host, self.config.control_port))
            
            # Attendre les paquets Capabilities et ConnInfo
            for _ in range(2):
                try:
                    response, addr = self.control_socket.recvfrom(1024)
                    self.logger.debug(f"Réponse post-ACK: {response.hex().upper()}")
                    
                    if response[4] == self.PKT_TYPE_CAPABILITIES:
                        self.logger.info("Capabilities reçues")
                    elif response[4] == self.PKT_TYPE_CONN_INFO:
                        self.logger.info("ConnInfo reçu")
                        
                except socket.timeout:
                    pass
            
            self.logged_in = True
            self.logger.info("Login RS-BA1 réussi")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur Token ACK: {e}")
            return False

    def _encode_credentials(self, credential: str) -> bytes:
        """Encode les credentials selon l'algorithme RS-BA1"""
        # Algorithme d'encodage basé sur kappanhang
        encoded = bytearray()
        for i, char in enumerate(credential.encode('utf-8')):
            encoded.append(char ^ (i + 1))
        return bytes(encoded)

    def _build_control_packet(self, packet_type: int, data: bytes) -> bytes:
        """Construit un paquet de contrôle RS-BA1"""
        self.control_seq += 1
        
        packet = bytearray(self.MAGIC_HEADER)  # Magic header
        packet.append(packet_type)  # Type
        packet.extend(b'\x00\x00\x00')  # Padding
        packet.extend(struct.pack('<I', self.my_id))  # My ID
        packet.extend(struct.pack('<I', self.radio_id or 0))  # Radio ID
        packet.extend(struct.pack('<H', self.control_seq))  # Sequence
        packet.extend(b'\x00\x00')  # Padding
        packet.extend(data)  # Data
        
        return bytes(packet)

    def _connect_serial_port(self) -> bool:
        """Connexion au port série selon le protocole réel"""
        try:
            self.serial_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.serial_socket.settimeout(self.config.timeout)

            # Handshake série identique au contrôle
            if not self._serial_handshake():
                return False

            # Ouvrir le canal série
            if not self._open_serial_channel():
                return False

            # Envoyer ConnInfo pour activer CI-V
            if not self._send_serial_conn_info():
                return False

            self.logger.info("Port série RS-BA1 connecté")
            return True

        except Exception as e:
            self.logger.error(f"Erreur connexion port série: {e}")
            return False

    def _serial_handshake(self) -> bool:
        """Handshake série Are You There / Are You Ready"""
        try:
            # Are You There série
            packet = self._build_serial_packet(
                self.PKT_TYPE_ARE_YOU_THERE,
                struct.pack('<I', self.my_id)
            )

            for attempt in range(5):
                self.serial_socket.sendto(packet, (self.config.host, self.config.serial_port))

                try:
                    response, addr = self.serial_socket.recvfrom(1024)
                    if len(response) >= 16 and response[4] == self.PKT_TYPE_I_AM_HERE:
                        break
                except socket.timeout:
                    continue
            else:
                return False

            # Are You Ready série
            packet = self._build_serial_packet(
                self.PKT_TYPE_ARE_YOU_READY,
                struct.pack('<II', self.my_id, self.radio_id)
            )

            for attempt in range(5):
                self.serial_socket.sendto(packet, (self.config.host, self.config.serial_port))

                try:
                    response, addr = self.serial_socket.recvfrom(1024)
                    if len(response) >= 16 and response[4] == self.PKT_TYPE_I_AM_READY:
                        return True
                except socket.timeout:
                    continue

            return False

        except Exception as e:
            self.logger.error(f"Erreur handshake série: {e}")
            return False

    def _open_serial_channel(self) -> bool:
        """Ouvre le canal série"""
        try:
            # Paquet Open/Close avec Open Request
            open_data = struct.pack('<III', self.my_id, self.radio_id, 0x01)  # 0x01 = Open
            packet = self._build_serial_packet(self.PKT_TYPE_OPEN_CLOSE, open_data)

            self.serial_socket.sendto(packet, (self.config.host, self.config.serial_port))
            self.logger.info("Canal série ouvert")
            return True

        except Exception as e:
            self.logger.error(f"Erreur ouverture canal série: {e}")
            return False

    def _send_serial_conn_info(self) -> bool:
        """Envoie ConnInfo série pour activer CI-V (crucial !)"""
        try:
            # ConnInfo série - CRUCIAL pour que CI-V fonctionne
            conn_info_data = struct.pack('<III', self.my_id, self.radio_id, self.token)
            packet = self._build_serial_packet(self.PKT_TYPE_CONN_INFO, conn_info_data)

            self.serial_socket.sendto(packet, (self.config.host, self.config.serial_port))
            self.logger.info("ConnInfo série envoyé - CI-V activé")
            return True

        except Exception as e:
            self.logger.error(f"Erreur ConnInfo série: {e}")
            return False

    def _build_serial_packet(self, packet_type: int, data: bytes) -> bytes:
        """Construit un paquet série RS-BA1"""
        self.serial_seq += 1

        packet = bytearray(self.MAGIC_HEADER)  # Magic header
        packet.append(packet_type)  # Type
        packet.extend(b'\x00\x00\x00')  # Padding
        packet.extend(struct.pack('<I', self.my_id))  # My ID
        packet.extend(struct.pack('<I', self.radio_id))  # Radio ID
        packet.extend(struct.pack('<H', self.serial_seq))  # Sequence
        packet.extend(b'\x00\x00')  # Padding
        packet.extend(data)  # Data

        return bytes(packet)

    def _send_ci_v_command(self, ci_v_command: List[int]) -> Optional[bytes]:
        """Envoie une commande CI-V via le protocole RS-BA1 réel"""
        if not self.connected or not self.logged_in:
            return None

        try:
            # Construire la commande CI-V complète
            ci_v_data = bytes([0xFE, 0xFE, self.ICOM_ADDRESS, self.CONTROLLER_ADDRESS] +
                             ci_v_command + [0xFD])

            # Construire le paquet CI-V RS-BA1
            ci_v_packet_data = bytearray()
            ci_v_packet_data.extend(struct.pack('<I', self.my_id))
            ci_v_packet_data.extend(struct.pack('<I', self.radio_id))
            ci_v_packet_data.extend(struct.pack('<H', self.serial_seq + 1))
            ci_v_packet_data.extend(b'\x00\x00')  # Padding
            ci_v_packet_data.append(self.PKT_TYPE_CI_V)  # Type CI-V
            ci_v_packet_data.extend(struct.pack('<H', len(ci_v_data)))  # Longueur CI-V
            ci_v_packet_data.extend(struct.pack('<H', self.serial_seq + 1))  # Séquence CI-V
            ci_v_packet_data.extend(ci_v_data)  # Données CI-V

            packet = self._build_serial_packet(self.PKT_TYPE_CI_V, bytes(ci_v_packet_data))

            self.logger.debug(f"Envoi CI-V RS-BA1: {packet.hex().upper()}")
            self.serial_socket.sendto(packet, (self.config.host, self.config.serial_port))

            # Attendre la réponse
            try:
                response, addr = self.serial_socket.recvfrom(1024)
                self.logger.debug(f"Réponse CI-V RS-BA1: {response.hex().upper()}")
                return response
            except socket.timeout:
                return b'\x00'  # Succès sans réponse

        except Exception as e:
            self.logger.error(f"Erreur CI-V RS-BA1: {e}")
            return None

    def _start_ping(self):
        """Démarre le thread de ping"""
        self.stop_ping = False
        self.ping_thread = threading.Thread(target=self._ping_worker, daemon=True)
        self.ping_thread.start()

    def _ping_worker(self):
        """Worker thread pour maintenir la connexion avec ping"""
        while not self.stop_ping and self.connected:
            try:
                time.sleep(10)  # Ping toutes les 10 secondes

                # Ping contrôle
                ping_data = struct.pack('<III', self.my_id, self.radio_id, self.ping_seq)
                ping_packet = self._build_control_packet(self.PKT_TYPE_PING, ping_data)
                self.control_socket.sendto(ping_packet, (self.config.host, self.config.control_port))

                # Ping série
                ping_packet = self._build_serial_packet(self.PKT_TYPE_PING, ping_data)
                self.serial_socket.sendto(ping_packet, (self.config.host, self.config.serial_port))

                self.ping_seq += 1
                self.logger.debug("Ping RS-BA1 envoyé")

            except Exception as e:
                self.logger.error(f"Erreur ping: {e}")
                break

    def disconnect(self):
        """Déconnexion complète RS-BA1"""
        self.logger.info("Déconnexion protocole RS-BA1 réel")

        # Arrêter le ping
        self.stop_ping = True
        if self.ping_thread:
            self.ping_thread.join(timeout=2.0)

        # Fermer le canal série
        if self.serial_socket and self.connected:
            try:
                close_data = struct.pack('<III', self.my_id, self.radio_id, 0x00)  # 0x00 = Close
                close_packet = self._build_serial_packet(self.PKT_TYPE_OPEN_CLOSE, close_data)
                self.serial_socket.sendto(close_packet, (self.config.host, self.config.serial_port))
            except:
                pass

        # Déconnexion contrôle
        if self.control_socket and self.connected:
            try:
                disconnect_data = struct.pack('<III', self.my_id, self.radio_id, self.token or 0)
                disconnect_packet = self._build_control_packet(self.PKT_TYPE_DISCONNECT, disconnect_data)
                self.control_socket.sendto(disconnect_packet, (self.config.host, self.config.control_port))
            except:
                pass

        # Fermer les sockets
        for sock in [self.control_socket, self.serial_socket]:
            if sock:
                try:
                    sock.close()
                except:
                    pass

        self.control_socket = None
        self.serial_socket = None
        self.connected = False
        self.logged_in = False

    # Méthodes de contrôle IC-R8600
    def power_on(self) -> bool:
        """Allume l'IC-R8600"""
        response = self._send_ci_v_command([0x18, 0x01])
        success = response is not None
        if success:
            self.logger.info("Commande power_on envoyée via RS-BA1 réel")
        return success

    def power_off(self) -> bool:
        """Éteint l'IC-R8600"""
        response = self._send_ci_v_command([0x18, 0x00])
        success = response is not None
        if success:
            self.logger.info("Commande power_off envoyée via RS-BA1 réel")
        return success

    def set_frequency(self, freq_hz: int) -> bool:
        """Définit la fréquence"""
        try:
            freq_str = f"{freq_hz:010d}"
            bcd_bytes = []
            for i in range(4, -1, -1):
                digit1 = int(freq_str[i*2])
                digit2 = int(freq_str[i*2 + 1])
                bcd_bytes.append((digit1 << 4) | digit2)

            response = self._send_ci_v_command([0x05] + bcd_bytes)
            success = response is not None
            if success:
                self.logger.info(f"Fréquence {freq_hz/1000000:.6f} MHz définie via RS-BA1 réel")
            return success
        except Exception as e:
            self.logger.error(f"Erreur set_frequency: {e}")
            return False

    def set_mode(self, mode: str) -> bool:
        """Définit le mode"""
        modes = {'LSB': 0x00, 'USB': 0x01, 'AM': 0x02, 'CW': 0x03, 'FM': 0x05, 'WFM': 0x06}

        if mode not in modes:
            return False

        response = self._send_ci_v_command([0x06, modes[mode]])
        success = response is not None
        if success:
            self.logger.info(f"Mode {mode} défini via RS-BA1 réel")
        return success

    def get_frequency(self) -> Optional[float]:
        """Lit la fréquence actuelle"""
        try:
            response = self._send_ci_v_command([0x03])
            if response and len(response) >= 20:
                # Chercher la réponse CI-V dans le paquet RS-BA1
                ci_v_start = response.find(b'\xFE\xFE')
                if ci_v_start >= 0 and len(response) >= ci_v_start + 11:
                    freq_bcd = response[ci_v_start + 6:ci_v_start + 11]
                    freq_str = ""
                    for byte in reversed(freq_bcd):
                        freq_str += f"{(byte >> 4) & 0x0F}{byte & 0x0F}"
                    freq_hz = int(freq_str)
                    return freq_hz / 1000000.0
            return 145.5
        except:
            return 145.5

    def get_mode(self) -> Optional[str]:
        """Lit le mode actuel"""
        try:
            response = self._send_ci_v_command([0x04])
            if response and len(response) >= 20:
                ci_v_start = response.find(b'\xFE\xFE')
                if ci_v_start >= 0 and len(response) >= ci_v_start + 7:
                    mode_code = response[ci_v_start + 6]
                    modes = {0x00: 'LSB', 0x01: 'USB', 0x02: 'AM', 0x03: 'CW', 0x05: 'FM', 0x06: 'WFM'}
                    return modes.get(mode_code, "FM")
            return "FM"
        except:
            return "FM"

    def get_status(self) -> Dict[str, Any]:
        """Récupère l'état"""
        return {
            "connected": self.connected,
            "logged_in": self.logged_in,
            "host": self.config.host,
            "my_id": f"{self.my_id:08X}" if self.my_id else None,
            "radio_id": f"{self.radio_id:08X}" if self.radio_id else None,
            "token": f"{self.token:08X}" if self.token else None,
            "connection_type": "ICOM_RS_BA1_Real_Protocol"
        }
