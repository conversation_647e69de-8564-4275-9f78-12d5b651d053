@echo off
REM ========================================
REM VirtualHere Server Setup - PC Distant
REM Installation et configuration automatique
REM ========================================

echo [INFO] Installation VirtualHere Server pour IC-R8600
echo ========================================

REM Créer le répertoire d'installation
if not exist "C:\VirtualHere" mkdir "C:\VirtualHere"
cd /d "C:\VirtualHere"

REM Télécharger VirtualHere Server
echo [INFO] Téléchargement VirtualHere Server...
powershell -Command "Invoke-WebRequest -Uri 'https://www.virtualhere.com/sites/default/files/usbserver/vhusbdwin64.exe' -OutFile 'vhusbdwin64.exe'"

if not exist "vhusbdwin64.exe" (
    echo [ERROR] Échec téléchargement VirtualHere Server
    pause
    exit /b 1
)

REM Configuration VirtualHere Server
echo [INFO] Configuration VirtualHere Server...
echo # Configuration VirtualHere Server pour IC-R8600 > config.ini
echo # Autoriser tous les clients >> config.ini
echo ServerName=IC-R8600-Remote >> config.ini
echo AutoFind=1 >> config.ini
echo # Port par défaut 7575 >> config.ini
echo Port=7575 >> config.ini
echo # Log pour debug >> config.ini
echo LogLevel=2 >> config.ini
echo LogFile=virtualhere.log >> config.ini
echo # Autoriser connexions externes >> config.ini
echo onClientConnect=return true >> config.ini

REM Créer script de démarrage
echo [INFO] Création script de démarrage...
echo @echo off > start_virtualhere.bat
echo echo [INFO] Démarrage VirtualHere Server pour IC-R8600... >> start_virtualhere.bat
echo cd /d "C:\VirtualHere" >> start_virtualhere.bat
echo vhusbdwin64.exe -c config.ini >> start_virtualhere.bat

REM Créer tâche planifiée pour démarrage automatique
echo [INFO] Configuration démarrage automatique...
schtasks /create /tn "VirtualHere-IC-R8600" /tr "C:\VirtualHere\start_virtualhere.bat" /sc onstart /ru SYSTEM /f

REM Créer script d'arrêt
echo @echo off > stop_virtualhere.bat
echo echo [INFO] Arrêt VirtualHere Server... >> stop_virtualhere.bat
echo taskkill /f /im vhusbdwin64.exe >> stop_virtualhere.bat

REM Créer script de statut
echo @echo off > status_virtualhere.bat
echo echo [INFO] Statut VirtualHere Server: >> status_virtualhere.bat
echo tasklist /fi "imagename eq vhusbdwin64.exe" >> status_virtualhere.bat
echo echo. >> status_virtualhere.bat
echo echo [INFO] Périphériques USB disponibles: >> status_virtualhere.bat
echo powershell "Get-WmiObject -Class Win32_USBControllerDevice | ForEach-Object { [wmi]($_.Dependent) } | Where-Object { $_.Description -like '*ICOM*' -or $_.Description -like '*IC-R8600*' -or $_.Description -like '*USB Serial*' } | Select-Object Description, DeviceID" >> status_virtualhere.bat

REM Configuration pare-feu Windows
echo [INFO] Configuration pare-feu Windows...
netsh advfirewall firewall add rule name="VirtualHere Server" dir=in action=allow protocol=TCP localport=7575
netsh advfirewall firewall add rule name="VirtualHere Server UDP" dir=in action=allow protocol=UDP localport=7575

echo.
echo ========================================
echo [SUCCESS] Installation VirtualHere Server terminée !
echo ========================================
echo.
echo Scripts créés:
echo - start_virtualhere.bat : Démarrer le serveur
echo - stop_virtualhere.bat  : Arrêter le serveur  
echo - status_virtualhere.bat: Vérifier le statut
echo.
echo Configuration:
echo - Port: 7575
echo - Démarrage automatique: OUI
echo - Pare-feu: Configuré
echo.
echo IMPORTANT:
echo 1. Connectez l'IC-R8600 en USB au PC
echo 2. Installez les drivers ICOM si nécessaire
echo 3. Lancez: start_virtualhere.bat
echo 4. Vérifiez avec: status_virtualhere.bat
echo.
pause

REM Démarrer immédiatement le serveur
echo [INFO] Démarrage du serveur VirtualHere...
start "" "start_virtualhere.bat"

echo [INFO] VirtualHere Server démarré !
echo Vérifiez le statut avec: status_virtualhere.bat
pause
