#!/usr/bin/env python3
"""
Test des ports COM6 et COM7 pour trouver l'IC-R8600
"""

import serial
import time

def test_port(port_name):
    """Test un port COM spécifique"""
    print(f"\n🔍 TEST {port_name}")
    print("=" * 30)
    
    try:
        # Tentative d'ouverture
        print(f"Ouverture {port_name}...")
        ser = serial.Serial(port_name, 19200, timeout=1)
        print(f"✅ {port_name} ouvert avec succès")
        
        # Test commande CI-V avec adresse DFh (remote)
        cmd = bytes([0xFE, 0xFE, 0xDF, 0xE0, 0x03, 0xFD])
        print(f"Envoi commande: {cmd.hex().upper()}")
        
        ser.reset_input_buffer()
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(20)
        if response:
            print(f"✅ RÉPONSE: {response.hex().upper()}")
            print(f"🎉 {port_name} = IC-R8600 TROUVÉ !")
            ser.close()
            return True
        else:
            print(f"⚠️  Pas de réponse sur {port_name}")
            
        ser.close()
        return False
        
    except Exception as e:
        print(f"❌ Erreur {port_name}: {e}")
        return False

def main():
    """Test des deux ports"""
    print("🎯 RECHERCHE IC-R8600 SUR PORTS VIRTUALHERE")
    print("=" * 50)
    
    # Test COM6
    com6_ok = test_port("COM6")
    
    # Test COM7
    com7_ok = test_port("COM7")
    
    print("\n" + "=" * 50)
    print("📊 RÉSULTATS")
    print("=" * 50)
    
    if com6_ok:
        print("✅ IC-R8600 trouvé sur COM6")
        print("👉 Utilisez COM6 dans l'application")
    elif com7_ok:
        print("✅ IC-R8600 trouvé sur COM7")
        print("👉 Utilisez COM7 dans l'application")
    else:
        print("❌ IC-R8600 non trouvé sur COM6 ni COM7")
        print("Vérifications:")
        print("- VirtualHere Client connecté ?")
        print("- IC-R8600 'Use this device' activé ?")
        print("- IC-R8600 allumé ?")
        print("- Configuration CI-V correcte ?")

if __name__ == "__main__":
    main()
