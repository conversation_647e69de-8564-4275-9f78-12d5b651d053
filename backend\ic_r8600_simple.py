#!/usr/bin/env python3
"""
Contrôle IC-R8600 simplifié - Version qui fonctionne
Basé sur les tests qui marchent (test_com6.py)
"""

import serial
import serial.tools.list_ports
import time
import logging
from typing import Optional

class ICR8600Simple:
    """Contrôle IC-R8600 simplifié et fonctionnel"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.serial_connection = None
        self.connected = False
        self.port = None
        
        # Paramètres série
        self.BAUD_RATE = 19200
        self.TIMEOUT = 1.0
        
    def find_icom_port(self) -> Optional[str]:
        """Trouve le port IC-R8600"""
        try:
            ports = serial.tools.list_ports.comports()
            for port in ports:
                if "Silicon Labs CP210x" in port.description:
                    self.logger.info(f"Port IC-R8600 trouvé: {port.device} ({port.description})")
                    return port.device
            return None
        except Exception as e:
            self.logger.error(f"Erreur recherche port: {e}")
            return None
    
    def connect(self, port: Optional[str] = None) -> bool:
        """Connexion simple et rapide"""
        try:
            # Auto-détection du port
            if not port:
                port = self.find_icom_port()
                if not port:
                    self.logger.error("Aucun port IC-R8600 trouvé")
                    return False
            
            # Fermer connexion existante
            self.disconnect()
            
            # Connexion série
            self.logger.info(f"Connexion IC-R8600 sur {port}")
            self.serial_connection = serial.Serial(
                port=port,
                baudrate=self.BAUD_RATE,
                timeout=self.TIMEOUT,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                bytesize=serial.EIGHTBITS
            )
            
            # Test simple de communication
            if self._test_communication():
                self.connected = True
                self.port = port
                self.logger.info(f"✅ IC-R8600 connecté sur {port}")
                return True
            else:
                self.logger.error("❌ Test de communication échoué")
                self.disconnect()
                return False
                
        except Exception as e:
            self.logger.error(f"Erreur connexion: {e}")
            self.disconnect()
            return False
    
    def disconnect(self):
        """Déconnexion"""
        if self.serial_connection and self.serial_connection.is_open:
            try:
                self.serial_connection.close()
            except:
                pass
        self.serial_connection = None
        self.connected = False
        self.port = None
    
    def _test_communication(self) -> bool:
        """Test simple de communication"""
        try:
            if not self.serial_connection or not self.serial_connection.is_open:
                return False
            
            # Vider les buffers
            self.serial_connection.reset_input_buffer()
            self.serial_connection.reset_output_buffer()
            
            # Test lecture fréquence (commande 03)
            cmd = bytes([0xFE, 0xFE, 0x96, 0x00, 0x03, 0xFD])
            self.serial_connection.write(cmd)
            time.sleep(0.1)
            
            # Lire réponse
            response = self.serial_connection.read(20)
            if len(response) >= 6 and response[0] == 0xFE and response[1] == 0xFE:
                self.logger.info("✅ Communication IC-R8600 OK")
                return True
            else:
                self.logger.warning(f"❌ Réponse inattendue: {response.hex()}")
                return False
                
        except Exception as e:
            self.logger.error(f"Erreur test communication: {e}")
            return False
    
    def send_command(self, cmd: bytes) -> Optional[bytes]:
        """Envoi de commande avec réponse"""
        try:
            if not self.connected or not self.serial_connection:
                return None
            
            # Vider les buffers
            self.serial_connection.reset_input_buffer()
            self.serial_connection.reset_output_buffer()
            
            # Envoyer commande
            self.serial_connection.write(cmd)
            time.sleep(0.1)
            
            # Lire réponse
            response = self.serial_connection.read(20)
            return response if len(response) > 0 else None
            
        except Exception as e:
            self.logger.error(f"Erreur envoi commande: {e}")
            return None
    
    def set_frequency(self, freq_hz: int) -> bool:
        """Change la fréquence"""
        try:
            # Conversion Hz vers BCD (format IC-R8600)
            freq_str = f"{freq_hz:010d}"
            bcd_bytes = []
            for i in range(0, 10, 2):
                digit1 = int(freq_str[i])
                digit2 = int(freq_str[i+1])
                bcd_byte = (digit2 << 4) | digit1
                bcd_bytes.append(bcd_byte)
            
            # Commande changement fréquence (05)
            cmd = bytes([0xFE, 0xFE, 0x96, 0x00, 0x05] + bcd_bytes + [0xFD])
            response = self.send_command(cmd)
            
            if response and len(response) >= 6:
                self.logger.info(f"✅ Fréquence changée: {freq_hz} Hz")
                return True
            else:
                self.logger.warning(f"❌ Échec changement fréquence: {freq_hz} Hz")
                return False
                
        except Exception as e:
            self.logger.error(f"Erreur changement fréquence: {e}")
            return False
    
    def set_mode(self, mode: str) -> bool:
        """Change le mode"""
        try:
            # Mapping des modes
            mode_map = {
                'LSB': 0x00, 'USB': 0x01, 'AM': 0x02, 'CW': 0x03,
                'FM': 0x05, 'WFM': 0x06, 'CWR': 0x07, 'RTTY': 0x08,
                'RTTYR': 0x09, 'PSK': 0x12, 'PSKR': 0x13
            }
            
            if mode not in mode_map:
                self.logger.error(f"Mode non supporté: {mode}")
                return False
            
            # Commande changement mode (06)
            cmd = bytes([0xFE, 0xFE, 0x96, 0x00, 0x06, mode_map[mode], 0xFD])
            response = self.send_command(cmd)
            
            if response and len(response) >= 6:
                self.logger.info(f"✅ Mode changé: {mode}")
                return True
            else:
                self.logger.warning(f"❌ Échec changement mode: {mode}")
                return False
                
        except Exception as e:
            self.logger.error(f"Erreur changement mode: {e}")
            return False
    
    def set_power(self, power_on: bool) -> bool:
        """Allume/éteint le récepteur"""
        try:
            # Commande power (18)
            power_val = 0x01 if power_on else 0x00
            cmd = bytes([0xFE, 0xFE, 0x96, 0x00, 0x18, power_val, 0xFD])
            response = self.send_command(cmd)
            
            if response and len(response) >= 6:
                state = "ON" if power_on else "OFF"
                self.logger.info(f"✅ Alimentation: {state}")
                return True
            else:
                state = "ON" if power_on else "OFF"
                self.logger.warning(f"❌ Échec alimentation: {state}")
                return False
                
        except Exception as e:
            self.logger.error(f"Erreur alimentation: {e}")
            return False
    
    def get_status(self) -> dict:
        """Retourne le statut"""
        return {
            "connected": self.connected,
            "port": self.port,
            "frequency": 145500000,  # Valeur par défaut
            "mode": "FM",
            "power_on": True
        }
