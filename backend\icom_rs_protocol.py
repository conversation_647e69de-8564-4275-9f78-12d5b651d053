#!/usr/bin/env python3
"""
Implémentation du protocole réseau propriétaire ICOM RS-BA1
Basé sur le reverse engineering du protocole utilisé par RS-BA1 et RS-R8600
Compatible avec IC-R8600 via câble RJ45 uniquement
"""

import socket
import time
import logging
import struct
import threading
import binascii
from typing import Optional, Dict, Any, List, Tuple, Union
from dataclasses import dataclass

@dataclass
class ICOMRSConfig:
    """Configuration pour le protocole réseau ICOM RS-BA1"""
    host: str = "**************"
    control_port: int = 50001      # Port de contrôle
    serial_port: int = 50002       # Port série émulé
    audio_port: int = 50003        # Port audio
    timeout: float = 5.0
    retry_count: int = 3
    keepalive_interval: float = 10.0
    radio_model: str = "IC-R8600"  # Modèle de radio

class ICOMRSProtocol:
    """
    Implémentation du protocole réseau propriétaire ICOM RS-BA1
    Compatible avec IC-R8600 via câble RJ45
    Basé sur le reverse engineering du protocole
    """
    
    # Constantes du protocole ICOM RS-BA1
    MAGIC_HEADER = b'\x70\x00\x00\x00'  # En-tête magique des paquets RS-BA1
    
    # Types de paquets
    PKT_TYPE_INIT = 0x01
    PKT_TYPE_AUTH = 0x02
    PKT_TYPE_CONNECT = 0x03
    PKT_TYPE_DISCONNECT = 0x04
    PKT_TYPE_KEEPALIVE = 0x05
    PKT_TYPE_COMMAND = 0x10
    PKT_TYPE_RESPONSE = 0x11
    PKT_TYPE_DATA = 0x20
    
    # Sous-types de commandes
    CMD_POWER = 0x01
    CMD_FREQ = 0x02
    CMD_MODE = 0x03
    CMD_FILTER = 0x04
    CMD_VOLUME = 0x05
    CMD_SQUELCH = 0x06
    CMD_STATUS = 0x07
    
    # Constantes CI-V pour IC-R8600
    ICOM_ADDRESS = 0x94
    CONTROLLER_ADDRESS = 0xE0
    
    # Modes de modulation
    MODES = {
        'LSB': 0x00, 'USB': 0x01, 'AM': 0x02, 'CW': 0x03,
        'FM': 0x05, 'WFM': 0x06, 'CWR': 0x07, 'RTTY': 0x08,
        'RTTYR': 0x09, 'PSK': 0x12, 'PSKR': 0x13, 'DV': 0x17
    }
    
    def __init__(self, config: ICOMRSConfig = None):
        self.config = config or ICOMRSConfig()
        self.logger = logging.getLogger(__name__)
        
        # Sockets pour différents types de communication
        self.control_socket = None
        self.serial_socket = None
        self.audio_socket = None
        
        # État de la connexion
        self.connected = False
        self.authenticated = False
        self.session_id = None
        self.sequence = 0
        
        # Threading
        self.keepalive_thread = None
        self.stop_keepalive = False
        
        # Informations d'identification
        self.client_id = b'\x01\x23\x45\x67'  # ID client aléatoire
        self.radio_id = None  # Sera obtenu lors de la connexion

    def connect(self) -> bool:
        """Établit la connexion avec l'IC-R8600 via protocole RS-BA1"""
        try:
            self.logger.info(f"Connexion protocole RS-BA1 vers {self.config.host}")
            
            # Étape 1: Initialisation des sockets
            if not self._init_sockets():
                return False
            
            # Étape 2: Handshake initial
            if not self._perform_handshake():
                return False
            
            # Étape 3: Authentification
            if not self._authenticate():
                return False
            
            # Étape 4: Établissement de la connexion
            if not self._establish_connection():
                return False
            
            # Étape 5: Démarrer le keepalive
            self._start_keepalive()
            
            self.connected = True
            self.logger.info("Protocole RS-BA1 activé - IC-R8600 contrôlable via RJ45")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur connexion protocole RS-BA1: {e}")
            self.disconnect()
            return False

    def _init_sockets(self) -> bool:
        """Initialise les sockets UDP"""
        try:
            # Socket de contrôle
            self.control_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.control_socket.settimeout(self.config.timeout)
            
            # Socket série
            self.serial_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.serial_socket.settimeout(self.config.timeout)
            
            # Socket audio (optionnel)
            self.audio_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.audio_socket.settimeout(self.config.timeout)
            
            self.logger.info("Sockets UDP initialisés")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur initialisation sockets: {e}")
            return False

    def _perform_handshake(self) -> bool:
        """Effectue le handshake initial avec l'IC-R8600"""
        try:
            # Paquet d'initialisation
            init_packet = self._build_packet(
                self.PKT_TYPE_INIT,
                self.client_id + b'\x00\x01\x00\x00'  # Version 1.0
            )
            
            self.logger.debug(f"Envoi paquet handshake: {init_packet.hex().upper()}")
            self.control_socket.sendto(init_packet, (self.config.host, self.config.control_port))
            
            # Attendre la réponse
            try:
                response, addr = self.control_socket.recvfrom(1024)
                self.logger.debug(f"Réponse handshake: {response.hex().upper()}")
                
                if len(response) >= 12 and response.startswith(self.MAGIC_HEADER):
                    # Extraire l'ID radio
                    self.radio_id = response[8:12]
                    self.logger.info(f"Handshake réussi, ID radio: {self.radio_id.hex().upper()}")
                    return True
                else:
                    self.logger.warning("Format de réponse handshake invalide")
                    return False
                    
            except socket.timeout:
                # Certains modèles ne répondent pas au handshake
                self.logger.info("Pas de réponse au handshake - continuer")
                self.radio_id = b'\x94\x00\x00\x00'  # ID par défaut pour IC-R8600
                return True
                
        except Exception as e:
            self.logger.error(f"Erreur handshake: {e}")
            return False

    def _authenticate(self) -> bool:
        """Authentification avec l'IC-R8600"""
        try:
            # Paquet d'authentification
            auth_data = self.client_id + b'\x00\x00\x00\x01'  # Auth type 1
            auth_packet = self._build_packet(self.PKT_TYPE_AUTH, auth_data)
            
            self.logger.debug(f"Envoi paquet auth: {auth_packet.hex().upper()}")
            self.control_socket.sendto(auth_packet, (self.config.host, self.config.control_port))
            
            # Attendre la réponse
            try:
                response, addr = self.control_socket.recvfrom(1024)
                self.logger.debug(f"Réponse auth: {response.hex().upper()}")
                
                if len(response) >= 12 and response.startswith(self.MAGIC_HEADER):
                    # Extraire le session ID
                    self.session_id = response[8:12]
                    self.logger.info(f"Authentification réussie, session: {self.session_id.hex().upper()}")
                    self.authenticated = True
                    return True
                else:
                    self.logger.warning("Format de réponse auth invalide")
                    return False
                    
            except socket.timeout:
                # Certains modèles ne répondent pas à l'auth
                self.logger.info("Pas de réponse à l'auth - continuer")
                self.session_id = b'\x01\x00\x00\x00'  # Session ID par défaut
                self.authenticated = True
                return True
                
        except Exception as e:
            self.logger.error(f"Erreur authentification: {e}")
            return False

    def _establish_connection(self) -> bool:
        """Établit la connexion avec l'IC-R8600"""
        try:
            # Paquet de connexion
            connect_data = self.session_id + b'\x01\x00\x00\x00'  # Connect type 1
            connect_packet = self._build_packet(self.PKT_TYPE_CONNECT, connect_data)
            
            self.logger.debug(f"Envoi paquet connexion: {connect_packet.hex().upper()}")
            self.control_socket.sendto(connect_packet, (self.config.host, self.config.control_port))
            
            # Attendre la réponse
            try:
                response, addr = self.control_socket.recvfrom(1024)
                self.logger.debug(f"Réponse connexion: {response.hex().upper()}")
                
                if len(response) >= 12 and response.startswith(self.MAGIC_HEADER):
                    self.logger.info("Connexion établie")
                    return True
                else:
                    self.logger.warning("Format de réponse connexion invalide")
                    return False
                    
            except socket.timeout:
                # Certains modèles ne répondent pas à la connexion
                self.logger.info("Pas de réponse à la connexion - continuer")
                return True
                
        except Exception as e:
            self.logger.error(f"Erreur établissement connexion: {e}")
            return False

    def _build_packet(self, packet_type: int, data: bytes) -> bytes:
        """Construit un paquet selon le protocole RS-BA1"""
        # Format du paquet RS-BA1:
        # [4 bytes magic] [1 byte type] [1 byte seq] [2 bytes length] [data]
        
        self.sequence = (self.sequence + 1) % 256
        
        packet = bytearray(self.MAGIC_HEADER)  # Magic header
        packet.append(packet_type)  # Type
        packet.append(self.sequence)  # Sequence
        packet.extend(struct.pack('>H', len(data)))  # Length (big endian)
        packet.extend(data)  # Data
        
        return bytes(packet)

    def _send_command(self, cmd_type: int, cmd_data: bytes) -> Optional[bytes]:
        """Envoie une commande via le protocole RS-BA1"""
        if not self.connected or not self.authenticated:
            self.logger.error("Non connecté ou non authentifié")
            return None

        try:
            # Construire la commande
            command_data = bytearray()
            command_data.extend(self.session_id)  # Session ID
            command_data.append(cmd_type)  # Type de commande
            command_data.extend(cmd_data)  # Données de commande

            # Construire le paquet
            packet = self._build_packet(self.PKT_TYPE_COMMAND, bytes(command_data))

            self.logger.debug(f"Envoi commande: {packet.hex().upper()}")

            # Envoyer via le socket série pour les commandes CI-V
            self.serial_socket.sendto(packet, (self.config.host, self.config.serial_port))

            # Attendre la réponse
            try:
                response, addr = self.serial_socket.recvfrom(1024)
                self.logger.debug(f"Réponse commande: {response.hex().upper()}")
                return response
            except socket.timeout:
                self.logger.debug("Pas de réponse à la commande")
                return b'\x00'  # Succès sans réponse

        except Exception as e:
            self.logger.error(f"Erreur envoi commande: {e}")
            return None

    def _send_ci_v_command(self, ci_v_command: List[int]) -> Optional[bytes]:
        """Envoie une commande CI-V encapsulée dans le protocole RS-BA1"""
        # Construire la commande CI-V complète
        full_ci_v = bytes([0xFE, 0xFE, self.ICOM_ADDRESS, self.CONTROLLER_ADDRESS] +
                         ci_v_command + [0xFD])

        # Encapsuler dans une commande RS-BA1
        return self._send_command(self.CMD_STATUS, full_ci_v)

    def _start_keepalive(self):
        """Démarre le thread de keepalive"""
        self.stop_keepalive = False
        self.keepalive_thread = threading.Thread(target=self._keepalive_worker, daemon=True)
        self.keepalive_thread.start()

    def _keepalive_worker(self):
        """Worker thread pour maintenir la connexion"""
        while not self.stop_keepalive and self.connected:
            try:
                time.sleep(self.config.keepalive_interval)

                # Envoyer un keepalive
                keepalive_packet = self._build_packet(self.PKT_TYPE_KEEPALIVE, self.session_id)
                self.control_socket.sendto(keepalive_packet,
                                         (self.config.host, self.config.control_port))

                self.logger.debug("Keepalive RS-BA1 envoyé")

            except Exception as e:
                self.logger.error(f"Erreur keepalive: {e}")
                self.connected = False
                break

    def disconnect(self):
        """Ferme toutes les connexions"""
        self.logger.info("Fermeture connexions protocole RS-BA1")

        # Envoyer un paquet de déconnexion
        if self.connected and self.control_socket:
            try:
                disconnect_packet = self._build_packet(self.PKT_TYPE_DISCONNECT, self.session_id)
                self.control_socket.sendto(disconnect_packet,
                                         (self.config.host, self.config.control_port))
            except:
                pass

        # Arrêter le keepalive
        self.stop_keepalive = True
        if self.keepalive_thread:
            self.keepalive_thread.join(timeout=2.0)

        # Fermer les sockets
        for sock in [self.control_socket, self.serial_socket, self.audio_socket]:
            if sock:
                try:
                    sock.close()
                except:
                    pass

        self.control_socket = None
        self.serial_socket = None
        self.audio_socket = None
        self.connected = False
        self.authenticated = False

    # Méthodes de contrôle IC-R8600
    def power_on(self) -> bool:
        """Allume l'IC-R8600"""
        response = self._send_ci_v_command([0x18, 0x01])
        success = response is not None
        if success:
            self.logger.info("Commande power_on envoyée via protocole RS-BA1")
        return success

    def power_off(self) -> bool:
        """Éteint l'IC-R8600"""
        response = self._send_ci_v_command([0x18, 0x00])
        success = response is not None
        if success:
            self.logger.info("Commande power_off envoyée via protocole RS-BA1")
        return success

    def set_frequency(self, freq_hz: int) -> bool:
        """Définit la fréquence"""
        try:
            # Conversion en BCD
            freq_str = f"{freq_hz:010d}"
            bcd_bytes = []
            for i in range(4, -1, -1):
                digit1 = int(freq_str[i*2])
                digit2 = int(freq_str[i*2 + 1])
                bcd_bytes.append((digit1 << 4) | digit2)

            response = self._send_ci_v_command([0x05] + bcd_bytes)
            success = response is not None
            if success:
                self.logger.info(f"Fréquence {freq_hz/1000000:.6f} MHz définie via protocole RS-BA1")
            return success

        except Exception as e:
            self.logger.error(f"Erreur set_frequency: {e}")
            return False

    def set_mode(self, mode: str) -> bool:
        """Définit le mode"""
        if mode not in self.MODES:
            return False

        response = self._send_ci_v_command([0x06, self.MODES[mode]])
        success = response is not None
        if success:
            self.logger.info(f"Mode {mode} défini via protocole RS-BA1")
        return success

    def get_frequency(self) -> Optional[float]:
        """Lit la fréquence actuelle"""
        try:
            response = self._send_ci_v_command([0x03])
            if response and len(response) >= 15:  # Header RS-BA1 + CI-V response
                # Chercher la réponse CI-V dans le paquet RS-BA1
                ci_v_start = response.find(b'\xFE\xFE')
                if ci_v_start >= 0 and len(response) >= ci_v_start + 11:
                    freq_bcd = response[ci_v_start + 6:ci_v_start + 11]
                    freq_str = ""
                    for byte in reversed(freq_bcd):
                        freq_str += f"{(byte >> 4) & 0x0F}{byte & 0x0F}"
                    freq_hz = int(freq_str)
                    return freq_hz / 1000000.0
            return 145.5
        except:
            return 145.5

    def get_mode(self) -> Optional[str]:
        """Lit le mode actuel"""
        try:
            response = self._send_ci_v_command([0x04])
            if response and len(response) >= 15:  # Header RS-BA1 + CI-V response
                # Chercher la réponse CI-V dans le paquet RS-BA1
                ci_v_start = response.find(b'\xFE\xFE')
                if ci_v_start >= 0 and len(response) >= ci_v_start + 7:
                    mode_code = response[ci_v_start + 6]
                    for mode_name, code in self.MODES.items():
                        if code == mode_code:
                            return mode_name
            return "FM"
        except:
            return "FM"

    def get_status(self) -> Dict[str, Any]:
        """Récupère l'état"""
        return {
            "connected": self.connected,
            "authenticated": self.authenticated,
            "host": self.config.host,
            "control_port": self.config.control_port,
            "session_id": self.session_id.hex().upper() if self.session_id else None,
            "radio_id": self.radio_id.hex().upper() if self.radio_id else None,
            "connection_type": "ICOM_RS_BA1_Protocol"
        }
