#!/usr/bin/env python3
"""
Workaround spécifique pour IC-R8600 Main CPU v1.33
Cette version a des problèmes connus avec Network Control
"""

import socket
import time
import struct

class ICR8600_V133_Workaround:
    """Workaround pour la version 1.33 de l'IC-R8600"""
    
    def __init__(self, host="**************"):
        self.host = host
        
    def test_v133_specific_ports(self):
        """Test des ports spécifiques à la version 1.33"""
        print("🔧 WORKAROUND VERSION 1.33")
        print("=" * 40)
        print("La version Main CPU 1.33 a des problèmes connus")
        print("avec Network Control. Test de solutions...")
        print()
        
        # Ports alternatifs utilisés par la v1.33
        alternative_ports = [
            23,     # Telnet (fonctionne)
            50000,  # Port alternatif
            50004,  # Port alternatif
            50005,  # Port alternatif
            8080,   # Port HTTP alternatif
            8000,   # Port alternatif
        ]
        
        working_ports = []
        
        for port in alternative_ports:
            if self._test_port_v133(port):
                working_ports.append(port)
        
        return working_ports
    
    def _test_port_v133(self, port):
        """Test spécifique d'un port pour v1.33"""
        try:
            # Test UDP avec paquet spécial v1.33
            sock_udp = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock_udp.settimeout(2.0)
            
            # Paquet de réveil spécifique v1.33
            wake_packet = b'\x00\x00\x01\x33'  # Version signature
            sock_udp.sendto(wake_packet, (self.host, port))
            
            try:
                response, addr = sock_udp.recvfrom(1024)
                print(f"✅ Port {port} UDP: RÉPONSE v1.33")
                print(f"   Réponse: {response.hex().upper()}")
                sock_udp.close()
                return True
            except socket.timeout:
                pass
            
            sock_udp.close()
            
            # Test TCP
            sock_tcp = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock_tcp.settimeout(2.0)
            result = sock_tcp.connect_ex((self.host, port))
            
            if result == 0:
                print(f"✅ Port {port} TCP: OUVERT")
                
                # Test commande spéciale v1.33
                if port == 23:
                    self._test_telnet_v133(sock_tcp)
                
                sock_tcp.close()
                return True
            
            sock_tcp.close()
            
        except Exception as e:
            print(f"❌ Port {port}: {e}")
        
        return False
    
    def _test_telnet_v133(self, sock):
        """Test Telnet spécifique v1.33"""
        try:
            # Commandes spéciales pour activer le réseau en v1.33
            v133_commands = [
                b'NETW:ENAB ON\r\n',      # Activation réseau
                b'NETW:PORT 50001\r\n',   # Configuration port
                b'NETW:STAT?\r\n',        # Status réseau
                b'SYST:VERS?\r\n',        # Version
            ]
            
            for cmd in v133_commands:
                print(f"   Test v1.33: {cmd.decode('ascii', errors='ignore').strip()}")
                sock.sendall(cmd)
                time.sleep(0.5)
                
                try:
                    response = sock.recv(1024)
                    if response:
                        print(f"   Réponse: {response.decode('ascii', errors='ignore').strip()}")
                        
                        # Si on reçoit une réponse positive
                        if b'OK' in response or b'ON' in response:
                            print("   ✅ Commande v1.33 acceptée !")
                            return True
                            
                except socket.timeout:
                    pass
                    
        except Exception as e:
            print(f"   Erreur v1.33: {e}")
        
        return False
    
    def try_v133_activation_sequence(self):
        """Séquence d'activation spécifique v1.33"""
        print("\n🚀 SÉQUENCE D'ACTIVATION V1.33")
        print("-" * 35)
        
        try:
            # Connexion Telnet pour activation
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5.0)
            sock.connect((self.host, 23))
            
            print("✅ Connexion Telnet établie")
            
            # Séquence d'activation v1.33
            activation_sequence = [
                b'*RST\r\n',                    # Reset
                b'SYST:COMM:SER:ECHO OFF\r\n',  # Désactiver echo
                b'NETW:ENAB ON\r\n',            # Activer réseau
                b'NETW:CONT:PORT 50001\r\n',    # Port contrôle
                b'NETW:SER:PORT 50002\r\n',     # Port série
                b'NETW:AUD:PORT 50003\r\n',     # Port audio
                b'NETW:APPL\r\n',               # Appliquer config
                b'*SAV\r\n',                    # Sauvegarder
            ]
            
            for cmd in activation_sequence:
                print(f"Envoi: {cmd.decode('ascii', errors='ignore').strip()}")
                sock.sendall(cmd)
                time.sleep(1)
                
                try:
                    response = sock.recv(1024)
                    if response:
                        resp_str = response.decode('ascii', errors='ignore').strip()
                        print(f"Réponse: {resp_str}")
                        
                        if 'ERROR' in resp_str.upper():
                            print("❌ Erreur dans la séquence")
                        elif 'OK' in resp_str.upper():
                            print("✅ Commande acceptée")
                            
                except socket.timeout:
                    print("Pas de réponse")
            
            sock.close()
            
            print("\n⏳ Attente 5 secondes pour application des changements...")
            time.sleep(5)
            
            # Test des ports après activation
            print("\n🔍 Test des ports après activation v1.33:")
            return self.test_v133_specific_ports()
            
        except Exception as e:
            print(f"❌ Erreur séquence v1.33: {e}")
            return []
    
    def test_ci_v_via_telnet_v133(self):
        """Test CI-V via Telnet pour v1.33"""
        print("\n⚡ TEST CI-V VIA TELNET V1.33")
        print("-" * 35)
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5.0)
            sock.connect((self.host, 23))
            
            # Mode binaire pour CI-V
            sock.sendall(b'SYST:COMM:SER:MODE BIN\r\n')
            time.sleep(1)
            
            # Commandes CI-V directes
            ci_v_commands = [
                b'\xFE\xFE\x94\xE0\x19\x00\xFD',  # Status
                b'\xFE\xFE\x94\xE0\x18\x00\xFD',  # Power OFF
            ]
            
            for cmd in ci_v_commands:
                print(f"CI-V v1.33: {cmd.hex().upper()}")
                print("OBSERVEZ L'IC-R8600 !")
                
                sock.sendall(cmd)
                time.sleep(2)
                
                try:
                    response = sock.recv(1024)
                    if response:
                        print(f"Réponse: {response.hex().upper()}")
                        
                        if b'\xFB' in response:
                            print("✅ ACK - Commande v1.33 réussie !")
                            return True
                        elif b'\xFA' in response:
                            print("❌ NAK - Commande v1.33 refusée")
                            
                except socket.timeout:
                    print("Pas de réponse")
            
            sock.close()
            
        except Exception as e:
            print(f"❌ Erreur CI-V v1.33: {e}")
        
        return False

def main():
    """Test du workaround v1.33"""
    print("🔧 WORKAROUND IC-R8600 VERSION 1.33")
    print("=" * 50)
    print("Version détectée: Main CPU 1.33")
    print("Cette version a des problèmes connus avec Network Control")
    print("=" * 50)
    
    workaround = ICR8600_V133_Workaround("**************")
    
    # Test 1: Ports alternatifs
    working_ports = workaround.test_v133_specific_ports()
    
    # Test 2: Séquence d'activation
    if not working_ports:
        print("\nAucun port alternatif trouvé.")
        print("Tentative de séquence d'activation v1.33...")
        working_ports = workaround.try_v133_activation_sequence()
    
    # Test 3: CI-V via Telnet
    if 23 in working_ports:
        print("\nTelnet disponible - Test CI-V direct...")
        if workaround.test_ci_v_via_telnet_v133():
            print("✅ SUCCÈS ! CI-V fonctionne via Telnet en v1.33")
        else:
            print("❌ CI-V via Telnet ne fonctionne pas")
    
    # Résumé
    print("\n" + "=" * 50)
    print("📋 RÉSUMÉ WORKAROUND V1.33")
    print("=" * 50)
    
    if working_ports:
        print(f"✅ Ports fonctionnels: {working_ports}")
        print("✅ Contrôle possible avec cette version")
    else:
        print("❌ Aucun port fonctionnel trouvé")
        print("💡 RECOMMANDATION: Mise à jour firmware vers v1.40+")
    
    print("\n💡 SOLUTIONS:")
    print("1. Mise à jour firmware (recommandée)")
    print("2. Utilisation du contrôle USB (alternative)")
    print("3. Contrôle via Telnet si disponible")

if __name__ == "__main__":
    main()
