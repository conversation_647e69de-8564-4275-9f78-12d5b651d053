#!/usr/bin/env python3
"""
Test UDP avancé pour IC-R8600
Test de différents protocoles et séquences d'initialisation
"""

import socket
import time
import struct
import sys

def test_udp_raw_civ():
    """Test UDP avec commandes CI-V brutes"""
    host = "**************"
    ports = [50001, 50002, 50003]
    
    print("🧪 Test UDP CI-V brut sur tous les ports...")
    
    for port in ports:
        print(f"\n🔌 Test port UDP {port}")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(2.0)
            
            # Différentes commandes CI-V à tester
            commands = [
                # Lecture fréquence
                bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD]),
                # Lecture mode
                bytes([0xFE, 0xFE, 0x94, 0xE0, 0x04, 0xFD]),
                # Status
                bytes([0xFE, 0xFE, 0x94, 0xE0, 0x19, 0x00, 0xFD]),
                # Ping
                bytes([0xFE, 0xFE, 0x94, 0xE0, 0xFD]),
            ]
            
            for i, cmd in enumerate(commands):
                print(f"  Commande {i+1}: {cmd.hex().upper()}")
                sock.sendto(cmd, (host, port))
                
                try:
                    response, addr = sock.recvfrom(1024)
                    print(f"  ✅ Réponse: {response.hex().upper()}")
                    sock.close()
                    return True, port
                except socket.timeout:
                    print(f"  ⏱️ Timeout")
            
            sock.close()
            
        except Exception as e:
            print(f"  ❌ Erreur: {e}")
    
    return False, None

def test_udp_icom_protocol():
    """Test du protocole UDP spécifique ICOM"""
    host = "**************"
    port = 50001
    
    print(f"\n🧪 Test protocole UDP ICOM sur port {port}...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(3.0)
        
        # Séquence d'initialisation ICOM (hypothétique)
        init_sequences = [
            # Séquence 1: Handshake simple
            b'\x00\x00\x00\x10\x01\x00' + b'\x00' * 10,
            
            # Séquence 2: Identification
            b'ICOM' + b'\x00' * 12,
            
            # Séquence 3: IC-R8600 ID
            b'ICR8600\x00' + b'\x00' * 8,
            
            # Séquence 4: CI-V avec header
            b'\x00\x06' + bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD]),
        ]
        
        for i, seq in enumerate(init_sequences):
            print(f"  Séquence {i+1}: {seq.hex().upper()}")
            sock.sendto(seq, (host, port))
            
            try:
                response, addr = sock.recvfrom(1024)
                print(f"  ✅ Réponse: {response.hex().upper()}")
                
                # Si on a une réponse, tester une commande CI-V
                civ_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
                sock.sendto(civ_cmd, (host, port))
                
                try:
                    civ_resp, _ = sock.recvfrom(1024)
                    print(f"  ✅ CI-V après init: {civ_resp.hex().upper()}")
                    sock.close()
                    return True
                except socket.timeout:
                    print(f"  ⏱️ Pas de réponse CI-V après init")
                
            except socket.timeout:
                print(f"  ⏱️ Timeout séquence {i+1}")
        
        sock.close()
        
    except Exception as e:
        print(f"  ❌ Erreur protocole ICOM: {e}")
    
    return False

def test_broadcast_discovery():
    """Test de découverte par broadcast"""
    print(f"\n🧪 Test de découverte par broadcast...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        sock.settimeout(3.0)
        
        # Messages de découverte
        discovery_messages = [
            b'ICOM_DISCOVERY',
            b'IC-R8600?',
            bytes([0xFE, 0xFE, 0x00, 0xE0, 0x19, 0x00, 0xFD]),  # CI-V broadcast
            b'\xFF' * 16,  # Pattern de découverte
        ]
        
        for i, msg in enumerate(discovery_messages):
            print(f"  Broadcast {i+1}: {msg.hex().upper() if len(msg) < 20 else str(msg)}")
            
            # Broadcast sur différents ports
            for port in [50001, 50002, 50003]:
                try:
                    sock.sendto(msg, ('255.255.255.255', port))
                    sock.sendto(msg, ('192.168.37.255', port))
                except:
                    pass
            
            # Écouter les réponses
            try:
                response, addr = sock.recvfrom(1024)
                print(f"  ✅ Réponse de {addr}: {response.hex().upper()}")
                sock.close()
                return True
            except socket.timeout:
                print(f"  ⏱️ Pas de réponse broadcast")
        
        sock.close()
        
    except Exception as e:
        print(f"  ❌ Erreur broadcast: {e}")
    
    return False

def test_tcp_on_udp_ports():
    """Test TCP sur les ports UDP configurés"""
    host = "**************"
    ports = [50001, 50002, 50003]
    
    print(f"\n🧪 Test TCP sur les ports UDP...")
    
    for port in ports:
        print(f"  Test TCP port {port}")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2.0)
            
            result = sock.connect_ex((host, port))
            if result == 0:
                print(f"  ✅ TCP connecté sur port {port} !")
                
                # Tester commande CI-V
                civ_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
                sock.send(civ_cmd)
                
                try:
                    response = sock.recv(1024)
                    print(f"  ✅ Réponse TCP: {response.hex().upper()}")
                    sock.close()
                    return True, port
                except socket.timeout:
                    print(f"  ⏱️ Pas de réponse TCP")
                
                sock.close()
            else:
                print(f"  ❌ TCP refusé sur port {port}")
                
        except Exception as e:
            print(f"  ❌ Erreur TCP port {port}: {e}")
    
    return False, None

if __name__ == "__main__":
    print("=" * 70)
    print("🧪 TEST UDP AVANCÉ IC-R8600")
    print("=" * 70)
    
    results = []
    
    # Test 1: UDP CI-V brut
    udp_success, udp_port = test_udp_raw_civ()
    results.append(("UDP CI-V brut", udp_success))
    
    # Test 2: Protocole ICOM
    icom_success = test_udp_icom_protocol()
    results.append(("Protocole ICOM", icom_success))
    
    # Test 3: Découverte broadcast
    broadcast_success = test_broadcast_discovery()
    results.append(("Découverte broadcast", broadcast_success))
    
    # Test 4: TCP sur ports UDP
    tcp_success, tcp_port = test_tcp_on_udp_ports()
    results.append(("TCP sur ports UDP", tcp_success))
    
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ DES TESTS AVANCÉS")
    print("=" * 70)
    
    for test_name, success in results:
        status = "✅ OK" if success else "❌ ÉCHEC"
        print(f"{test_name}: {status}")
    
    if any(result[1] for result in results):
        print("\n🎉 Au moins un protocole fonctionne !")
        if udp_success:
            print(f"✅ UDP CI-V fonctionnel sur port {udp_port}")
        if tcp_success:
            print(f"✅ TCP fonctionnel sur port {tcp_port}")
    else:
        print("\n⚠️ Aucun protocole ne répond")
        print("🔧 Vérifications nécessaires:")
        print("   - Network Control vraiment activé ?")
        print("   - IC-R8600 redémarré après configuration ?")
        print("   - Firewall bloquant les ports ?")
        print("   - Configuration réseau correcte ?")
    
    print("=" * 70)
