#!/usr/bin/env python3
"""
Test de la fonction scan
"""

import requests
import json

BASE_URL = "http://localhost:8001"

def test_scan():
    """Test scan de fréquences"""
    print("🔍 TEST SCAN DE FRÉQUENCES")
    print("=" * 50)
    
    # Paramètres du scan
    scan_data = {
        "start_frequency_mhz": 88.0,
        "end_frequency_mhz": 90.0,
        "step_khz": 200,
        "mode": "FM"
    }
    
    print(f"Scan: {scan_data['start_frequency_mhz']}-{scan_data['end_frequency_mhz']} MHz")
    print(f"Pas: {scan_data['step_khz']} kHz")
    print(f"Mode: {scan_data['mode']}")
    print("⚠️  OBSERVEZ L'IC-R8600 pendant le scan !")
    
    response = requests.post(f"{BASE_URL}/api/scan", json=scan_data)
    
    print(f"\nStatus: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Succès: {result['success']}")
        print(f"✅ Fréquences scannées: {result['total_frequencies']}")
        
        print("\n📊 RÉSULTATS DU SCAN:")
        for freq_data in result['scan_results']:
            status = "🔴 ACTIF" if freq_data['active'] else "⚪ Libre"
            print(f"  {freq_data['frequency_display']} MHz: Signal {freq_data['signal_strength']} {status}")
    else:
        print(f"❌ Erreur: {response.text}")

if __name__ == "__main__":
    test_scan()
