#!/usr/bin/env python3
"""
FastAPI Server pour IC-R8600 USB Distant
Contrôle via VirtualHere avec gestion robuste FH
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
import logging
import asyncio
import time
import csv
import os
from datetime import datetime

from ic_r8600_usb_remote import ic_r8600, lifespan

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Création de l'application FastAPI
app = FastAPI(
    title="IC-R8600 USB Remote Controller",
    description="Contrôle IC-R8600 via USB distant (VirtualHere) sur réseau FH",
    version="2.0.0",
    lifespan=lifespan
)

# Configuration CORS pour React
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Modèles Pydantic
class FrequencyRequest(BaseModel):
    frequency_mhz: float

class ModeRequest(BaseModel):
    mode: str

class ConnectionRequest(BaseModel):
    port: Optional[str] = None

class LogEntry(BaseModel):
    timestamp: str
    frequency: int
    mode: str
    rssi: float
    signal_strength: int

# Variables globales
csv_log_file = "ic_r8600_log.csv"
connection_status = {"last_check": 0, "status": "unknown"}

# Endpoints principaux
@app.get("/")
async def root():
    """Page d'accueil"""
    return {
        "message": "IC-R8600 USB Remote Controller",
        "version": "2.0.0",
        "connection_type": "USB_VirtualHere",
        "docs": "/docs",
        "status": "running"
    }

@app.get("/api/status")
async def get_status():
    """Récupère l'état complet du système"""
    try:
        status = ic_r8600.get_status()
        
        # Ajouter informations système
        status.update({
            "server_time": datetime.now().isoformat(),
            "connection_method": "USB_Remote_VirtualHere",
            "fh_optimized": True,
            "auto_reconnect": True
        })
        
        return status
        
    except Exception as e:
        logger.error(f"Erreur get_status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/connect")
async def connect_device(request: ConnectionRequest):
    """Connexion au dispositif IC-R8600"""
    try:
        success = ic_r8600.connect(request.port)
        
        if success:
            logger.info("Connexion IC-R8600 établie via USB distant")
            return {
                "success": True,
                "message": "IC-R8600 connecté via USB distant",
                "port": ic_r8600.status.port
            }
        else:
            raise HTTPException(
                status_code=503,
                detail="Impossible de se connecter à l'IC-R8600. Vérifiez VirtualHere."
            )
            
    except Exception as e:
        logger.error(f"Erreur connexion: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/disconnect")
async def disconnect_device():
    """Déconnexion du dispositif"""
    try:
        ic_r8600.disconnect()
        return {"success": True, "message": "IC-R8600 déconnecté"}
        
    except Exception as e:
        logger.error(f"Erreur déconnexion: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/set_frequency")
async def set_frequency(request: FrequencyRequest, background_tasks: BackgroundTasks):
    """Définit la fréquence"""
    try:
        freq_hz = int(request.frequency_mhz * 1000000)
        
        if not (0.1 <= request.frequency_mhz <= 3000):
            raise HTTPException(
                status_code=400,
                detail="Fréquence hors limites (0.1 - 3000 MHz)"
            )
        
        success = ic_r8600.set_frequency(freq_hz)
        
        if success:
            # Log en arrière-plan
            background_tasks.add_task(log_activity, "set_frequency", freq_hz)
            
            return {
                "success": True,
                "frequency_mhz": request.frequency_mhz,
                "frequency_hz": freq_hz
            }
        else:
            raise HTTPException(
                status_code=503,
                detail="Échec définition fréquence"
            )
            
    except ValueError:
        raise HTTPException(status_code=400, detail="Fréquence invalide")
    except Exception as e:
        logger.error(f"Erreur set_frequency: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/get_frequency")
async def get_frequency():
    """Lit la fréquence actuelle"""
    try:
        freq_hz = ic_r8600.get_frequency()
        
        if freq_hz is not None:
            return {
                "frequency_hz": freq_hz,
                "frequency_mhz": freq_hz / 1000000
            }
        else:
            raise HTTPException(
                status_code=503,
                detail="Impossible de lire la fréquence"
            )
            
    except Exception as e:
        logger.error(f"Erreur get_frequency: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/set_mode")
async def set_mode(request: ModeRequest, background_tasks: BackgroundTasks):
    """Définit le mode de réception"""
    try:
        valid_modes = ['LSB', 'USB', 'AM', 'CW', 'FM', 'WFM', 'CWR', 'RTTY']
        
        if request.mode not in valid_modes:
            raise HTTPException(
                status_code=400,
                detail=f"Mode invalide. Modes supportés: {valid_modes}"
            )
        
        success = ic_r8600.set_mode(request.mode)
        
        if success:
            background_tasks.add_task(log_activity, "set_mode", request.mode)
            
            return {
                "success": True,
                "mode": request.mode
            }
        else:
            raise HTTPException(
                status_code=503,
                detail="Échec définition mode"
            )
            
    except Exception as e:
        logger.error(f"Erreur set_mode: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/get_signal_strength")
async def get_signal_strength():
    """Lit la force du signal"""
    try:
        signal = ic_r8600.get_signal_strength()
        rssi = ic_r8600.get_rssi()
        
        return {
            "signal_strength": signal,
            "rssi_dbm": rssi,
            "s_meter": signal
        }
        
    except Exception as e:
        logger.error(f"Erreur get_signal_strength: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/scan_ports")
async def scan_ports():
    """Scanne les ports COM disponibles"""
    try:
        import serial.tools.list_ports
        
        ports = []
        for port in serial.tools.list_ports.comports():
            ports.append({
                "device": port.device,
                "description": port.description,
                "hwid": port.hwid,
                "is_icom": any(keyword in port.description.upper() 
                              for keyword in ['ICOM', 'IC-R8600', 'R8600'])
            })
        
        return {"ports": ports}
        
    except Exception as e:
        logger.error(f"Erreur scan_ports: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/logs")
async def get_logs(limit: int = 100):
    """Récupère les logs d'activité"""
    try:
        logs = []
        
        if os.path.exists(csv_log_file):
            with open(csv_log_file, 'r', newline='') as file:
                reader = csv.DictReader(file)
                logs = list(reader)[-limit:]  # Derniers logs
        
        return {"logs": logs}
        
    except Exception as e:
        logger.error(f"Erreur get_logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/logs")
async def clear_logs():
    """Efface les logs"""
    try:
        if os.path.exists(csv_log_file):
            os.remove(csv_log_file)
        
        return {"success": True, "message": "Logs effacés"}
        
    except Exception as e:
        logger.error(f"Erreur clear_logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/health")
async def health_check():
    """Vérification de santé du système"""
    try:
        status = ic_r8600.get_status()
        
        health = {
            "status": "healthy" if status["connected"] else "degraded",
            "connection": status["connected"],
            "last_update": status.get("last_update", 0),
            "uptime": status.get("uptime", 0),
            "virtualhere": "active" if status["connected"] else "inactive"
        }
        
        return health
        
    except Exception as e:
        logger.error(f"Erreur health_check: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }

# Fonctions utilitaires
async def log_activity(action: str, value: Any):
    """Log des activités en CSV"""
    try:
        # Créer le fichier CSV s'il n'existe pas
        file_exists = os.path.exists(csv_log_file)
        
        with open(csv_log_file, 'a', newline='') as file:
            fieldnames = ['timestamp', 'action', 'value', 'frequency', 'mode', 'rssi', 'signal_strength']
            writer = csv.DictWriter(file, fieldnames=fieldnames)
            
            if not file_exists:
                writer.writeheader()
            
            # Récupérer l'état actuel
            status = ic_r8600.get_status()
            
            writer.writerow({
                'timestamp': datetime.now().isoformat(),
                'action': action,
                'value': str(value),
                'frequency': status.get('frequency', 0),
                'mode': status.get('mode', ''),
                'rssi': status.get('rssi', 0),
                'signal_strength': status.get('signal_strength', 0)
            })
            
    except Exception as e:
        logger.error(f"Erreur log_activity: {e}")

# Tâche de monitoring en arrière-plan
@app.on_event("startup")
async def startup_event():
    """Événements de démarrage"""
    logger.info("Démarrage serveur IC-R8600 USB Remote")
    
    # Créer tâche de monitoring
    asyncio.create_task(monitor_connection())

async def monitor_connection():
    """Monitoring de connexion en arrière-plan"""
    while True:
        try:
            await asyncio.sleep(30)  # Check toutes les 30 secondes
            
            if ic_r8600.status.connected:
                # Test de connectivité
                freq = ic_r8600.get_frequency()
                if freq is None:
                    logger.warning("Perte de connexion détectée")
                    ic_r8600.connect()
            else:
                # Tentative de reconnexion
                logger.info("Tentative de reconnexion automatique")
                ic_r8600.connect()
                
        except Exception as e:
            logger.error(f"Erreur monitoring: {e}")

# Servir les fichiers statiques React
if os.path.exists("../frontend/dist"):
    app.mount("/static", StaticFiles(directory="../frontend/dist/static"), name="static")
    
    @app.get("/{full_path:path}")
    async def serve_react_app(full_path: str):
        """Servir l'application React"""
        if full_path.startswith("api/"):
            raise HTTPException(status_code=404)
        return FileResponse("../frontend/dist/index.html")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main_usb_remote:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
