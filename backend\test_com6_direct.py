#!/usr/bin/env python3
"""
Test direct COM6 - Diagnostic VirtualHere
"""

import serial
import serial.tools.list_ports
import time

def test_com6_direct():
    """Test direct du port COM6"""
    print("🔍 TEST DIRECT COM6 - DIAGNOSTIC VIRTUALHERE")
    print("=" * 60)
    
    # 1. Lister tous les ports
    print("1. PORTS COM DISPONIBLES:")
    ports = serial.tools.list_ports.comports()
    for port in ports:
        print(f"   {port.device}: {port.description}")
        if 'COM6' in port.device:
            print(f"   ✅ COM6 détecté: {port.description}")
    
    # 2. Test d'ouverture COM6
    print(f"\n2. TEST OUVERTURE COM6:")
    try:
        print("   Tentative d'ouverture...")
        ser = serial.Serial('COM6', 19200, timeout=1)
        print("   ✅ COM6 ouvert avec succès")
        
        # 3. Test écriture/lecture
        print(f"\n3. TEST COMMUNICATION:")
        
        # Commande CI-V simple
        cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x19, 0x00, 0xFD])
        print(f"   Envoi commande: {cmd.hex().upper()}")
        
        ser.write(cmd)
        time.sleep(0.5)
        
        response = ser.read(20)
        if response:
            print(f"   ✅ Réponse reçue: {response.hex().upper()}")
        else:
            print(f"   ⚠️  Aucune réponse")
        
        ser.close()
        print("   ✅ COM6 fermé proprement")
        
        return True
        
    except serial.SerialException as e:
        print(f"   ❌ Erreur série: {e}")
        if "PermissionError" in str(e):
            print("   💡 Solution: Redémarrez VirtualHere Client")
        elif "FileNotFoundError" in str(e):
            print("   💡 Solution: Vérifiez que l'IC-R8600 est 'Use this device'")
        return False
        
    except Exception as e:
        print(f"   ❌ Erreur générale: {e}")
        return False

def check_virtualhere_status():
    """Vérifier le statut VirtualHere"""
    print(f"\n4. STATUT VIRTUALHERE:")
    
    import subprocess
    try:
        # Vérifier VirtualHere Client
        result = subprocess.run(['tasklist', '/fi', 'imagename eq vhui64.exe'], 
                              capture_output=True, text=True)
        
        if 'vhui64.exe' in result.stdout:
            print("   ✅ VirtualHere Client: ACTIF")
        else:
            print("   ❌ VirtualHere Client: INACTIF")
            
    except Exception as e:
        print(f"   ❌ Erreur vérification: {e}")

if __name__ == "__main__":
    test_com6_direct()
    check_virtualhere_status()
    
    print(f"\n" + "=" * 60)
    print("DIAGNOSTIC TERMINÉ")
    print("=" * 60)
    input("Appuyez sur Entrée pour quitter...")
