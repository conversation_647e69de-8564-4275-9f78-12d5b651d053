#!/usr/bin/env python3
"""
Activateur Network Control pour IC-R8600
Utilise la commande CI-V 0109 pour forcer l'activation du Network Control
Basé sur le guide CI-V officiel ICOM
"""

import socket
import time
import logging
from typing import Optional, List

class ICR8600NetworkActivator:
    """Activateur Network Control pour IC-R8600 via CI-V"""
    
    def __init__(self, host="**************"):
        self.host = host
        self.logger = logging.getLogger(__name__)
        
        # Adresses CI-V
        self.ICOM_ADDRESS = 0x94  # IC-R8600
        self.CONTROLLER_ADDRESS = 0xE0
        
        # Commandes CI-V spécifiques (du guide officiel)
        self.CMD_NETWORK_CONTROL = [0x01, 0x09]  # Commande Network Control
        self.CMD_POWER_ON = [0x18, 0x01]
        self.CMD_POWER_OFF = [0x18, 0x00]
        
    def _build_ci_v_command(self, command: List[int], data: List[int] = None) -> bytes:
        """Construit une commande CI-V complète"""
        cmd = [0xFE, 0xFE, self.ICOM_ADDRESS, self.CONTROLLER_ADDRESS]
        cmd.extend(command)
        if data:
            cmd.extend(data)
        cmd.append(0xFD)
        return bytes(cmd)
    
    def _send_via_telnet(self, ci_v_command: bytes) -> bool:
        """Envoie une commande CI-V via Telnet"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5.0)
            sock.connect((self.host, 23))
            
            # Configuration pour CI-V
            init_commands = [
                b'*CLS\r\n',
                b'SYST:COMM:SER:ECHO OFF\r\n',
                b'FORM:BORD NORM\r\n',
            ]
            
            for cmd in init_commands:
                sock.sendall(cmd)
                time.sleep(0.2)
                try:
                    sock.recv(1024)  # Vider le buffer
                except socket.timeout:
                    pass
            
            # Envoyer la commande CI-V
            self.logger.info(f"Envoi CI-V: {ci_v_command.hex().upper()}")
            sock.sendall(ci_v_command)
            time.sleep(1.0)
            
            # Essayer de lire la réponse
            try:
                response = sock.recv(1024)
                self.logger.info(f"Réponse: {response.hex().upper()}")
                
                # Vérifier ACK (FB) ou NAK (FA)
                if b'\xFB' in response:
                    self.logger.info("ACK reçu - Commande acceptée")
                    sock.close()
                    return True
                elif b'\xFA' in response:
                    self.logger.warning("NAK reçu - Commande refusée")
                else:
                    self.logger.info("Réponse reçue")
                    
            except socket.timeout:
                self.logger.info("Pas de réponse (normal pour certaines commandes)")
            
            sock.close()
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur Telnet: {e}")
            return False
    
    def activate_network_control(self) -> bool:
        """Active le Network Control via commande CI-V 0109"""
        self.logger.info("Activation Network Control via CI-V 0109...")
        
        # Commande CI-V pour activer Network Control (01 = ON)
        cmd = self._build_ci_v_command(self.CMD_NETWORK_CONTROL, [0x01])
        
        if self._send_via_telnet(cmd):
            self.logger.info("Commande Network Control ON envoyée")
            
            # Attendre un peu pour que la commande soit traitée
            time.sleep(3)
            
            # Vérifier l'état
            return self.check_network_control_status()
        
        return False
    
    def deactivate_network_control(self) -> bool:
        """Désactive le Network Control via commande CI-V 0109"""
        self.logger.info("Désactivation Network Control via CI-V 0109...")
        
        # Commande CI-V pour désactiver Network Control (00 = OFF)
        cmd = self._build_ci_v_command(self.CMD_NETWORK_CONTROL, [0x00])
        
        if self._send_via_telnet(cmd):
            self.logger.info("Commande Network Control OFF envoyée")
            return True
        
        return False
    
    def check_network_control_status(self) -> bool:
        """Vérifie l'état du Network Control via commande CI-V 0109"""
        self.logger.info("Vérification état Network Control...")
        
        # Commande CI-V pour lire l'état Network Control (sans data = lecture)
        cmd = self._build_ci_v_command(self.CMD_NETWORK_CONTROL)
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5.0)
            sock.connect((self.host, 23))
            
            # Envoyer la commande de lecture
            sock.sendall(cmd)
            time.sleep(1.0)
            
            try:
                response = sock.recv(1024)
                self.logger.info(f"Réponse état: {response.hex().upper()}")
                
                # Analyser la réponse
                if b'\x01\x09\x01' in response:
                    self.logger.info("Network Control: ON")
                    sock.close()
                    return True
                elif b'\x01\x09\x00' in response:
                    self.logger.info("Network Control: OFF")
                    sock.close()
                    return False
                else:
                    self.logger.warning("Réponse état non reconnue")
                    
            except socket.timeout:
                self.logger.warning("Pas de réponse pour vérification état")
            
            sock.close()
            
        except Exception as e:
            self.logger.error(f"Erreur vérification état: {e}")
        
        return False
    
    def test_network_ports_after_activation(self) -> bool:
        """Test les ports réseau après activation"""
        self.logger.info("Test des ports réseau après activation...")
        
        ports = [50001, 50002, 50003]
        active_ports = []
        
        for port in ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                sock.settimeout(2.0)
                
                # Test avec commande CI-V simple
                test_cmd = self._build_ci_v_command([0x19, 0x00])  # Status
                sock.sendto(test_cmd, (self.host, port))
                
                try:
                    response, addr = sock.recvfrom(1024)
                    self.logger.info(f"Port {port}: ACTIF - {response.hex().upper()}")
                    active_ports.append(port)
                except socket.timeout:
                    self.logger.info(f"Port {port}: Inactif")
                
                sock.close()
                
            except Exception as e:
                self.logger.error(f"Erreur test port {port}: {e}")
        
        if active_ports:
            self.logger.info(f"Ports actifs après activation: {active_ports}")
            return True
        else:
            self.logger.warning("Aucun port UDP actif après activation")
            return False
    
    def force_restart_network(self) -> bool:
        """Force un redémarrage du système réseau"""
        self.logger.info("Forçage redémarrage système réseau...")
        
        try:
            # Séquence de redémarrage réseau
            commands = [
                # Désactiver Network Control
                self._build_ci_v_command(self.CMD_NETWORK_CONTROL, [0x00]),
                # Attendre
                None,  # Pause
                # Réactiver Network Control
                self._build_ci_v_command(self.CMD_NETWORK_CONTROL, [0x01]),
            ]
            
            for i, cmd in enumerate(commands):
                if cmd is None:
                    self.logger.info("Pause 5 secondes...")
                    time.sleep(5)
                    continue
                
                self.logger.info(f"Étape {i+1}: {cmd.hex().upper()}")
                if not self._send_via_telnet(cmd):
                    self.logger.error(f"Échec étape {i+1}")
                    return False
                
                time.sleep(2)
            
            # Test final
            time.sleep(5)
            return self.test_network_ports_after_activation()
            
        except Exception as e:
            self.logger.error(f"Erreur redémarrage réseau: {e}")
            return False
    
    def complete_network_activation_sequence(self) -> bool:
        """Séquence complète d'activation réseau"""
        self.logger.info("=" * 60)
        self.logger.info("SÉQUENCE COMPLÈTE D'ACTIVATION NETWORK CONTROL")
        self.logger.info("=" * 60)
        
        # Étape 1: Vérifier l'état actuel
        self.logger.info("Étape 1: Vérification état actuel...")
        current_state = self.check_network_control_status()
        
        # Étape 2: Activer si nécessaire
        if not current_state:
            self.logger.info("Étape 2: Activation Network Control...")
            if not self.activate_network_control():
                self.logger.error("Échec activation Network Control")
                
                # Étape 3: Tentative de redémarrage forcé
                self.logger.info("Étape 3: Tentative redémarrage forcé...")
                return self.force_restart_network()
        else:
            self.logger.info("Network Control déjà activé")
        
        # Étape 4: Test final des ports
        self.logger.info("Étape 4: Test final des ports...")
        return self.test_network_ports_after_activation()

def test_network_activator():
    """Test de l'activateur Network Control"""
    print("🔧 TEST ACTIVATEUR NETWORK CONTROL IC-R8600")
    print("=" * 60)
    print("Utilise la commande CI-V 0109 du guide officiel ICOM")
    print("=" * 60)
    
    # Configuration logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    activator = ICR8600NetworkActivator("**************")
    
    try:
        # Séquence complète
        if activator.complete_network_activation_sequence():
            print("\n✅ SUCCÈS ! Network Control activé et ports ouverts")
            print("Vous pouvez maintenant utiliser l'application avec contrôle réseau")
        else:
            print("\n❌ ÉCHEC de l'activation Network Control")
            print("Le problème peut être :")
            print("1. Version firmware 1.33 incompatible")
            print("2. Configuration réseau incorrecte")
            print("3. Problème matériel")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_network_activator()
