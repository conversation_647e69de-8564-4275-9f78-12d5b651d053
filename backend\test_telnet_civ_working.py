#!/usr/bin/env python3
"""
Test CI-V via Telnet - Version fonctionnelle
Nous savons que le port 23 répond, testons-le directement
"""

import socket
import time
import sys

def test_telnet_civ_communication():
    """Test de communication CI-V via Telnet"""
    host = "**************"
    port = 23
    
    print("🔌 CONNEXION TELNET CI-V")
    print("=" * 40)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10.0)
        sock.connect((host, port))
        print(f"✅ Connecté à {host}:{port}")
        
        # Lire le message de bienvenue
        welcome = sock.recv(2048)
        print(f"📥 Bienvenue ({len(welcome)} bytes)")
        
        # Afficher le texte lisible
        welcome_text = welcome.decode('ascii', errors='ignore')
        if "IC-R8600" in welcome_text:
            print("✅ IC-R8600 identifié")
        
        # Test 1: Commande CI-V de lecture de fréquence
        print("\n📤 Test 1: Lecture de fréquence")
        ci_v_freq = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
        print(f"Envoi: {ci_v_freq.hex().upper()}")
        
        sock.send(ci_v_freq)
        time.sleep(1)
        
        response1 = sock.recv(1024)
        print(f"Réponse: {response1.hex().upper()}")
        
        # Analyser la réponse
        if len(response1) > 0:
            print(f"📊 {len(response1)} bytes reçus")
            
            # Chercher des patterns CI-V
            response_text = response1.decode('ascii', errors='ignore')
            print(f"Texte: {repr(response_text)}")
            
            # Chercher FE FE dans la réponse
            if b'\xFE\xFE' in response1:
                print("🎯 Pattern CI-V FE FE détecté !")
                
                # Extraire la commande CI-V
                fe_pos = response1.find(b'\xFE\xFE')
                if fe_pos >= 0:
                    # Chercher le FD de fin
                    fd_pos = response1.find(b'\xFD', fe_pos)
                    if fd_pos >= 0:
                        ci_v_data = response1[fe_pos:fd_pos+1]
                        print(f"✅ Commande CI-V complète: {ci_v_data.hex().upper()}")
                        
                        # Décoder la commande
                        if len(ci_v_data) >= 6:
                            src = ci_v_data[2]
                            dst = ci_v_data[3]
                            cmd = ci_v_data[4]
                            data = ci_v_data[5:-1]
                            
                            print(f"   Source: 0x{src:02X}")
                            print(f"   Destination: 0x{dst:02X}")
                            print(f"   Commande: 0x{cmd:02X}")
                            print(f"   Données: {data.hex().upper()}")
                            
                            # Si c'est une réponse de fréquence (commande 03)
                            if cmd == 0x03 and len(data) >= 5:
                                print("🎯 Réponse de fréquence détectée !")
                                try:
                                    # Décoder la fréquence BCD
                                    freq_hz = 0
                                    for byte in reversed(data):
                                        freq_hz = freq_hz * 100 + (byte >> 4) * 10 + (byte & 0x0F)
                                    freq_mhz = freq_hz / 1000000
                                    print(f"📻 Fréquence actuelle: {freq_mhz:.6f} MHz")
                                except Exception as e:
                                    print(f"⚠️ Erreur décodage fréquence: {e}")
        
        # Test 2: Commande CI-V de lecture de mode
        print("\n📤 Test 2: Lecture de mode")
        ci_v_mode = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x04, 0xFD])
        print(f"Envoi: {ci_v_mode.hex().upper()}")
        
        sock.send(ci_v_mode)
        time.sleep(1)
        
        response2 = sock.recv(1024)
        print(f"Réponse: {response2.hex().upper()}")
        
        if b'\xFE\xFE' in response2:
            print("🎯 Réponse CI-V mode détectée !")
        
        # Test 3: Commande de statut
        print("\n📤 Test 3: Statut")
        ci_v_status = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x19, 0x00, 0xFD])
        print(f"Envoi: {ci_v_status.hex().upper()}")
        
        sock.send(ci_v_status)
        time.sleep(1)
        
        response3 = sock.recv(1024)
        print(f"Réponse: {response3.hex().upper()}")
        
        if b'\xFE\xFE' in response3:
            print("🎯 Réponse CI-V statut détectée !")
        
        sock.close()
        print("🔌 Connexion fermée")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_telnet_civ_set_frequency():
    """Test de changement de fréquence via Telnet CI-V"""
    host = "**************"
    port = 23
    
    print("\n🎛️ TEST CHANGEMENT DE FRÉQUENCE")
    print("=" * 40)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        sock.connect((host, port))
        
        # Lire le prompt
        sock.recv(1024)
        
        # Fréquence à définir: 145.500 MHz = 145500000 Hz
        # En BCD: 00 00 50 45 01 (little endian)
        freq_bcd = bytes([0x00, 0x00, 0x50, 0x45, 0x01])
        
        # Commande CI-V de changement de fréquence
        ci_v_set_freq = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x05]) + freq_bcd + bytes([0xFD])
        
        print(f"📤 Changement fréquence vers 145.500 MHz")
        print(f"Envoi: {ci_v_set_freq.hex().upper()}")
        
        sock.send(ci_v_set_freq)
        time.sleep(1)
        
        response = sock.recv(1024)
        print(f"Réponse: {response.hex().upper()}")
        
        if b'\xFE\xFE' in response:
            print("✅ Commande de fréquence acceptée !")
        
        # Vérifier en relisant la fréquence
        print("\n📤 Vérification: lecture de la nouvelle fréquence")
        ci_v_read_freq = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
        
        sock.send(ci_v_read_freq)
        time.sleep(1)
        
        verify_response = sock.recv(1024)
        print(f"Réponse: {verify_response.hex().upper()}")
        
        sock.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 TEST CI-V VIA TELNET IC-R8600")
    print("=" * 60)
    
    # Test 1: Communication de base
    comm_success = test_telnet_civ_communication()
    
    # Test 2: Changement de fréquence
    if comm_success:
        freq_success = test_telnet_civ_set_frequency()
    else:
        freq_success = False
    
    print("\n" + "=" * 60)
    print("📊 RÉSULTATS")
    print("=" * 60)
    
    if comm_success:
        print("✅ Communication CI-V via Telnet FONCTIONNE !")
        print("🎯 L'IC-R8600 accepte les commandes CI-V sur le port 23")
        print("📡 Protocol: TCP sur port 23 (Telnet)")
        
        if freq_success:
            print("✅ Contrôle de fréquence fonctionnel")
        
        print("\n💡 SOLUTION TROUVÉE :")
        print("   - Utiliser TCP port 23 au lieu d'UDP 50001")
        print("   - Envoyer les commandes CI-V directement via Telnet")
        print("   - Adapter l'application pour ce protocole")
        
    else:
        print("❌ Communication CI-V via Telnet échouée")
        print("🔧 Vérifications supplémentaires nécessaires")
    
    print("=" * 60)
