"""
Serveur FastAPI pour contrôler l'ICOM IC-R8600
Interface REST pour communication CI-V et enregistrement audio
"""

import os
import logging
from datetime import datetime
from typing import List, Optional
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles

from models import (
    CommandRequest, CommandResponse, RadioStatusResponse,
    ScanRequest, AudioRecordingRequest, AudioRecordingResponse,
    AudioRecordingStatus, RecordingInfo, AudioDevice, ErrorResponse
)
from icom_handler import ICOMHandler
from network_handler import ICOMNetworkBridge
from icom_network_handler import ICOMNetworkHandler, NetworkConfig
from icom_udp_handler import ICOMUDPHandler, UDPConfig
from icom_network_protocol import ICOMNetworkProtocol, ICOMNetworkConfig
from ic_r8600_v133_network import ICR8600_V133_Network
from ic_r8600_telnet_final import ICR8600TelnetFinal
from ic_r8600_usb_remote import ICR<PERSON>00US<PERSON>emote
from ic_r8600_usb_remote import ic_r8600, lifespan
from audio_recorder import AudioRecorder

# Configuration
app = FastAPI(
    title="ICOM IC-R8600 Controller API",
    description="API REST pour contrôler le récepteur ICOM IC-R8600 via CI-V",
    version="1.0.0"
)

# Configuration CORS pour le frontend React
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Permettre toutes les origines pour le développement
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Instances globales
icom_handler = None
network_handler = None
network_bridge = None
audio_recorder = None
use_network_mode = False  # Flag pour choisir le mode de communication
connection_status = {
    "network": False,
    "serial": False,
    "active_connection": None
}

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('backend.log')
    ]
)
logger = logging.getLogger(__name__)

@app.on_event("startup")
async def startup_event():
    """Initialisation au démarrage avec priorité réseau"""
    global icom_handler, network_handler, usb_handler, network_bridge, audio_recorder, use_network_mode, connection_status

    logger.info("Demarrage du controleur IC-R8600")

    # SOLUTION USB DISTANTE: Contrôle via VirtualHere
    logger.info("Contrôle IC-R8600 via USB distant - Solution VirtualHere")
    logger.info("PC distant: 192.168.37.2, PC supervision: 192.168.37.10")

    # Étape 1: Tentative connexion USB distante (VirtualHere)
    usb_handler = ICR8600USBRemote()
    use_usb_mode = False

    # Tentative de connexion USB distante
    if usb_handler.connect():
        logger.info("Connexion USB distante etablie - IC-R8600 controlable via VirtualHere")
        use_usb_mode = True
        connection_status["usb_remote"] = True
        connection_status["active_connection"] = "usb_remote_virtualhere"
        logger.info("Mode USB distant actif - Interface web disponible")
        logger.info("SUCCÈS ! Contrôle USB distant opérationnel via VirtualHere")
    else:
        logger.warning("Connexion USB distante echouee, tentative réseau...")

        # Fallback: Connexion réseau Telnet
        network_handler = ICR8600TelnetFinal("192.168.37.100")
        if network_handler.connect():
            logger.info("Fallback réseau Telnet etabli")
            connection_status["network"] = True
            connection_status["active_connection"] = "network_telnet_fallback"
        else:
            logger.error("Échec connexion USB distant et réseau")
            logger.error("Verifiez:")
            logger.error("   - VirtualHere Server actif sur PC distant")
            logger.error("   - VirtualHere Client connecté")
            logger.error("   - IC-R8600 'Use this device' activé")
            logger.error("   - Port COM visible dans gestionnaire périphériques")
            connection_status["usb_remote"] = False
            connection_status["network"] = False
            connection_status["active_connection"] = None

    # Étape 2: Mode dégradé si pas de connexion complète
    if not use_network_mode:
        logger.warning("Mode degrade - Connexion limitee")
        logger.info("L'application fonctionnera en mode monitoring uniquement")
        # Créer un handler factice pour éviter les erreurs
        class DummyHandler:
            def __init__(self):
                self.connected = False
            def get_status(self):
                return {"connected": False, "mode": "simulation"}
            def power_on(self): return False
            def power_off(self): return False
            def set_frequency(self, freq): return False
            def get_frequency(self): return None
            def set_mode(self, mode): return False
            def get_mode(self): return None

        network_handler = DummyHandler()
        connection_status["active_connection"] = "simulation"

    # Étape 3: Configuration globale
    logger.info("Configuration du controleur IC-R8600")
    app.state.network_handler = network_handler
    app.state.connection_status = connection_status
    app.state.use_network_mode = use_network_mode

    # Étape 4: Initialisation de l'enregistreur audio (placeholder)
    # TODO: Implémenter l'enregistreur audio complet
    class DummyAudioRecorder:
        def __init__(self):
            self.is_recording = False
        def list_recordings(self):
            return []
        def get_status(self):
            return {"recording": False, "filename": None}
        def start_recording(self, filename=None):
            return False
        def stop_recording(self):
            return False

    audio_recorder = DummyAudioRecorder()

    logger.info("Controleur IC-R8600 initialise avec succes")
    logger.info(f"Mode actif: {connection_status['active_connection']}")
    logger.info("Serveur API pret sur http://localhost:8000")

@app.on_event("shutdown")
async def shutdown_event():
    """Nettoyage à l'arrêt"""
    global icom_handler, network_handler, audio_recorder

    logger.info("Arret du controleur IC-R8600")

    if network_handler:
        network_handler.disconnect()
        logger.info("Connexion réseau fermée")

    if icom_handler:
        icom_handler.disconnect()
        logger.info("Connexion série fermée")

    if audio_recorder and audio_recorder.is_recording:
        audio_recorder.stop_recording()
        logger.info("Enregistrement audio arrêté")

# Fonctions helper pour gestion des connexions

def get_active_handler():
    """Retourne le handler actif (USB distant, réseau ou série)"""
    global network_handler, icom_handler, usb_handler, connection_status

    # Priorité 1: USB distant (VirtualHere)
    if (connection_status["active_connection"] == "usb_remote_virtualhere" and usb_handler):
        return usb_handler
    # Priorité 2: Réseau (Telnet fallback)
    elif (connection_status["active_connection"] in ["network_telnet_fallback", "network", "network_telnet_final", "network_v133_telnet", "network_icom_protocol", "network_udp", "network_tcp_telnet", "network_udp_rj45"]
        and network_handler):
        return network_handler
    # Priorité 3: Série locale
    elif connection_status["active_connection"] == "serial" and icom_handler:
        return icom_handler
    else:
        return None

def execute_command_with_fallback(command_func, *args, **kwargs):
    """Exécute une commande avec fallback automatique"""
    global connection_status

    handler = get_active_handler()
    if not handler:
        logger.error("Aucun handler disponible")
        return False

    try:
        # Tenter avec le handler actif
        result = command_func(handler, *args, **kwargs)

        if result is not None and result is not False:
            return result

        # Si échec et on est en réseau, essayer le fallback série
        if (connection_status["active_connection"] == "network" and
            connection_status["serial"] and icom_handler):

            logger.warning("Commande réseau échouée, fallback vers série")
            connection_status["active_connection"] = "serial"
            return command_func(icom_handler, *args, **kwargs)

        return False

    except Exception as e:
        logger.error(f"Erreur exécution commande: {e}")
        return False

# Routes API

@app.get("/", response_model=dict)
async def root():
    """Page d'accueil de l'API"""
    return {
        "message": "ICOM IC-R8600 Controller API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }

@app.options("/api/command")
async def options_command():
    """Handler pour les requêtes OPTIONS (CORS preflight)"""
    return {"message": "OK"}

@app.post("/api/command", response_model=CommandResponse)
async def send_command(command: CommandRequest):
    """Envoie une commande au récepteur ICOM"""
    logger.info(f"Commande recue: {command}")

    handler = get_active_handler()

    try:
        results = []

        if handler:
            # Mode réel avec handler disponible
            # Traitement des commandes avec fallback automatique
            if command.power_on is not None:
                if command.power_on:
                    success = execute_command_with_fallback(lambda h: h.power_on())
                    results.append(f"Power ON: {'OK' if success else 'ERREUR'}")
                else:
                    success = execute_command_with_fallback(lambda h: h.power_off())
                    results.append(f"Power OFF: {'OK' if success else 'ERREUR'}")

            if command.frequency is not None:
                success = execute_command_with_fallback(lambda h: h.set_frequency(command.frequency))
                results.append(f"Fréquence {command.frequency} Hz: {'OK' if success else 'ERREUR'}")

            if command.mode is not None:
                success = execute_command_with_fallback(lambda h: h.set_mode(command.mode))
                results.append(f"Mode {command.mode}: {'OK' if success else 'ERREUR'}")

            if command.rf_gain is not None:
                # Note: rf_gain peut ne pas être disponible sur le network handler
                if hasattr(handler, 'set_rf_gain'):
                    success = execute_command_with_fallback(lambda h: h.set_rf_gain(command.rf_gain))
                    results.append(f"RF Gain {command.rf_gain}: {'OK' if success else 'ERREUR'}")
                else:
                    results.append(f"RF Gain: Non supporté en mode réseau")
        else:
            # Mode simulation - accepter les commandes mais simuler les réponses
            if command.power_on is not None:
                action = "ON" if command.power_on else "OFF"
                results.append(f"Power {action}: OK (simulation)")

            if command.frequency is not None:
                results.append(f"Fréquence {command.frequency} Hz: OK (simulation)")

            if command.mode is not None:
                results.append(f"Mode {command.mode}: OK (simulation)")

            if command.rf_gain is not None:
                results.append(f"RF Gain {command.rf_gain}: OK (simulation)")

        connection_info = {
            "connection_type": connection_status["active_connection"] or "simulation",
            "timestamp": datetime.now().isoformat(),
            "simulation_mode": handler is None
        }

        return CommandResponse(
            success=True,
            message="; ".join(results) if results else "Aucune commande spécifiée",
            data=connection_info
        )

    except Exception as e:
        logger.error(f"Erreur commande: {e}")
        # Retourner une réponse d'erreur mais pas une exception HTTP
        return CommandResponse(
            success=False,
            message=f"Erreur: {str(e)}",
            data={"error": True, "timestamp": datetime.now().isoformat()}
        )

@app.get("/api/connection/status")
async def get_connection_status():
    """Retourne l'état des connexions"""
    handler = get_active_handler()

    status = {
        "connections": connection_status,
        "active_handler_available": handler is not None,
        "network_details": None,
        "serial_details": None
    }

    # Détails de la connexion réseau
    if network_handler and connection_status["network"]:
        network_status = network_handler.get_status()
        status["network_details"] = {
            "host": network_handler.config.host,
            "port": network_handler.config.port,
            "connection_type": network_handler.config.connection_type.value,
            "connected": network_status.get("connected", False)
        }

    # Détails de la connexion série
    if icom_handler and connection_status["serial"]:
        status["serial_details"] = {
            "port": icom_handler.port,
            "baudrate": icom_handler.baudrate,
            "connected": icom_handler.serial_conn and icom_handler.serial_conn.is_open if icom_handler.serial_conn else False
        }

    return status

@app.post("/api/connection/switch")
async def switch_connection():
    """Bascule entre connexion réseau et série"""
    global connection_status

    if connection_status["active_connection"] == "network" and connection_status["serial"]:
        connection_status["active_connection"] = "serial"
        return {"message": "Basculé vers connexion série", "active": "serial"}
    elif connection_status["active_connection"] == "serial" and connection_status["network"]:
        connection_status["active_connection"] = "network"
        return {"message": "Basculé vers connexion réseau", "active": "network"}
    else:
        raise HTTPException(status_code=400, detail="Impossible de basculer - connexion alternative non disponible")

@app.post("/api/scan/start", response_model=CommandResponse)
async def start_scan(scan_request: ScanRequest):
    """Démarre un scan de fréquences"""
    try:
        if not connection_status["connected"]:
            raise HTTPException(status_code=400, detail="Aucune connexion active")

        # Validation des fréquences
        if scan_request.start_frequency >= scan_request.end_frequency:
            raise HTTPException(status_code=400, detail="Fréquence de début doit être inférieure à la fréquence de fin")

        # Calcul du pas de scan (par défaut 25 kHz si non spécifié)
        step = getattr(scan_request, 'step', 25000)

        # Démarrage du scan
        logger.info(f"Démarrage scan: {scan_request.start_frequency} -> {scan_request.end_frequency}, pas: {step}")

        # Pour l'instant, on simule le démarrage du scan
        # TODO: Implémenter le vrai scan avec threading
        return CommandResponse(
            success=True,
            message=f"Scan démarré de {scan_request.start_frequency/1000000:.3f} MHz à {scan_request.end_frequency/1000000:.3f} MHz",
            simulation_mode=False
        )

    except Exception as e:
        logger.error(f"Erreur démarrage scan: {e}")
        return CommandResponse(
            success=False,
            message=f"Erreur démarrage scan: {str(e)}",
            simulation_mode=False
        )

@app.post("/api/scan/stop", response_model=CommandResponse)
async def stop_scan():
    """Arrête le scan en cours"""
    try:
        logger.info("Arrêt du scan demandé")
        # TODO: Implémenter l'arrêt du scan
        return CommandResponse(
            success=True,
            message="Scan arrêté",
            simulation_mode=False
        )
    except Exception as e:
        logger.error(f"Erreur arrêt scan: {e}")
        return CommandResponse(
            success=False,
            message=f"Erreur arrêt scan: {str(e)}",
            simulation_mode=False
        )

# Routes Audio

@app.post("/api/audio/start", response_model=AudioRecordingResponse)
async def start_audio_recording(recording_request: AudioRecordingRequest):
    """Démarre l'enregistrement audio"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        result = audio_recorder.start_recording(
            audio_type=recording_request.audio_type.value,
            device_id=recording_request.device_id
        )

        if result["success"]:
            return AudioRecordingResponse(**result)
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except Exception as e:
        logger.error(f"Erreur démarrage enregistrement: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/audio/stop", response_model=AudioRecordingResponse)
async def stop_audio_recording():
    """Arrête l'enregistrement audio"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        result = audio_recorder.stop_recording()

        if result["success"]:
            return AudioRecordingResponse(**result)
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except Exception as e:
        logger.error(f"Erreur arrêt enregistrement: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/audio/status", response_model=AudioRecordingStatus)
async def get_audio_status():
    """Récupère l'état de l'enregistrement audio"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        status = audio_recorder.get_recording_status()
        return AudioRecordingStatus(**status)
    except Exception as e:
        logger.error(f"Erreur état enregistrement: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/recordings", response_model=List[RecordingInfo])
async def list_recordings():
    """Liste tous les enregistrements disponibles"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        recordings = audio_recorder.list_recordings()
        return [RecordingInfo(**rec) for rec in recordings]
    except Exception as e:
        logger.error(f"Erreur listage enregistrements: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/recordings/{filename}")
async def download_recording(filename: str):
    """Télécharge un fichier d'enregistrement"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        filepath = os.path.join(audio_recorder.recordings_dir, filename)
        if os.path.exists(filepath):
            return FileResponse(
                filepath,
                media_type="audio/wav",
                filename=filename
            )
        else:
            raise HTTPException(status_code=404, detail="Fichier non trouvé")
    except Exception as e:
        logger.error(f"Erreur téléchargement: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/recordings/{filename}", response_model=CommandResponse)
async def delete_recording(filename: str):
    """Supprime un enregistrement"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        result = audio_recorder.delete_recording(filename)
        return CommandResponse(**result)
    except Exception as e:
        logger.error(f"Erreur suppression: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/audio/devices", response_model=List[AudioDevice])
async def get_audio_devices():
    """Liste les périphériques audio disponibles"""
    if not audio_recorder:
        raise HTTPException(status_code=500, detail="Enregistreur audio non initialisé")

    try:
        devices = audio_recorder.get_audio_devices()
        return [AudioDevice(**device) for device in devices]
    except Exception as e:
        logger.error(f"Erreur listage périphériques: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/status", response_model=RadioStatusResponse)
async def get_status():
    """Récupère l'état actuel du récepteur"""
    handler = get_active_handler()

    try:
        if handler:
            # Essayer de récupérer le statut réel
            if hasattr(handler, 'get_status'):
                status = handler.get_status()
            else:
                # Pour le network handler, construire le statut manuellement
                status = {
                    "frequency": handler.get_frequency() if hasattr(handler, 'get_frequency') else None,
                    "mode": handler.get_mode() if hasattr(handler, 'get_mode') else None,
                    "rssi": handler.get_rssi() if hasattr(handler, 'get_rssi') else None,
                    "power_on": True,  # Assume ON si connecté
                    "rf_gain": 50,     # Valeur par défaut
                    "filter_width": 15000  # Valeur par défaut
                }
        else:
            # Mode simulation si aucune connexion
            status = {
                "frequency": 145500000,
                "mode": "FM",
                "rssi": -80,
                "power_on": True,
                "rf_gain": 50,
                "filter_width": 15000,
                "simulation_mode": True
            }

        # Ajouter les informations de connexion
        if status:
            status["connection_type"] = connection_status["active_connection"]
            status["network_available"] = connection_status.get("network", False)
            status["serial_available"] = connection_status.get("serial", False)
            status["usb_remote_available"] = connection_status.get("usb_remote", False)

            # Ajouter informations VirtualHere si USB distant
            if connection_status["active_connection"] == "usb_remote_virtualhere":
                status.update({
                    "virtualhere_active": True,
                    "distant_pc": "192.168.37.2",
                    "supervision_pc": "192.168.37.10",
                    "usb_bridge": "CP2102 USB to UART Bridge",
                    "com_port": getattr(usb_handler, 'status', {}).get('port', 'COM6')
                })
            else:
                status["virtualhere_active"] = False

        return RadioStatusResponse(
            frequency=status.get("frequency", 145500000),
            mode=status.get("mode", "FM"),
            rssi=status.get("rssi", -80),
            power_on=status.get("power_on", True),
            rf_gain=status.get("rf_gain", 50),
            filter_width=status.get("filter_width", 15000),
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Erreur lecture état: {e}")
        # Retourner des valeurs par défaut en cas d'erreur
        return RadioStatusResponse(
            frequency=145500000,
            mode="FM",
            rssi=-80,
            power_on=True,
            rf_gain=50,
            filter_width=15000,
            timestamp=datetime.now().isoformat()
        )

@app.post("/api/civ_command")
async def send_civ_command(command_hex: str):
    """Envoie une commande CI-V directe en hexadécimal"""
    try:
        logger.info(f"Commande CI-V directe: {command_hex}")

        # Convertir hex en bytes
        command_bytes = bytes.fromhex(command_hex.replace(" ", ""))

        handler = get_active_handler()
        if not handler:
            return {
                "success": False,
                "error": "Aucun handler disponible",
                "response_hex": ""
            }

        # Envoyer la commande directement
        if hasattr(handler, '_send_command_raw'):
            response = handler._send_command_raw(command_bytes)
            if response:
                return {
                    "success": True,
                    "command_hex": command_hex,
                    "response_hex": response.hex().upper(),
                    "response_length": len(response)
                }
            else:
                return {
                    "success": False,
                    "error": "Pas de réponse",
                    "command_hex": command_hex,
                    "response_hex": ""
                }
        else:
            return {
                "success": False,
                "error": "Handler ne supporte pas les commandes directes",
                "response_hex": ""
            }

    except ValueError as e:
        return {
            "success": False,
            "error": f"Format hexadécimal invalide: {e}",
            "response_hex": ""
        }
    except Exception as e:
        logger.error(f"Erreur commande CI-V directe: {e}")
        return {
            "success": False,
            "error": str(e),
            "response_hex": ""
        }



@app.get("/api/virtualhere/status")
async def get_virtualhere_status():
    """Récupère l'état de VirtualHere"""
    try:
        import serial.tools.list_ports

        # Scanner les ports COM
        ports = []
        virtualhere_port = None

        for port in serial.tools.list_ports.comports():
            port_info = {
                "device": port.device,
                "description": port.description,
                "is_virtualhere": False,
                "is_icom": False
            }

            desc = port.description.upper()
            if any(keyword in desc for keyword in ['CP2102', 'CP210', 'SILICON LABS']):
                port_info["is_virtualhere"] = True
                port_info["is_icom"] = True
                virtualhere_port = port.device

            ports.append(port_info)

        return {
            "virtualhere_detected": virtualhere_port is not None,
            "icom_port": virtualhere_port,
            "total_ports": len(ports),
            "ports": ports,
            "connection_active": connection_status.get("active_connection") == "usb_remote_virtualhere"
        }

    except Exception as e:
        logger.error(f"Erreur status VirtualHere: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/virtualhere/reconnect")
async def reconnect_virtualhere():
    """Force la reconnexion VirtualHere"""
    try:
        global usb_handler

        if usb_handler:
            usb_handler.disconnect()
            time.sleep(1)

            if usb_handler.connect():
                connection_status["usb_remote"] = True
                connection_status["active_connection"] = "usb_remote_virtualhere"
                return {"success": True, "message": "Reconnexion VirtualHere réussie"}
            else:
                return {"success": False, "message": "Échec reconnexion VirtualHere"}
        else:
            return {"success": False, "message": "Handler USB non initialisé"}

    except Exception as e:
        logger.error(f"Erreur reconnexion VirtualHere: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
