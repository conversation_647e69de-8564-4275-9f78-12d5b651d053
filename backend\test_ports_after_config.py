#!/usr/bin/env python3
"""
Test des ports après configuration Network Control
"""

import socket
import time

def test_udp_ports():
    """Test des ports UDP après configuration"""
    print("=" * 50)
    print("TEST PORTS UDP APRÈS CONFIGURATION")
    print("=" * 50)
    print("ASSUREZ-VOUS D'AVOIR :")
    print("1. Activé Network Control sur l'IC-R8600")
    print("2. Redémarré l'IC-R8600")
    print("3. Appuyé sur [LOCAL] pour désactiver contrôle local")
    print("=" * 50)
    
    host = "**************"
    ports = [50001, 50002, 50003]
    
    for port in ports:
        print(f"\nTest port UDP {port}:")
        
        try:
            # Test connexion UDP
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(2.0)
            
            # Envoyer un paquet de test
            test_packet = b'\xFE\xFE\x94\xE0\x19\x00\xFD'  # CI-V status query
            sock.sendto(test_packet, (host, port))
            print(f"  Paquet envoyé: {test_packet.hex().upper()}")
            
            try:
                response, addr = sock.recvfrom(1024)
                print(f"  ✅ RÉPONSE REÇUE: {response.hex().upper()}")
                print(f"     Taille: {len(response)} bytes")
                
                # Analyser la réponse CI-V
                if response.startswith(b'\xFE\xFE'):
                    print("     Format CI-V détecté !")
                    if b'\xFB' in response:
                        print("     ACK reçu - Commande acceptée !")
                    elif b'\xFA' in response:
                        print("     NAK reçu - Commande refusée")
                
            except socket.timeout:
                print("  ❌ Pas de réponse (timeout)")
            
            sock.close()
            
        except Exception as e:
            print(f"  ❌ Erreur: {e}")

def test_tcp_ports():
    """Test des ports TCP"""
    print("\n" + "=" * 50)
    print("TEST PORTS TCP")
    print("=" * 50)
    
    host = "**************"
    ports = [23, 50001, 50002, 50003, 80, 8080]
    
    for port in ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2.0)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"✅ Port TCP {port} OUVERT")
            else:
                print(f"❌ Port TCP {port} FERMÉ")
                
        except Exception as e:
            print(f"❌ Port TCP {port} ERREUR: {e}")

def continuous_monitoring():
    """Monitoring continu pour voir quand les ports s'ouvrent"""
    print("\n" + "=" * 50)
    print("MONITORING CONTINU DES PORTS")
    print("=" * 50)
    print("Configurez Network Control sur l'IC-R8600 maintenant...")
    print("Ce script va surveiller l'ouverture des ports en temps réel")
    print("Appuyez sur Ctrl+C pour arrêter")
    
    host = "**************"
    udp_ports = [50001, 50002, 50003]
    
    previous_status = {}
    
    try:
        while True:
            current_status = {}
            
            for port in udp_ports:
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                    sock.settimeout(1.0)
                    
                    # Test simple
                    sock.sendto(b'\x00', (host, port))
                    
                    try:
                        response, addr = sock.recvfrom(1024)
                        current_status[port] = "OUVERT"
                    except socket.timeout:
                        current_status[port] = "FERMÉ"
                    
                    sock.close()
                    
                except Exception:
                    current_status[port] = "ERREUR"
            
            # Afficher seulement si changement
            if current_status != previous_status:
                timestamp = time.strftime("%H:%M:%S")
                print(f"\n[{timestamp}] État des ports:")
                for port, status in current_status.items():
                    print(f"  Port {port}: {status}")
                
                previous_status = current_status.copy()
            
            time.sleep(2)
            
    except KeyboardInterrupt:
        print("\nMonitoring arrêté")

if __name__ == "__main__":
    print("VÉRIFICATION PORTS IC-R8600 APRÈS CONFIGURATION")
    
    # Test initial
    test_tcp_ports()
    test_udp_ports()
    
    # Monitoring continu
    input("\nAppuyez sur Entrée pour démarrer le monitoring continu...")
    continuous_monitoring()
