@echo off
REM ========================================
REM Déploiement complet IC-R8600 USB Remote
REM Solution VirtualHere pour site distant
REM ========================================

echo ========================================
echo DÉPLOIEMENT IC-R8600 USB REMOTE
echo ========================================
echo.
echo Cette solution permet de contrôler un IC-R8600
echo connecté en USB sur un PC distant via réseau FH
echo.
echo Architecture:
echo PC Distant (VirtualHere Server) ^<--FH--^> PC Supervision (VirtualHere Client + FastAPI)
echo.

REM Vérifier si on est sur PC distant ou PC supervision
echo Quel type de PC configurez-vous ?
echo 1. PC Distant (avec IC-R8600 connecté en USB)
echo 2. PC Supervision (avec interface web)
echo.
set /p choice="Votre choix (1 ou 2): "

if "%choice%"=="1" goto :setup_distant
if "%choice%"=="2" goto :setup_supervision
echo Choix invalide !
pause
exit /b 1

:setup_distant
echo.
echo ========================================
echo CONFIGURATION PC DISTANT
echo ========================================
echo.

REM Vérifier la présence de l'IC-R8600
echo Vérification de la connexion IC-R8600...
powershell "Get-WmiObject -Class Win32_USBControllerDevice | ForEach-Object { [wmi]($_.Dependent) } | Where-Object { $_.Description -like '*ICOM*' -or $_.Description -like '*IC-R8600*' -or $_.Description -like '*USB Serial*' } | Select-Object Description, DeviceID"

echo.
echo L'IC-R8600 est-il visible ci-dessus ?
set /p icom_present="(o/n): "

if not "%icom_present%"=="o" (
    echo.
    echo [ATTENTION] IC-R8600 non détecté !
    echo 1. Connectez l'IC-R8600 en USB
    echo 2. Installez les drivers ICOM si nécessaire
    echo 3. Relancez ce script
    pause
    exit /b 1
)

REM Installation VirtualHere Server
echo.
echo Installation VirtualHere Server...
call virtualhere_server_setup.bat

echo.
echo ========================================
echo CONFIGURATION PC DISTANT TERMINÉE
echo ========================================
echo.
echo Prochaines étapes:
echo 1. Notez l'adresse IP de ce PC
echo 2. Configurez le PC de supervision
echo 3. Testez la connexion
echo.
ipconfig | findstr "IPv4"
echo.
pause
goto :end

:setup_supervision
echo.
echo ========================================
echo CONFIGURATION PC SUPERVISION
echo ========================================
echo.

REM Demander l'IP du PC distant
set /p distant_ip="Adresse IP du PC distant: "

if "%distant_ip%"=="" (
    echo Adresse IP requise !
    pause
    exit /b 1
)

REM Installation VirtualHere Client
echo.
echo Installation VirtualHere Client...
call virtualhere_client_setup.bat

REM Installation dépendances Python
echo.
echo Installation des dépendances Python...
pip install fastapi uvicorn pyserial python-multipart

REM Créer script de démarrage
echo.
echo Création des scripts de démarrage...

echo @echo off > start_ic_r8600_remote.bat
echo echo ========================================== >> start_ic_r8600_remote.bat
echo echo DÉMARRAGE IC-R8600 USB REMOTE >> start_ic_r8600_remote.bat
echo echo ========================================== >> start_ic_r8600_remote.bat
echo echo. >> start_ic_r8600_remote.bat
echo echo 1. Démarrage VirtualHere Client... >> start_ic_r8600_remote.bat
echo start "" "C:\VirtualHere-Client\vhui64.exe" >> start_ic_r8600_remote.bat
echo timeout /t 5 >> start_ic_r8600_remote.bat
echo echo. >> start_ic_r8600_remote.bat
echo echo 2. Connectez-vous au serveur %distant_ip% dans VirtualHere >> start_ic_r8600_remote.bat
echo echo 3. Clic droit sur IC-R8600 ^> 'Use this device' >> start_ic_r8600_remote.bat
echo echo 4. Appuyez sur une touche quand c'est fait... >> start_ic_r8600_remote.bat
echo pause >> start_ic_r8600_remote.bat
echo echo. >> start_ic_r8600_remote.bat
echo echo 5. Démarrage serveur FastAPI... >> start_ic_r8600_remote.bat
echo cd /d "%~dp0backend" >> start_ic_r8600_remote.bat
echo python main_usb_remote.py >> start_ic_r8600_remote.bat

REM Script de test
echo @echo off > test_ic_r8600_remote.bat
echo echo ========================================== >> test_ic_r8600_remote.bat
echo echo TEST IC-R8600 USB REMOTE >> test_ic_r8600_remote.bat
echo echo ========================================== >> test_ic_r8600_remote.bat
echo echo. >> test_ic_r8600_remote.bat
echo echo 1. Test des ports COM... >> test_ic_r8600_remote.bat
echo call "C:\VirtualHere-Client\check_com_ports.bat" >> test_ic_r8600_remote.bat
echo echo. >> test_ic_r8600_remote.bat
echo echo 2. Test de communication IC-R8600... >> test_ic_r8600_remote.bat
echo cd /d "%~dp0backend" >> test_ic_r8600_remote.bat
echo python -c "from ic_r8600_usb_remote import ic_r8600; print('Test connexion...'); result = ic_r8600.connect(); print(f'Résultat: {result}'); print(f'Statut: {ic_r8600.get_status()}'); ic_r8600.disconnect()" >> test_ic_r8600_remote.bat
echo pause >> test_ic_r8600_remote.bat

REM Configuration réseau
echo.
echo Configuration réseau pour PC distant %distant_ip%...
echo # Configuration réseau IC-R8600 USB Remote > config_network.txt
echo PC Distant: %distant_ip% >> config_network.txt
echo VirtualHere Server Port: 7575 >> config_network.txt
echo FastAPI Server Port: 8000 >> config_network.txt
echo Interface Web: http://localhost:8000 >> config_network.txt

echo.
echo ========================================
echo CONFIGURATION PC SUPERVISION TERMINÉE
echo ========================================
echo.
echo Scripts créés:
echo - start_ic_r8600_remote.bat : Démarrage complet
echo - test_ic_r8600_remote.bat  : Test de connexion
echo.
echo Prochaines étapes:
echo 1. Lancez: start_ic_r8600_remote.bat
echo 2. Connectez-vous au serveur %distant_ip% dans VirtualHere
echo 3. Activez l'IC-R8600 dans VirtualHere Client
echo 4. Testez avec: test_ic_r8600_remote.bat
echo 5. Accédez à: http://localhost:8000
echo.
pause
goto :end

:end
echo.
echo ========================================
echo DÉPLOIEMENT TERMINÉ
echo ========================================
echo.
echo Documentation complète disponible dans:
echo - README_USB_Remote.md
echo.
echo Support:
echo - Logs VirtualHere: C:\VirtualHere\virtualhere.log
echo - Logs FastAPI: Console du serveur
echo.
pause
