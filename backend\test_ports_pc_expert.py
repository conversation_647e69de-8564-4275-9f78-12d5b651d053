#!/usr/bin/env python3
"""
Test Expert des Ports PC - 50001, 50002, 50003
Vérification approfondie des capacités UDP du PC
"""

import socket
import threading
import time
import subprocess
import psutil

class TestPortsExpertPC:
    """Test expert des ports UDP sur PC"""
    
    def __init__(self):
        self.test_ports = [50001, 50002, 50003]
        self.target_ip = "**************"
        self.results = {}
    
    def test_1_bind_ports_locaux(self):
        """Test 1: Capacité à binder les ports localement"""
        print("🔍 TEST 1: BIND PORTS LOCAUX")
        print("-" * 50)
        
        for port in self.test_ports:
            try:
                # Test bind sur toutes les interfaces
                sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                sock.bind(('', port))
                
                local_addr = sock.getsockname()
                print(f"✅ Port {port}: Bind réussi sur {local_addr}")
                
                sock.close()
                self.results[f'bind_{port}'] = True
                
            except Exception as e:
                print(f"❌ Port {port}: Erreur bind - {e}")
                self.results[f'bind_{port}'] = False
    
    def test_2_envoi_udp_ports(self):
        """Test 2: Envoi UDP depuis chaque port"""
        print("\n🔍 TEST 2: ENVOI UDP DEPUIS CHAQUE PORT")
        print("-" * 50)
        
        for port in self.test_ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                
                # Bind sur port spécifique
                sock.bind(('', port))
                
                # Envoyer paquet de test
                test_data = f"TEST_PORT_{port}".encode()
                sock.sendto(test_data, (self.target_ip, port))
                
                print(f"✅ Port {port}: Envoi UDP réussi depuis port local {port}")
                
                sock.close()
                self.results[f'send_{port}'] = True
                
            except Exception as e:
                print(f"❌ Port {port}: Erreur envoi - {e}")
                self.results[f'send_{port}'] = False
    
    def test_3_reception_udp_ports(self):
        """Test 3: Capacité de réception UDP"""
        print("\n🔍 TEST 3: RÉCEPTION UDP SUR CHAQUE PORT")
        print("-" * 50)
        
        def serveur_udp(port, duration=5):
            """Serveur UDP temporaire"""
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                sock.settimeout(duration)
                sock.bind(('', port))
                
                print(f"🎧 Écoute sur port {port} pendant {duration}s...")
                
                start_time = time.time()
                packets_received = 0
                
                while time.time() - start_time < duration:
                    try:
                        data, addr = sock.recvfrom(1024)
                        packets_received += 1
                        print(f"   📦 Port {port}: Paquet reçu de {addr} - {data[:20]}")
                    except socket.timeout:
                        break
                    except Exception as e:
                        print(f"   ⚠️  Port {port}: Erreur réception - {e}")
                        break
                
                sock.close()
                self.results[f'recv_{port}'] = packets_received
                print(f"✅ Port {port}: {packets_received} paquets reçus")
                
            except Exception as e:
                print(f"❌ Port {port}: Erreur serveur - {e}")
                self.results[f'recv_{port}'] = 0
        
        # Lancer serveurs en parallèle
        threads = []
        for port in self.test_ports:
            thread = threading.Thread(target=serveur_udp, args=(port, 3))
            thread.start()
            threads.append(thread)
            time.sleep(0.5)  # Décalage pour éviter conflits
        
        # Envoyer des paquets de test vers nous-mêmes
        time.sleep(1)
        for port in self.test_ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                test_data = f"AUTOTEST_{port}".encode()
                sock.sendto(test_data, ('127.0.0.1', port))
                sock.close()
            except:
                pass
        
        # Attendre fin des serveurs
        for thread in threads:
            thread.join()
    
    def test_4_netstat_verification(self):
        """Test 4: Vérification netstat"""
        print("\n🔍 TEST 4: VÉRIFICATION NETSTAT")
        print("-" * 50)
        
        try:
            result = subprocess.run(['netstat', '-an', '-p', 'UDP'], 
                                  capture_output=True, text=True)
            
            for port in self.test_ports:
                port_found = False
                for line in result.stdout.split('\n'):
                    if f':{port}' in line and 'UDP' in line:
                        print(f"✅ Port {port}: {line.strip()}")
                        port_found = True
                        break
                
                if not port_found:
                    print(f"⚠️  Port {port}: Non visible dans netstat")
                
                self.results[f'netstat_{port}'] = port_found
        
        except Exception as e:
            print(f"❌ Erreur netstat: {e}")
    
    def test_5_processus_utilisant_ports(self):
        """Test 5: Processus utilisant les ports"""
        print("\n🔍 TEST 5: PROCESSUS UTILISANT LES PORTS")
        print("-" * 50)
        
        try:
            connections = psutil.net_connections(kind='udp')
            
            for port in self.test_ports:
                processes_using_port = []
                
                for conn in connections:
                    if conn.laddr and conn.laddr.port == port:
                        try:
                            process = psutil.Process(conn.pid) if conn.pid else None
                            process_name = process.name() if process else "Inconnu"
                            processes_using_port.append((conn.pid, process_name))
                        except:
                            processes_using_port.append((conn.pid, "Inaccessible"))
                
                if processes_using_port:
                    print(f"⚠️  Port {port} utilisé par:")
                    for pid, name in processes_using_port:
                        print(f"   PID {pid}: {name}")
                    self.results[f'used_{port}'] = True
                else:
                    print(f"✅ Port {port}: Libre")
                    self.results[f'used_{port}'] = False
        
        except Exception as e:
            print(f"❌ Erreur vérification processus: {e}")
    
    def test_6_test_complet_communication(self):
        """Test 6: Test complet de communication"""
        print("\n🔍 TEST 6: TEST COMPLET COMMUNICATION")
        print("-" * 50)
        
        for port in self.test_ports:
            print(f"\n--- Test communication port {port} ---")
            
            try:
                # Créer socket avec options avancées
                sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
                sock.settimeout(3.0)
                
                # Test 1: Envoi vers IC-R8600
                ci_v_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x19, 0x00, 0xFD])
                sock.sendto(ci_v_cmd, (self.target_ip, port))
                print(f"   ✅ Envoi CI-V vers {self.target_ip}:{port}")
                
                # Test 2: Tentative de réception
                try:
                    response, addr = sock.recvfrom(1024)
                    print(f"   ✅ Réponse reçue: {response.hex().upper()}")
                    self.results[f'comm_{port}'] = True
                except socket.timeout:
                    print(f"   ⚠️  Pas de réponse (timeout)")
                    self.results[f'comm_{port}'] = False
                
                # Test 3: Informations socket
                local_addr = sock.getsockname()
                print(f"   📍 Adresse locale: {local_addr}")
                
                sock.close()
                
            except Exception as e:
                print(f"   ❌ Erreur communication: {e}")
                self.results[f'comm_{port}'] = False
    
    def generer_rapport_ports(self):
        """Générer rapport détaillé des ports"""
        print("\n" + "=" * 60)
        print("📊 RAPPORT EXPERT PORTS PC")
        print("=" * 60)
        
        for port in self.test_ports:
            print(f"\n🔌 PORT {port}:")
            
            tests_port = [
                (f'bind_{port}', 'Bind local'),
                (f'send_{port}', 'Envoi UDP'),
                (f'recv_{port}', 'Réception UDP'),
                (f'netstat_{port}', 'Visible netstat'),
                (f'used_{port}', 'Utilisé par processus'),
                (f'comm_{port}', 'Communication IC-R8600'),
            ]
            
            port_ok = True
            for test_key, test_name in tests_port:
                if test_key in self.results:
                    result = self.results[test_key]
                    
                    if test_key.startswith('recv_'):
                        status = "✅" if result > 0 else "⚠️ "
                        print(f"   {status} {test_name}: {result} paquets")
                    elif test_key.startswith('used_'):
                        status = "⚠️ " if result else "✅"
                        print(f"   {status} {test_name}: {'Oui' if result else 'Non'}")
                        if result:
                            port_ok = False
                    else:
                        status = "✅" if result else "❌"
                        print(f"   {status} {test_name}")
                        if not result and test_key != f'comm_{port}':
                            port_ok = False
            
            print(f"   🎯 Port {port}: {'OPÉRATIONNEL' if port_ok else 'PROBLÈME DÉTECTÉ'}")
        
        # Résumé global
        print(f"\n{'='*60}")
        print("🎯 RÉSUMÉ GLOBAL")
        print(f"{'='*60}")
        
        ports_ok = 0
        for port in self.test_ports:
            bind_ok = self.results.get(f'bind_{port}', False)
            send_ok = self.results.get(f'send_{port}', False)
            not_used = not self.results.get(f'used_{port}', True)
            
            if bind_ok and send_ok and not_used:
                ports_ok += 1
        
        if ports_ok == len(self.test_ports):
            print("✅ TOUS LES PORTS PC SONT OPÉRATIONNELS")
            print("Le problème est 100% côté IC-R8600")
        else:
            print(f"⚠️  {ports_ok}/{len(self.test_ports)} ports opérationnels")
            print("Vérifications supplémentaires nécessaires")

def main():
    """Test expert complet des ports PC"""
    print("🔧 TEST EXPERT PORTS PC - 50001, 50002, 50003")
    print("=" * 60)
    print("Vérification approfondie des capacités UDP")
    print("=" * 60)
    
    tester = TestPortsExpertPC()
    
    # Exécution des tests
    tests = [
        tester.test_1_bind_ports_locaux,
        tester.test_2_envoi_udp_ports,
        tester.test_3_reception_udp_ports,
        tester.test_4_netstat_verification,
        tester.test_5_processus_utilisant_ports,
        tester.test_6_test_complet_communication,
    ]
    
    for test in tests:
        try:
            test()
            time.sleep(1)
        except Exception as e:
            print(f"❌ Erreur test: {e}")
    
    # Rapport final
    tester.generer_rapport_ports()

if __name__ == "__main__":
    main()
