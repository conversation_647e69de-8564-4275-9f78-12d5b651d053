#!/usr/bin/env python3
"""
Serveur de test pour vérifier les nouvelles fonctionnalités
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

app = FastAPI(title="IC-R8600 Test Server")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Models
class CommandRequest(BaseModel):
    frequency: float = None
    mode: str = None
    power: bool = None

class ScanRequest(BaseModel):
    start_frequency: int
    end_frequency: int
    step: int = 25000

class CommandResponse(BaseModel):
    success: bool
    message: str = ""
    simulation_mode: bool = True

# État simulé
status = {
    "connected": True,
    "frequency": 145500000,
    "mode": "FM",
    "power_on": True
}

@app.get("/api/status")
async def get_status():
    return status

@app.post("/api/command")
async def send_command(command: CommandRequest):
    """Commande simulée"""
    if command.frequency:
        status["frequency"] = int(command.frequency * 1000000)
        return {"success": True, "frequency_mhz": command.frequency, "frequency_hz": status["frequency"]}
    
    if command.mode:
        status["mode"] = command.mode
        return {"success": True, "mode": command.mode}
    
    if command.power is not None:
        status["power_on"] = command.power
        return {"success": True, "power": command.power}
    
    return {"success": False, "message": "Aucune commande valide"}

@app.post("/api/scan/start")
async def start_scan(scan_request: ScanRequest):
    """Test du scan"""
    if scan_request.start_frequency >= scan_request.end_frequency:
        raise HTTPException(status_code=400, detail="Fréquence de début doit être inférieure à la fin")
    
    return CommandResponse(
        success=True,
        message=f"Scan démarré: {scan_request.start_frequency/1000000:.3f} - {scan_request.end_frequency/1000000:.3f} MHz",
        simulation_mode=True
    )

@app.post("/api/scan/stop")
async def stop_scan():
    """Test arrêt scan"""
    return CommandResponse(
        success=True,
        message="Scan arrêté (test)",
        simulation_mode=True
    )

if __name__ == "__main__":
    print("🚀 Serveur de test IC-R8600")
    print("📡 http://localhost:8001")
    uvicorn.run(app, host="0.0.0.0", port=8001)
