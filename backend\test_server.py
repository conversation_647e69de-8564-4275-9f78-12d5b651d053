#!/usr/bin/env python3
"""
Serveur de test pour vérifier les nouvelles fonctionnalités
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

app = FastAPI(title="IC-R8600 Test Server")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Models
class CommandRequest(BaseModel):
    frequency: float = None
    mode: str = None
    power: bool = None
    rf_gain: int = None

class ScanRequest(BaseModel):
    start_frequency: int
    end_frequency: int
    step: int = 25000

class CommandResponse(BaseModel):
    success: bool
    message: str = ""
    simulation_mode: bool = True

# État simulé
status = {
    "connected": True,
    "frequency": 145500000,
    "mode": "FM",
    "power_on": True,
    "volume": 128,
    "squelch": 50,
    "rf_gain": 128
}

@app.get("/api/status")
async def get_status():
    return status

@app.post("/api/command")
async def send_command(command: CommandRequest):
    """Commande simulée"""
    results = []

    if command.frequency:
        status["frequency"] = int(command.frequency * 1000000)
        results.append(f"Fréquence: {command.frequency} MHz")

    if command.mode:
        status["mode"] = command.mode
        results.append(f"Mode: {command.mode}")

    if command.power is not None:
        status["power_on"] = command.power
        results.append(f"Alimentation: {'ON' if command.power else 'OFF'}")

    if command.rf_gain is not None:
        status["rf_gain"] = command.rf_gain
        results.append(f"RF Gain: {command.rf_gain}")

    if results:
        return {"success": True, "message": " | ".join(results)}

    return {"success": False, "message": "Aucune commande valide"}

@app.post("/api/scan/start")
async def start_scan(scan_request: ScanRequest):
    """Test du scan"""
    if scan_request.start_frequency >= scan_request.end_frequency:
        raise HTTPException(status_code=400, detail="Fréquence de début doit être inférieure à la fin")
    
    return CommandResponse(
        success=True,
        message=f"Scan démarré: {scan_request.start_frequency/1000000:.3f} - {scan_request.end_frequency/1000000:.3f} MHz",
        simulation_mode=True
    )

@app.post("/api/scan/stop")
async def stop_scan():
    """Test arrêt scan"""
    return CommandResponse(
        success=True,
        message="Scan arrêté (test)",
        simulation_mode=True
    )

@app.get("/api/recordings")
async def get_recordings():
    """Liste des enregistrements (simulé)"""
    return {
        "recordings": [
            {
                "filename": "test_recording_1.wav",
                "date": "2025-07-17 14:30:00",
                "duration": "00:02:15",
                "frequency": "145.500 MHz",
                "mode": "FM"
            },
            {
                "filename": "test_recording_2.wav",
                "date": "2025-07-17 13:15:00",
                "duration": "00:01:45",
                "frequency": "88.500 MHz",
                "mode": "FM"
            }
        ]
    }

@app.post("/api/audio/start")
async def start_recording():
    """Démarrage enregistrement (simulé)"""
    return CommandResponse(
        success=True,
        message="Enregistrement démarré (test)",
        simulation_mode=True
    )

@app.post("/api/audio/stop")
async def stop_recording():
    """Arrêt enregistrement (simulé)"""
    return CommandResponse(
        success=True,
        message="Enregistrement arrêté (test)",
        simulation_mode=True
    )

if __name__ == "__main__":
    print("🚀 Serveur de test IC-R8600")
    print("📡 http://localhost:8001")
    uvicorn.run(app, host="0.0.0.0", port=8001)
