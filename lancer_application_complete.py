#!/usr/bin/env python3
"""
Script de lancement complet de l'application IC-R8600
Lance automatiquement le backend et le frontend
"""

import subprocess
import time
import sys
import os
import webbrowser
from pathlib import Path

def print_banner():
    """Affiche la bannière de l'application"""
    print("=" * 70)
    print("APPLICATION IC-R8600 - LANCEMENT AUTOMATIQUE")
    print("=" * 70)
    print("Mode: TCP Telnet (port 23)")
    print("Backend: http://localhost:8000")
    print("Frontend: http://localhost:5173")
    print("=" * 70)

def check_ic_r8600_connection():
    """Vérifie la connexion avec l'IC-R8600"""
    print("1. Verification de la connexion IC-R8600...")
    
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3.0)
        result = sock.connect_ex(("**************", 23))
        sock.close()
        
        if result == 0:
            print("   [OK] IC-R8600 accessible sur **************:23")
            return True
        else:
            print("   [ERREUR] IC-R8600 non accessible")
            return False
    except Exception as e:
        print(f"   [ERREUR] Test connexion: {e}")
        return False

def launch_backend():
    """Lance le backend"""
    print("\n2. Lancement du backend...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("   [ERREUR] Dossier backend non trouve")
        return None
    
    try:
        # Lancer le backend en arrière-plan
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            cwd=backend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print("   [OK] Backend en cours de demarrage...")
        
        # Attendre que le backend soit prêt
        for i in range(10):
            try:
                import requests
                response = requests.get("http://localhost:8000/api/status", timeout=1)
                if response.status_code == 200:
                    print("   [OK] Backend pret et fonctionnel")
                    return process
            except:
                pass
            
            print(f"   Attente backend... ({i+1}/10)")
            time.sleep(2)
        
        print("   [ATTENTION] Backend demarre mais statut incertain")
        return process
        
    except Exception as e:
        print(f"   [ERREUR] Lancement backend: {e}")
        return None

def launch_frontend():
    """Lance le frontend"""
    print("\n3. Lancement du frontend...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("   [ERREUR] Dossier frontend non trouve")
        return None
    
    try:
        # Vérifier si npm est disponible
        subprocess.run(["npm", "--version"], capture_output=True, check=True)
        
        # Lancer le frontend
        process = subprocess.Popen(
            ["npm", "run", "dev"],
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print("   [OK] Frontend en cours de demarrage...")
        
        # Attendre que le frontend soit prêt
        for i in range(15):
            try:
                import requests
                response = requests.get("http://localhost:5173", timeout=1)
                if response.status_code == 200:
                    print("   [OK] Frontend pret et accessible")
                    return process
            except:
                pass
            
            print(f"   Attente frontend... ({i+1}/15)")
            time.sleep(2)
        
        print("   [ATTENTION] Frontend demarre mais statut incertain")
        return process
        
    except subprocess.CalledProcessError:
        print("   [ERREUR] npm non trouve - installez Node.js")
        return None
    except Exception as e:
        print(f"   [ERREUR] Lancement frontend: {e}")
        return None

def open_browser():
    """Ouvre le navigateur"""
    print("\n4. Ouverture du navigateur...")
    
    try:
        webbrowser.open("http://localhost:5173")
        print("   [OK] Navigateur ouvert sur http://localhost:5173")
    except Exception as e:
        print(f"   [ERREUR] Ouverture navigateur: {e}")
        print("   Ouvrez manuellement: http://localhost:5173")

def show_status():
    """Affiche le statut final"""
    print("\n" + "=" * 70)
    print("APPLICATION IC-R8600 LANCEE")
    print("=" * 70)
    print("Frontend:     http://localhost:5173")
    print("Backend API:  http://localhost:8000")
    print("Documentation: http://localhost:8000/docs")
    print("=" * 70)
    print("Appuyez sur Ctrl+C pour arreter l'application")
    print("=" * 70)

def main():
    """Fonction principale"""
    print_banner()
    
    # Étape 1: Vérifier la connexion IC-R8600
    if not check_ic_r8600_connection():
        print("\n[ERREUR] IC-R8600 non accessible")
        print("Verifiez:")
        print("- IC-R8600 allume")
        print("- Cable RJ45 connecte")
        print("- IP ************** configuree")
        return 1
    
    # Étape 2: Lancer le backend
    backend_process = launch_backend()
    if not backend_process:
        print("\n[ERREUR] Impossible de lancer le backend")
        return 1
    
    # Étape 3: Lancer le frontend
    frontend_process = launch_frontend()
    if not frontend_process:
        print("\n[ERREUR] Impossible de lancer le frontend")
        print("Le backend reste accessible sur http://localhost:8000")
        backend_process.terminate()
        return 1
    
    # Étape 4: Ouvrir le navigateur
    open_browser()
    
    # Étape 5: Afficher le statut
    show_status()
    
    # Boucle principale
    try:
        while True:
            # Vérifier que les processus sont toujours actifs
            if backend_process.poll() is not None:
                print("\n[ERREUR] Backend arrete inopinement")
                break
            
            if frontend_process.poll() is not None:
                print("\n[ERREUR] Frontend arrete inopinement")
                break
            
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n\nArret de l'application...")
        
        # Arrêter les processus
        if frontend_process:
            frontend_process.terminate()
            print("Frontend arrete")
        
        if backend_process:
            backend_process.terminate()
            print("Backend arrete")
        
        print("Application fermee")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"\n[ERREUR FATALE] {e}")
        sys.exit(1)
