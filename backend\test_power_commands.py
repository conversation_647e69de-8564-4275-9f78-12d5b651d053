#!/usr/bin/env python3
"""
Test des commandes power via l'API
"""

import requests
import time
import json

def test_power_commands():
    """Test des commandes power via l'API"""
    base_url = "http://localhost:8000"
    
    print("Test des commandes Power via API")
    print("=" * 40)
    
    # Test 1: Statut initial
    print("1. Lecture du statut initial...")
    try:
        response = requests.get(f"{base_url}/api/status")
        if response.status_code == 200:
            status = response.json()
            print(f"   Connecte: {status.get('connected', 'Unknown')}")
            print(f"   Frequence: {status.get('frequency', 'Unknown')}")
            print(f"   Mode: {status.get('mode', 'Unknown')}")
        else:
            print(f"   Erreur statut: {response.status_code}")
    except Exception as e:
        print(f"   Erreur: {e}")
    
    # Test 2: Commande Power OFF
    print("\n2. Test Power OFF...")
    try:
        command = {
            "power_on": False
        }
        response = requests.post(f"{base_url}/api/command", json=command)
        if response.status_code == 200:
            result = response.json()
            print(f"   Reponse: {result}")
        else:
            print(f"   Erreur: {response.status_code}")
    except Exception as e:
        print(f"   Erreur: {e}")
    
    time.sleep(2)
    
    # Test 3: Commande Power ON
    print("\n3. Test Power ON...")
    try:
        command = {
            "power_on": True
        }
        response = requests.post(f"{base_url}/api/command", json=command)
        if response.status_code == 200:
            result = response.json()
            print(f"   Reponse: {result}")
        else:
            print(f"   Erreur: {response.status_code}")
    except Exception as e:
        print(f"   Erreur: {e}")
    
    time.sleep(2)
    
    # Test 4: Statut final
    print("\n4. Lecture du statut final...")
    try:
        response = requests.get(f"{base_url}/api/status")
        if response.status_code == 200:
            status = response.json()
            print(f"   Connecte: {status.get('connected', 'Unknown')}")
            print(f"   Frequence: {status.get('frequency', 'Unknown')}")
            print(f"   Mode: {status.get('mode', 'Unknown')}")
        else:
            print(f"   Erreur statut: {response.status_code}")
    except Exception as e:
        print(f"   Erreur: {e}")

if __name__ == "__main__":
    test_power_commands()
