#!/usr/bin/env python3
"""
Test des vraies commandes IC-R8600 avec adresse DFh
"""

import requests
import time

BASE_URL = "http://localhost:8001"

def test_frequency():
    """Test changement de fréquence"""
    print("🔍 TEST FRÉQUENCE 88.5 MHz")
    print("=" * 40)
    print("⚠️  OBSERVEZ L'AFFICHAGE IC-R8600 !")
    
    try:
        data = {"frequency_mhz": 88.5}
        response = requests.post(f"{BASE_URL}/api/set_frequency", json=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Succès: {result['success']}")
            print(f"✅ Fréquence: {result['frequency_mhz']} MHz")
            
            input("La fréquence 88.5 MHz s'affiche-t-elle ? (Appuyez sur Entrée)")
            return True
        else:
            print(f"❌ Erreur: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_mode():
    """Test changement de mode"""
    print("\n🔍 TEST MODE AM")
    print("=" * 40)
    print("⚠️  OBSERVEZ L'AFFICHAGE IC-R8600 !")
    
    try:
        data = {"mode": "AM"}
        response = requests.post(f"{BASE_URL}/api/set_mode", json=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Succès: {result['success']}")
            print(f"✅ Mode: {result['mode']}")
            
            input("Le mode AM s'affiche-t-il ? (Appuyez sur Entrée)")
            return True
        else:
            print(f"❌ Erreur: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_power():
    """Test Power ON/OFF"""
    print("\n🔍 TEST POWER ON/OFF")
    print("=" * 40)
    print("⚠️  OBSERVEZ L'IC-R8600 !")
    
    try:
        # Power OFF
        print("\n1. Power OFF...")
        response = requests.post(f"{BASE_URL}/api/power_off")
        if response.status_code == 200:
            print("✅ Commande Power OFF envoyée")
            input("L'IC-R8600 s'éteint-il ? (Appuyez sur Entrée)")
        
        time.sleep(2)
        
        # Power ON
        print("\n2. Power ON...")
        response = requests.post(f"{BASE_URL}/api/power_on")
        if response.status_code == 200:
            print("✅ Commande Power ON envoyée")
            input("L'IC-R8600 se rallume-t-il ? (Appuyez sur Entrée)")
        
        return True
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Test complet"""
    print("🎯 TEST COMMANDES RÉELLES IC-R8600")
    print("=" * 50)
    print("Adresse CI-V: DFh (Remote)")
    print("Port: COM7 (VirtualHere)")
    print("=" * 50)
    
    # Test 1: Fréquence
    test_frequency()
    
    # Test 2: Mode
    test_mode()
    
    # Test 3: Power
    test_power()
    
    print("\n" + "=" * 50)
    print("🎉 TESTS TERMINÉS")
    print("=" * 50)
    print("Si l'IC-R8600 a réagi, les commandes fonctionnent !")

if __name__ == "__main__":
    main()
