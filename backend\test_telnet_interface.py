#!/usr/bin/env python3
"""
Test de l'interface Telnet IC-R8600 pour découvrir les commandes disponibles
Exploration systématique des commandes possibles
"""

import socket
import time
import sys

def send_telnet_command(sock, command, wait_time=0.5):
    """Envoie une commande Telnet et lit la réponse"""
    try:
        sock.send(f"{command}\r\n".encode())
        time.sleep(wait_time)
        
        response = sock.recv(4096)
        return response.decode('ascii', errors='ignore').strip()
    except Exception as e:
        return f"Erreur: {e}"

def explore_telnet_interface():
    """Explore l'interface Telnet de l'IC-R8600"""
    host = "**************"
    port = 23
    
    print(f"🔌 Connexion Telnet vers {host}:{port}")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10.0)
        sock.connect((host, port))
        
        # Lire le message de bienvenue
        welcome = sock.recv(1024)
        print(f"📥 Bienvenue: {welcome.decode('ascii', errors='ignore').strip()}")
        
        # Commandes à tester basées sur les protocoles ICOM standards
        test_commands = [
            # Commandes d'aide et d'information
            ("help", "Aide générale"),
            ("?", "Aide courte"),
            ("info", "Informations système"),
            ("version", "Version firmware"),
            ("status", "Statut général"),
            
            # Commandes de fréquence (format ICOM standard)
            ("*F", "Lecture fréquence (format ICOM)"),
            ("*F?", "Query fréquence"),
            ("F", "Fréquence courte"),
            ("FREQ", "Fréquence longue"),
            ("RF", "Radio fréquence"),
            
            # Commandes de mode
            ("*M", "Lecture mode (format ICOM)"),
            ("*M?", "Query mode"),
            ("M", "Mode court"),
            ("MODE", "Mode long"),
            
            # Commandes de volume
            ("*V", "Volume (format ICOM)"),
            ("*V?", "Query volume"),
            ("VOL", "Volume"),
            ("AF", "Audio fréquence"),
            
            # Commandes de contrôle
            ("*PWR", "Power control"),
            ("*PWR?", "Query power"),
            ("ON", "Power ON"),
            ("OFF", "Power OFF"),
            
            # Commandes CI-V en format texte
            ("CIV", "CI-V interface"),
            ("REMOTE", "Remote control"),
            ("LOCAL", "Local control"),
            
            # Commandes de scan
            ("SCAN", "Scan control"),
            ("*SCAN", "Scan ICOM"),
            ("*SCAN?", "Query scan"),
            
            # Commandes de mémoire
            ("MEM", "Memory"),
            ("*MEM", "Memory ICOM"),
            ("*MEM?", "Query memory"),
            
            # Commandes réseau
            ("NET", "Network"),
            ("NETWORK", "Network status"),
            ("IP", "IP address"),
            
            # Commandes système
            ("SYS", "System"),
            ("SYSTEM", "System info"),
            ("CONFIG", "Configuration"),
            ("SETTINGS", "Settings"),
            
            # Format de commandes avec paramètres
            ("*F 145500000", "Set fréquence 145.5 MHz"),
            ("*M FM", "Set mode FM"),
            ("*V 50", "Set volume 50%"),
        ]
        
        print(f"\n📤 Test de {len(test_commands)} commandes...")
        
        successful_commands = []
        
        for i, (cmd, desc) in enumerate(test_commands):
            print(f"\n[{i+1:2d}/{len(test_commands)}] {desc}")
            print(f"    Commande: {cmd}")
            
            response = send_telnet_command(sock, cmd)
            
            # Analyser la réponse
            if response and len(response) > 5:
                # Filtrer les réponses utiles
                if ("command not found" not in response.lower() and 
                    "IC-R8600 #" not in response and
                    "error" not in response.lower()):
                    
                    print(f"    ✅ Réponse: {response[:100]}...")
                    successful_commands.append((cmd, desc, response))
                else:
                    print(f"    ❌ {response}")
            else:
                print(f"    ❌ Pas de réponse ou réponse vide")
        
        print(f"\n📊 Commandes réussies: {len(successful_commands)}")
        
        if successful_commands:
            print("\n✅ Commandes fonctionnelles trouvées:")
            for cmd, desc, resp in successful_commands:
                print(f"  - {cmd:15} : {desc}")
                print(f"    Réponse: {resp[:80]}...")
                print()
        
        # Test de commandes interactives
        print("\n🔄 Test de commandes interactives...")
        
        interactive_commands = [
            "exit",
            "quit", 
            "logout",
            "disconnect",
            "close"
        ]
        
        for cmd in interactive_commands:
            print(f"Test: {cmd}")
            response = send_telnet_command(sock, cmd, 0.2)
            if response and "command not found" not in response.lower():
                print(f"  ✅ {response}")
            else:
                print(f"  ❌ Non reconnu")
        
        sock.close()
        return len(successful_commands) > 0
        
    except Exception as e:
        print(f"❌ Erreur connexion Telnet: {e}")
        return False

def test_raw_ci_v_over_telnet():
    """Test d'envoi de commandes CI-V brutes via Telnet"""
    host = "**************"
    port = 23
    
    print(f"\n🧪 Test CI-V brut via Telnet...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        sock.connect((host, port))
        
        # Lire le prompt
        sock.recv(1024)
        
        # Envoyer des données binaires CI-V directement
        print("📤 Envoi de commande CI-V binaire...")
        
        # Commande CI-V : FE FE 94 E0 03 FD (lecture fréquence)
        ci_v_command = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
        
        sock.send(ci_v_command)
        time.sleep(1)
        
        response = sock.recv(1024)
        
        if response and len(response) > 0:
            print(f"✅ Réponse binaire: {response.hex().upper()}")
            
            # Vérifier si c'est une réponse CI-V valide
            if (len(response) >= 6 and 
                response[0:2] == bytes([0xFE, 0xFE]) and 
                response[-1] == 0xFD):
                print("🎯 Réponse CI-V valide détectée !")
                return True
            else:
                print("⚠️ Réponse non-CI-V")
        else:
            print("❌ Pas de réponse")
        
        sock.close()
        
    except Exception as e:
        print(f"❌ Erreur CI-V binaire: {e}")
    
    return False

if __name__ == "__main__":
    print("=" * 70)
    print("🧪 EXPLORATION INTERFACE TELNET IC-R8600")
    print("=" * 70)
    
    # Test 1: Exploration des commandes Telnet
    commands_found = explore_telnet_interface()
    
    # Test 2: CI-V binaire via Telnet
    civ_works = test_raw_ci_v_over_telnet()
    
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ DE L'EXPLORATION")
    print("=" * 70)
    
    if commands_found:
        print("✅ Interface Telnet avec commandes spécifiques trouvée")
        print("🎯 Possibilité de contrôle via commandes texte")
    
    if civ_works:
        print("✅ Interface CI-V binaire via Telnet fonctionnelle")
        print("🎯 Possibilité de contrôle CI-V direct")
    
    if not commands_found and not civ_works:
        print("⚠️ Interface Telnet basique uniquement")
        print("🔧 Exploration d'autres méthodes nécessaire")
    
    print("\n💡 Prochaines étapes recommandées:")
    if commands_found or civ_works:
        print("   - Adapter l'application pour utiliser Telnet")
        print("   - Implémenter les commandes trouvées")
        print("   - Tester le contrôle complet via Telnet")
    else:
        print("   - Configurer le firewall Windows")
        print("   - Redémarrer l'IC-R8600")
        print("   - Vérifier la configuration Network Control")
    
    print("=" * 70)
