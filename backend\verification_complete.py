#!/usr/bin/env python3
"""
Vérification complète et systématique de l'IC-R8600
Diagnostic approfondi de tous les aspects réseau et configuration
"""

import socket
import subprocess
import time
import struct
import threading
from datetime import datetime

class ICOMVerification:
    """Vérification complète IC-R8600"""
    
    def __init__(self, host="**************"):
        self.host = host
        self.results = {}
        
    def run_complete_verification(self):
        """Lance la vérification complète"""
        print("🔍 VÉRIFICATION COMPLÈTE IC-R8600")
        print("=" * 60)
        print(f"Cible: {self.host}")
        print(f"Heure: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # Tests séquentiels
        self.test_1_network_connectivity()
        self.test_2_port_scanning()
        self.test_3_telnet_analysis()
        self.test_4_udp_discovery()
        self.test_5_ci_v_injection()
        self.test_6_protocol_analysis()
        self.test_7_configuration_check()
        
        # Rapport final
        self.generate_final_report()

    def test_1_network_connectivity(self):
        """Test 1: Connectivité réseau de base"""
        print("\n📡 TEST 1: CONNECTIVITÉ RÉSEAU")
        print("-" * 40)
        
        try:
            # Ping test
            result = subprocess.run(['ping', '-n', '4', self.host], 
                                  capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                # Extraire le temps de réponse
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'temps' in line and 'ms' in line:
                        print(f"✅ Ping réussi: {line.strip()}")
                        self.results['ping'] = 'SUCCESS'
                        break
            else:
                print("❌ Ping échoué")
                self.results['ping'] = 'FAILED'
                
        except Exception as e:
            print(f"❌ Erreur ping: {e}")
            self.results['ping'] = 'ERROR'

    def test_2_port_scanning(self):
        """Test 2: Scan des ports"""
        print("\n🔍 TEST 2: SCAN DES PORTS")
        print("-" * 40)
        
        ports_to_test = [23, 80, 443, 8080, 50001, 50002, 50003, 50000, 50004, 50005]
        open_ports = []
        
        for port in ports_to_test:
            try:
                # Test TCP
                sock_tcp = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock_tcp.settimeout(2.0)
                result_tcp = sock_tcp.connect_ex((self.host, port))
                sock_tcp.close()
                
                # Test UDP (envoi de données)
                sock_udp = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                sock_udp.settimeout(1.0)
                sock_udp.sendto(b'\x00\x01\x02\x03', (self.host, port))
                
                try:
                    response, addr = sock_udp.recvfrom(1024)
                    print(f"✅ Port {port} UDP: OUVERT (réponse reçue)")
                    open_ports.append(f"{port}/UDP")
                except socket.timeout:
                    if result_tcp == 0:
                        print(f"✅ Port {port} TCP: OUVERT")
                        open_ports.append(f"{port}/TCP")
                    else:
                        print(f"❌ Port {port}: FERMÉ")
                
                sock_udp.close()
                
            except Exception as e:
                print(f"❌ Port {port}: ERREUR ({e})")
        
        self.results['open_ports'] = open_ports
        print(f"\nPorts ouverts détectés: {open_ports}")

    def test_3_telnet_analysis(self):
        """Test 3: Analyse Telnet approfondie"""
        print("\n📞 TEST 3: ANALYSE TELNET")
        print("-" * 40)
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5.0)
            sock.connect((self.host, 23))
            
            print("✅ Connexion Telnet établie")
            
            # Lire le message de bienvenue
            try:
                welcome = sock.recv(1024)
                if welcome:
                    print(f"Message de bienvenue: {welcome.hex().upper()}")
                    print(f"ASCII: {welcome.decode('ascii', errors='ignore')}")
                    self.results['telnet_welcome'] = welcome.hex().upper()
                else:
                    print("Pas de message de bienvenue")
                    self.results['telnet_welcome'] = 'NONE'
            except socket.timeout:
                print("Timeout sur message de bienvenue")
                self.results['telnet_welcome'] = 'TIMEOUT'
            
            # Test de commandes diverses
            test_commands = [
                b'\r\n',
                b'?\r\n',
                b'help\r\n',
                b'status\r\n',
                b'info\r\n',
                b'version\r\n',
                b'*IDN?\r\n',
                b'SYST:ERR?\r\n',
                b'\xFE\xFE\x94\xE0\x19\x00\xFD',  # CI-V status
            ]
            
            responses = []
            for i, cmd in enumerate(test_commands):
                try:
                    print(f"Test commande {i+1}: {cmd}")
                    sock.sendall(cmd)
                    time.sleep(0.5)
                    
                    try:
                        response = sock.recv(1024)
                        if response:
                            print(f"  Réponse: {response.hex().upper()}")
                            print(f"  ASCII: {response.decode('ascii', errors='ignore')}")
                            responses.append(response.hex().upper())
                        else:
                            print("  Pas de réponse")
                            responses.append('NO_RESPONSE')
                    except socket.timeout:
                        print("  Timeout")
                        responses.append('TIMEOUT')
                        
                except Exception as e:
                    print(f"  Erreur: {e}")
                    responses.append('ERROR')
            
            self.results['telnet_responses'] = responses
            sock.close()
            
        except Exception as e:
            print(f"❌ Erreur Telnet: {e}")
            self.results['telnet_status'] = 'FAILED'

    def test_4_udp_discovery(self):
        """Test 4: Découverte UDP avancée"""
        print("\n📡 TEST 4: DÉCOUVERTE UDP AVANCÉE")
        print("-" * 40)
        
        ports = [50001, 50002, 50003]
        discovery_packets = [
            (b'\x00\x00\x00\x00', "Magic NULL"),
            (b'\x70\x00\x00\x00', "ICOM Magic"),
            (b'\xFE\xFE\x94\xE0\x19\x00\xFD', "CI-V Status"),
            (b'\xFE\xFE\x00\xE0\x19\x00\xFD', "CI-V Broadcast"),
            (b'HELLO', "ASCII Hello"),
            (b'\x01\x02\x03\x04', "Test Pattern"),
        ]
        
        responses_found = []
        
        for port in ports:
            print(f"\nTest port UDP {port}:")
            
            for packet, description in discovery_packets:
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                    sock.settimeout(2.0)
                    
                    print(f"  Envoi {description}: {packet.hex().upper()}")
                    sock.sendto(packet, (self.host, port))
                    
                    try:
                        response, addr = sock.recvfrom(1024)
                        print(f"  ✅ RÉPONSE: {response.hex().upper()}")
                        responses_found.append({
                            'port': port,
                            'packet': description,
                            'response': response.hex().upper()
                        })
                    except socket.timeout:
                        print(f"  ❌ Pas de réponse")
                    
                    sock.close()
                    
                except Exception as e:
                    print(f"  ❌ Erreur: {e}")
        
        self.results['udp_responses'] = responses_found

    def test_5_ci_v_injection(self):
        """Test 5: Injection CI-V directe"""
        print("\n⚡ TEST 5: INJECTION CI-V DIRECTE")
        print("-" * 40)
        
        ci_v_commands = [
            ([0x19, 0x00], "Status Query"),
            ([0x03], "Read Frequency"),
            ([0x04], "Read Mode"),
            ([0x18, 0x00], "Power OFF"),
            ([0x18, 0x01], "Power ON"),
        ]
        
        injection_results = []
        
        # Test via différents canaux
        channels = [
            ('UDP', 50001, 'control'),
            ('UDP', 50002, 'serial'),
            ('TCP', 23, 'telnet'),
        ]
        
        for protocol, port, channel in channels:
            print(f"\nTest injection via {protocol}:{port} ({channel}):")
            
            for command, description in ci_v_commands:
                try:
                    ci_v_packet = bytes([0xFE, 0xFE, 0x94, 0xE0] + command + [0xFD])
                    print(f"  {description}: {ci_v_packet.hex().upper()}")
                    
                    if protocol == 'UDP':
                        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                        sock.settimeout(3.0)
                        sock.sendto(ci_v_packet, (self.host, port))
                        
                        try:
                            response, addr = sock.recvfrom(1024)
                            print(f"    ✅ Réponse: {response.hex().upper()}")
                            injection_results.append({
                                'channel': f"{protocol}:{port}",
                                'command': description,
                                'response': response.hex().upper()
                            })
                        except socket.timeout:
                            print(f"    ❌ Pas de réponse")
                        
                        sock.close()
                        
                    elif protocol == 'TCP':
                        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        sock.settimeout(3.0)
                        sock.connect((self.host, port))
                        sock.sendall(ci_v_packet)
                        
                        try:
                            response = sock.recv(1024)
                            if response:
                                print(f"    ✅ Réponse: {response.hex().upper()}")
                                injection_results.append({
                                    'channel': f"{protocol}:{port}",
                                    'command': description,
                                    'response': response.hex().upper()
                                })
                            else:
                                print(f"    ❌ Pas de réponse")
                        except socket.timeout:
                            print(f"    ❌ Timeout")
                        
                        sock.close()
                    
                    time.sleep(1)  # Délai entre commandes
                    
                except Exception as e:
                    print(f"    ❌ Erreur: {e}")
        
        self.results['ci_v_injection'] = injection_results

    def test_6_protocol_analysis(self):
        """Test 6: Analyse de protocole"""
        print("\n🔬 TEST 6: ANALYSE DE PROTOCOLE")
        print("-" * 40)
        
        # Test de patterns de protocoles connus
        protocol_tests = [
            # Format Hamlib
            (b'\x00\x00\x00\x00\x00\x80\x00\x01\x00\x00\x00\x04' + struct.pack('>I', int(time.time())), "Hamlib Login"),
            
            # Format RS-BA1
            (b'\x70\x00\x00\x00\x01\x01\x00\x08\x01\x23\x45\x67\x00\x01\x00\x00', "RS-BA1 Init"),
            
            # Format ICOM Network
            (b'\x00\x00\x00\x00\x01\x00\x00\x00\x00\x00\x00\x08ICOM', "ICOM Network"),
        ]
        
        for packet, description in protocol_tests:
            print(f"\nTest {description}:")
            print(f"  Paquet: {packet.hex().upper()}")
            
            # Test sur port 50001
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                sock.settimeout(3.0)
                sock.sendto(packet, (self.host, 50001))
                
                try:
                    response, addr = sock.recvfrom(1024)
                    print(f"  ✅ Réponse: {response.hex().upper()}")
                except socket.timeout:
                    print(f"  ❌ Pas de réponse")
                
                sock.close()
                
            except Exception as e:
                print(f"  ❌ Erreur: {e}")

    def test_7_configuration_check(self):
        """Test 7: Vérification de configuration"""
        print("\n⚙️ TEST 7: VÉRIFICATION CONFIGURATION")
        print("-" * 40)
        
        print("VÉRIFIEZ MANUELLEMENT SUR L'IC-R8600:")
        print()
        print("1. MENU → SET → Network → Network Control")
        print("   État actuel: [À vérifier manuellement]")
        print()
        print("2. MENU → SET → Network → IP Address")
        print("   Doit être: **************")
        print()
        print("3. MENU → SET → Network → Control Port")
        print("   Doit être: 50001")
        print()
        print("4. MENU → SET → Network → Serial Port")
        print("   Doit être: 50002")
        print()
        print("5. Bouton [LOCAL] sur la face avant")
        print("   Appuyez pour basculer en mode REMOTE")
        print()
        print("6. Affichage de l'IC-R8600")
        print("   Doit montrer un indicateur REMOTE ou réseau")
        
        # Attendre confirmation utilisateur
        input("\nAppuyez sur Entrée après avoir vérifié ces paramètres...")

    def generate_final_report(self):
        """Génère le rapport final"""
        print("\n" + "=" * 60)
        print("📋 RAPPORT FINAL DE VÉRIFICATION")
        print("=" * 60)
        
        # Résumé des résultats
        print("\n🔍 RÉSUMÉ DES TESTS:")
        
        if self.results.get('ping') == 'SUCCESS':
            print("✅ Connectivité réseau: OK")
        else:
            print("❌ Connectivité réseau: ÉCHEC")
        
        open_ports = self.results.get('open_ports', [])
        if open_ports:
            print(f"✅ Ports ouverts: {', '.join(open_ports)}")
        else:
            print("❌ Aucun port ouvert détecté")
        
        udp_responses = self.results.get('udp_responses', [])
        if udp_responses:
            print(f"✅ Réponses UDP: {len(udp_responses)} trouvées")
        else:
            print("❌ Aucune réponse UDP")
        
        ci_v_responses = self.results.get('ci_v_injection', [])
        if ci_v_responses:
            print(f"✅ Réponses CI-V: {len(ci_v_responses)} trouvées")
        else:
            print("❌ Aucune réponse CI-V")
        
        # Diagnostic
        print("\n🎯 DIAGNOSTIC:")
        
        if not open_ports:
            print("❌ PROBLÈME MAJEUR: Aucun port réseau ouvert")
            print("   → Network Control probablement désactivé")
            print("   → Vérifiez la configuration IC-R8600")
        elif '50001/UDP' in open_ports or '50002/UDP' in open_ports:
            print("✅ Ports UDP ICOM détectés")
            if not ci_v_responses:
                print("❌ Mais aucune réponse aux commandes CI-V")
                print("   → Protocole ou format incorrect")
        else:
            print("⚠️ Ports non-standard ouverts")
            print("   → Configuration IC-R8600 à vérifier")
        
        # Recommandations
        print("\n💡 RECOMMANDATIONS:")
        
        if not open_ports:
            print("1. Activez Network Control sur l'IC-R8600")
            print("2. Redémarrez l'IC-R8600")
            print("3. Vérifiez l'IP et les ports")
        elif ci_v_responses:
            print("1. Protocole partiellement fonctionnel")
            print("2. Affinez le format des commandes")
        else:
            print("1. Testez avec le logiciel officiel RS-R8600")
            print("2. Capturez le trafic réseau réel")
            print("3. Analysez le protocole propriétaire")

def main():
    """Fonction principale"""
    verifier = ICOMVerification("**************")
    verifier.run_complete_verification()

if __name__ == "__main__":
    main()
