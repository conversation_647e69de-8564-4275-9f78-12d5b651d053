#!/usr/bin/env python3
"""
Debug des vraies commandes CI-V pour vérifier l'effet sur l'IC-R8600
"""

import socket
import time
import sys

def test_real_power_commands():
    """Test des vraies commandes power avec observation du récepteur"""
    host = "**************"
    port = 23
    
    print("TEST COMMANDES POWER RÉELLES")
    print("=" * 50)
    print("IMPORTANT: Observez l'IC-R8600 pendant ce test !")
    print("=" * 50)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        sock.connect((host, port))
        
        # Lire le message de bienvenue
        try:
            welcome = sock.recv(1024)
            print(f"Connexion établie: {welcome.hex().upper()}")
        except:
            print("Connexion établie (pas de message de bienvenue)")
        
        # Commandes CI-V à tester avec délais d'observation
        commands = [
            # Format: (commande, description, délai d'observation)
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x19, 0x00, 0xFD]), "Lecture statut power", 2),
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x00, 0xFD]), "Power OFF", 5),
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x19, 0x00, 0xFD]), "Vérification power OFF", 2),
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x01, 0xFD]), "Power ON", 5),
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x19, 0x00, 0xFD]), "Vérification power ON", 2),
        ]
        
        for i, (cmd, desc, delay) in enumerate(commands):
            print(f"\n{i+1}. {desc}")
            print(f"   Commande: {cmd.hex().upper()}")
            print(f"   OBSERVEZ L'IC-R8600 MAINTENANT !")
            
            # Envoyer la commande
            sock.sendall(cmd)
            
            # Attendre la réponse
            time.sleep(0.5)
            try:
                response = sock.recv(1024)
                print(f"   Réponse: {response.hex().upper()}")
                
                # Analyser la réponse
                if b'\xFE\xFE' in response:
                    fe_pos = response.find(b'\xFE\xFE')
                    fd_pos = response.find(b'\xFD', fe_pos)
                    if fd_pos >= 0:
                        ci_v_data = response[fe_pos:fd_pos+1]
                        if len(ci_v_data) >= 6:
                            cmd_byte = ci_v_data[4]
                            if cmd_byte == 0xFB:
                                print("   -> ACK: Commande acceptée")
                            elif cmd_byte == 0xFA:
                                print("   -> NAK: Commande refusée")
                            elif cmd_byte == 0x19 and len(ci_v_data) >= 7:
                                power_status = ci_v_data[6]
                                status_text = "ON" if power_status == 0x01 else "OFF"
                                print(f"   -> Statut power: {status_text}")
                            else:
                                print(f"   -> Réponse: 0x{cmd_byte:02X}")
                
            except socket.timeout:
                print("   -> Pas de réponse")
            
            # Délai d'observation
            print(f"   Attente {delay}s pour observer l'effet...")
            time.sleep(delay)
        
        sock.close()
        print("\nTest terminé")
        
    except Exception as e:
        print(f"Erreur: {e}")

def test_alternative_commands():
    """Test avec des commandes alternatives"""
    host = "**************"
    port = 23
    
    print("\n" + "=" * 50)
    print("TEST COMMANDES ALTERNATIVES")
    print("=" * 50)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        sock.connect((host, port))
        
        # Lire le prompt
        try:
            sock.recv(1024)
        except:
            pass
        
        # Commandes alternatives à tester
        alt_commands = [
            # Différentes adresses IC-R8600
            (bytes([0xFE, 0xFE, 0x96, 0xE0, 0x18, 0x00, 0xFD]), "Power OFF (adresse 0x96)"),
            (bytes([0xFE, 0xFE, 0x88, 0xE0, 0x18, 0x00, 0xFD]), "Power OFF (adresse 0x88)"),
            (bytes([0xFE, 0xFE, 0x00, 0xE0, 0x18, 0x00, 0xFD]), "Power OFF (broadcast)"),
            
            # Commandes sans préambule
            (bytes([0x18, 0x00]), "Power OFF (sans préambule)"),
            (bytes([0x18, 0x01]), "Power ON (sans préambule)"),
            
            # Commandes avec terminaison différente
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x00, 0x0D]), "Power OFF (CR)"),
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x00, 0x0A]), "Power OFF (LF)"),
        ]
        
        for i, (cmd, desc) in enumerate(alt_commands):
            print(f"\n{i+1}. {desc}")
            print(f"   Commande: {cmd.hex().upper()}")
            
            sock.sendall(cmd)
            time.sleep(1)
            
            try:
                response = sock.recv(1024)
                print(f"   Réponse: {response.hex().upper()}")
            except socket.timeout:
                print("   -> Pas de réponse")
            
            print("   Observez l'IC-R8600...")
            time.sleep(3)
        
        sock.close()
        
    except Exception as e:
        print(f"Erreur commandes alternatives: {e}")

def test_text_commands():
    """Test avec des commandes texte (si l'IC-R8600 supporte)"""
    host = "**************"
    port = 23
    
    print("\n" + "=" * 50)
    print("TEST COMMANDES TEXTE")
    print("=" * 50)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        sock.connect((host, port))
        
        # Lire le prompt
        try:
            welcome = sock.recv(1024)
            print(f"Message de bienvenue: {welcome.decode('ascii', errors='ignore')}")
        except:
            pass
        
        # Commandes texte à tester
        text_commands = [
            "POWER OFF\r\n",
            "POWER ON\r\n",
            "PWR 0\r\n",
            "PWR 1\r\n",
            "*PWR0\r\n",
            "*PWR1\r\n",
            "help\r\n",
            "?\r\n",
            "status\r\n",
        ]
        
        for i, cmd in enumerate(text_commands):
            print(f"\n{i+1}. Commande texte: {repr(cmd)}")
            
            sock.sendall(cmd.encode('ascii'))
            time.sleep(1)
            
            try:
                response = sock.recv(1024)
                text_response = response.decode('ascii', errors='ignore')
                print(f"   Réponse: {repr(text_response)}")
                if response.hex():
                    print(f"   Hex: {response.hex().upper()}")
            except socket.timeout:
                print("   -> Pas de réponse")
            
            print("   Observez l'IC-R8600...")
            time.sleep(2)
        
        sock.close()
        
    except Exception as e:
        print(f"Erreur commandes texte: {e}")

if __name__ == "__main__":
    print("DIAGNOSTIC COMPLET DES COMMANDES IC-R8600")
    print("Observez attentivement l'IC-R8600 pendant les tests !")
    print()
    
    test_real_power_commands()
    
    input("\nAppuyez sur Entrée pour tester les commandes alternatives...")
    test_alternative_commands()
    
    input("\nAppuyez sur Entrée pour tester les commandes texte...")
    test_text_commands()
    
    print("\nDiagnostic terminé !")
    print("Si aucune commande n'a eu d'effet, le problème est que")
    print("l'IC-R8600 n'accepte pas les commandes CI-V via Telnet.")
