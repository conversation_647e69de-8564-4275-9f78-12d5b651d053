#!/usr/bin/env python3
"""
Diagnostic complet de la connexion IC-R8600
Vérifie tous les aspects de la connectivité réseau
"""

import socket
import subprocess
import time
import sys
import os

def test_network_basics():
    """Test des bases réseau"""
    print("🌐 Test des bases réseau...")
    
    host = "**************"
    
    # Test 1: Ping
    print(f"  🏓 Ping vers {host}")
    try:
        result = subprocess.run(['ping', '-n', '4', host], 
                              capture_output=True, text=True, timeout=15)
        if result.returncode == 0:
            print("  ✅ Ping réussi")
            # Extraire les statistiques
            lines = result.stdout.split('\n')
            for line in lines:
                if 'Paquets' in line or 'packets' in line:
                    print(f"    {line.strip()}")
        else:
            print("  ❌ Ping échoué")
            print(f"    {result.stdout}")
    except Exception as e:
        print(f"  ❌ Erreur ping: {e}")
    
    # Test 2: ARP
    print(f"  🔍 Table ARP pour {host}")
    try:
        result = subprocess.run(['arp', '-a'], capture_output=True, text=True)
        if host in result.stdout:
            for line in result.stdout.split('\n'):
                if host in line:
                    print(f"    ✅ {line.strip()}")
        else:
            print("    ⚠️ Pas d'entrée ARP trouvée")
    except Exception as e:
        print(f"    ❌ Erreur ARP: {e}")

def test_port_scanning():
    """Scan détaillé des ports"""
    print("\n🔍 Scan détaillé des ports...")
    
    host = "**************"
    ports_to_test = [23, 80, 443, 8080, 50001, 50002, 50003, 50004, 50005]
    
    open_ports = []
    
    for port in ports_to_test:
        print(f"  Port {port}...", end=" ")
        
        # Test TCP
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2.0)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print("✅ TCP ouvert")
                open_ports.append((port, "TCP"))
                continue
        except:
            pass
        
        # Test UDP (envoi de données et vérification d'erreur ICMP)
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(1.0)
            sock.sendto(b'test', (host, port))
            
            try:
                # Si on reçoit quelque chose, le port est ouvert
                data, addr = sock.recvfrom(1024)
                print("✅ UDP ouvert (réponse)")
                open_ports.append((port, "UDP"))
                sock.close()
                continue
            except socket.timeout:
                # Timeout peut signifier port ouvert mais pas de réponse
                print("⚠️ UDP possible")
                open_ports.append((port, "UDP?"))
                sock.close()
                continue
        except Exception as e:
            if "unreachable" in str(e).lower():
                print("❌ UDP fermé")
            else:
                print("❌ UDP inaccessible")
        
        print("❌ Fermé")
    
    print(f"\n  📊 Ports ouverts détectés: {len([p for p in open_ports if '?' not in p[1]])}")
    for port, proto in open_ports:
        print(f"    - Port {port} ({proto})")

def test_firewall_windows():
    """Test du firewall Windows"""
    print("\n🛡️ Test du firewall Windows...")
    
    try:
        # Vérifier le statut du firewall
        result = subprocess.run(['netsh', 'advfirewall', 'show', 'allprofiles', 'state'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("  📊 État du firewall:")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'État' in line or 'State' in line:
                    print(f"    {line.strip()}")
        
        # Vérifier les règles pour Python
        result = subprocess.run(['netsh', 'advfirewall', 'firewall', 'show', 'rule', 'name=all'], 
                              capture_output=True, text=True)
        
        python_rules = []
        if 'python' in result.stdout.lower():
            lines = result.stdout.split('\n')
            for i, line in enumerate(lines):
                if 'python' in line.lower() and 'Nom de la règle' in line:
                    python_rules.append(line.strip())
        
        if python_rules:
            print(f"  🐍 Règles Python trouvées: {len(python_rules)}")
            for rule in python_rules[:3]:  # Limiter l'affichage
                print(f"    {rule}")
        else:
            print("  ⚠️ Aucune règle Python trouvée")
            
    except Exception as e:
        print(f"  ❌ Erreur firewall: {e}")

def test_network_interfaces():
    """Test des interfaces réseau"""
    print("\n🔌 Test des interfaces réseau...")
    
    try:
        result = subprocess.run(['ipconfig', '/all'], capture_output=True, text=True)
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            current_adapter = ""
            
            for line in lines:
                line = line.strip()
                
                if 'Carte' in line or 'adapter' in line.lower():
                    current_adapter = line
                    print(f"  🔌 {current_adapter}")
                
                elif 'Adresse IPv4' in line or 'IPv4 Address' in line:
                    ip = line.split(':')[-1].strip()
                    print(f"    IP: {ip}")
                    
                    # Vérifier si on est sur le même réseau que l'IC-R8600
                    if ip.startswith('192.168.37.'):
                        print(f"    ✅ Même réseau que l'IC-R8600")
                    elif ip.startswith('192.168.'):
                        print(f"    ⚠️ Réseau différent de l'IC-R8600")
                
                elif 'Masque de sous-réseau' in line or 'Subnet Mask' in line:
                    mask = line.split(':')[-1].strip()
                    print(f"    Masque: {mask}")
                    
    except Exception as e:
        print(f"  ❌ Erreur interfaces: {e}")

def test_ic_r8600_specific():
    """Tests spécifiques à l'IC-R8600"""
    print("\n📻 Tests spécifiques IC-R8600...")
    
    host = "**************"
    
    # Test 1: Connexion Telnet (on sait qu'elle fonctionne)
    print("  🔌 Test Telnet (port 23)")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3.0)
        sock.connect((host, 23))
        
        welcome = sock.recv(1024)
        if b'IC-R8600' in welcome:
            print("    ✅ IC-R8600 identifié via Telnet")
        else:
            print("    ⚠️ Connexion Telnet OK mais identification incertaine")
        
        sock.close()
        
    except Exception as e:
        print(f"    ❌ Erreur Telnet: {e}")
    
    # Test 2: Vérification des ports UDP avec netstat
    print("  📊 Vérification des ports UDP actifs")
    try:
        result = subprocess.run(['netstat', '-an'], capture_output=True, text=True)
        
        udp_ports = []
        for line in result.stdout.split('\n'):
            if 'UDP' in line and '**************' in line:
                udp_ports.append(line.strip())
        
        if udp_ports:
            print(f"    ✅ Ports UDP actifs sur **************:")
            for port in udp_ports:
                print(f"      {port}")
        else:
            print("    ❌ Aucun port UDP actif détecté sur **************")
            
    except Exception as e:
        print(f"    ❌ Erreur netstat: {e}")

def generate_recommendations():
    """Génère des recommandations basées sur les tests"""
    print("\n" + "=" * 70)
    print("💡 RECOMMANDATIONS")
    print("=" * 70)
    
    print("1. 🔧 Vérifications IC-R8600:")
    print("   - Network Control est bien sur ON")
    print("   - IC-R8600 redémarré après configuration")
    print("   - Voyant LOCAL éteint sur le panneau avant")
    print("   - Configuration IP correcte (**************)")
    
    print("\n2. 🌐 Vérifications réseau:")
    print("   - PC et IC-R8600 sur le même réseau")
    print("   - Câble RJ45 fonctionnel")
    print("   - Switch/routeur configuré correctement")
    
    print("\n3. 🛡️ Vérifications sécurité:")
    print("   - Firewall Windows autorise Python")
    print("   - Antivirus n'interfère pas")
    print("   - Ports UDP 50001-50003 non bloqués")
    
    print("\n4. 🔄 Actions à essayer:")
    print("   - Redémarrer l'IC-R8600")
    print("   - Redémarrer le PC")
    print("   - Tester avec un autre PC")
    print("   - Utiliser RS-R8600 officiel pour comparaison")

if __name__ == "__main__":
    print("=" * 70)
    print("🔍 DIAGNOSTIC COMPLET IC-R8600")
    print("=" * 70)
    
    # Exécuter tous les tests
    test_network_basics()
    test_port_scanning()
    test_firewall_windows()
    test_network_interfaces()
    test_ic_r8600_specific()
    
    # Générer les recommandations
    generate_recommendations()
    
    print("\n" + "=" * 70)
    print("✅ Diagnostic terminé")
    print("=" * 70)
