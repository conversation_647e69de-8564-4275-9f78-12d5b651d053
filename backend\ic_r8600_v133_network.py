#!/usr/bin/env python3
"""
Contrôle réseau spécifique pour IC-R8600 version 1.33
Exploite les capacités limitées de cette version firmware
"""

import socket
import time
import logging
import threading
from typing import Optional, Dict, Any, List

class ICR8600_V133_Network:
    """Contrôle réseau spécialisé pour IC-R8600 v1.33"""
    
    def __init__(self, host="**************"):
        self.host = host
        self.logger = logging.getLogger(__name__)
        
        # Connexions multiples pour v1.33
        self.telnet_socket = None
        self.connected = False
        
        # État du récepteur
        self.current_frequency = 145.5
        self.current_mode = "FM"
        self.power_state = True
        
        # Thread de monitoring
        self.monitor_thread = None
        self.stop_monitor = False

    def connect(self) -> bool:
        """Connexion spécialisée v1.33"""
        try:
            self.logger.info("Connexion IC-R8600 v1.33 via r<PERSON><PERSON>")
            
            # Méthode 1: Connexion Telnet (seule méthode fiable en v1.33)
            if self._connect_telnet():
                self.connected = True
                self._start_monitor()
                self.logger.info("Contrôle réseau v1.33 activé via Telnet")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Erreur connexion v1.33: {e}")
            return False

    def _connect_telnet(self) -> bool:
        """Connexion Telnet pour v1.33"""
        try:
            self.telnet_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.telnet_socket.settimeout(5.0)
            self.telnet_socket.connect((self.host, 23))
            
            # Configuration initiale pour v1.33
            init_commands = [
                b'*CLS\r\n',                    # Clear
                b'SYST:COMM:SER:ECHO OFF\r\n', # Disable echo
                b'FORM:BORD NORM\r\n',          # Normal byte order
            ]
            
            for cmd in init_commands:
                self.telnet_socket.sendall(cmd)
                time.sleep(0.2)
                
                # Vider le buffer
                try:
                    self.telnet_socket.recv(1024)
                except socket.timeout:
                    pass
            
            self.logger.info("Connexion Telnet v1.33 établie")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur Telnet v1.33: {e}")
            return False

    def _send_telnet_command(self, command: str) -> Optional[str]:
        """Envoie une commande via Telnet"""
        if not self.telnet_socket:
            return None
            
        try:
            cmd_bytes = command.encode('ascii') + b'\r\n'
            self.telnet_socket.sendall(cmd_bytes)
            time.sleep(0.1)
            
            # Lire la réponse
            try:
                self.telnet_socket.settimeout(2.0)
                response = self.telnet_socket.recv(1024)
                return response.decode('ascii', errors='ignore').strip()
            except socket.timeout:
                return None
                
        except Exception as e:
            self.logger.error(f"Erreur commande Telnet: {e}")
            return None

    def _send_ci_v_via_telnet(self, ci_v_command: List[int]) -> bool:
        """Envoie une commande CI-V via Telnet (méthode v1.33 avec reconnexion)"""
        max_retries = 3

        for attempt in range(max_retries):
            try:
                # Vérifier la connexion
                if not self.telnet_socket:
                    if not self._connect_telnet():
                        continue

                # Construire la commande CI-V
                ci_v_bytes = bytes([0xFE, 0xFE, 0x94, 0xE0] + ci_v_command + [0xFD])

                # Méthode 1: Envoi direct CI-V
                self.telnet_socket.sendall(ci_v_bytes)
                self.logger.info(f"Commande CI-V v1.33 envoyée (tentative {attempt+1}): {ci_v_bytes.hex().upper()}")

                # Attendre un peu pour que la commande soit traitée
                time.sleep(1.0)

                # Considérer comme succès si pas d'exception
                return True

            except (ConnectionResetError, OSError, socket.error) as e:
                self.logger.warning(f"Connexion fermée (tentative {attempt+1}): {e}")

                # Fermer et réinitialiser la connexion
                if self.telnet_socket:
                    try:
                        self.telnet_socket.close()
                    except:
                        pass
                    self.telnet_socket = None

                # Attendre avant de retry
                time.sleep(1.0)

                if attempt < max_retries - 1:
                    self.logger.info("Tentative de reconnexion...")
                    continue
                else:
                    self.logger.error("Échec après toutes les tentatives")
                    return False

            except Exception as e:
                self.logger.error(f"Erreur CI-V via Telnet v1.33: {e}")
                return False

        return False

    def _start_monitor(self):
        """Démarre le monitoring de connexion"""
        self.stop_monitor = False
        self.monitor_thread = threading.Thread(target=self._monitor_worker, daemon=True)
        self.monitor_thread.start()

    def _monitor_worker(self):
        """Worker de monitoring"""
        while not self.stop_monitor and self.connected:
            try:
                # Test de connectivité périodique
                response = self._send_telnet_command("*IDN?")
                if not response:
                    self.logger.warning("Perte de connexion Telnet v1.33")
                    self._reconnect()
                
                time.sleep(10)  # Check toutes les 10 secondes
                
            except Exception as e:
                self.logger.error(f"Erreur monitor v1.33: {e}")
                break

    def _reconnect(self):
        """Reconnexion automatique"""
        try:
            if self.telnet_socket:
                self.telnet_socket.close()
            
            time.sleep(2)
            self._connect_telnet()
            
        except Exception as e:
            self.logger.error(f"Erreur reconnexion v1.33: {e}")

    def disconnect(self):
        """Déconnexion"""
        self.logger.info("Déconnexion IC-R8600 v1.33")
        
        self.stop_monitor = True
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        
        if self.telnet_socket:
            try:
                self.telnet_socket.close()
            except:
                pass
            self.telnet_socket = None
        
        self.connected = False

    # Méthodes de contrôle
    def power_on(self) -> bool:
        """Allume l'IC-R8600"""
        success = self._send_ci_v_via_telnet([0x18, 0x01])
        if success:
            self.power_state = True
            self.logger.info("Power ON envoyé via réseau v1.33")
        return success

    def power_off(self) -> bool:
        """Éteint l'IC-R8600"""
        success = self._send_ci_v_via_telnet([0x18, 0x00])
        if success:
            self.power_state = False
            self.logger.info("Power OFF envoyé via réseau v1.33")
        return success

    def set_frequency(self, freq_hz: int) -> bool:
        """Définit la fréquence"""
        try:
            # Méthode 1: CI-V
            freq_str = f"{freq_hz:010d}"
            bcd_bytes = []
            for i in range(4, -1, -1):
                digit1 = int(freq_str[i*2])
                digit2 = int(freq_str[i*2 + 1])
                bcd_bytes.append((digit1 << 4) | digit2)

            success = self._send_ci_v_via_telnet([0x05] + bcd_bytes)
            
            # Méthode 2: SCPI si CI-V échoue
            if not success:
                freq_mhz = freq_hz / 1000000.0
                scpi_cmd = f"FREQ {freq_mhz:.6f}MHZ"
                response = self._send_telnet_command(scpi_cmd)
                success = response is not None
            
            if success:
                self.current_frequency = freq_hz / 1000000.0
                self.logger.info(f"Fréquence {self.current_frequency:.6f} MHz définie via réseau v1.33")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Erreur set_frequency v1.33: {e}")
            return False

    def set_mode(self, mode: str) -> bool:
        """Définit le mode"""
        modes = {'LSB': 0x00, 'USB': 0x01, 'AM': 0x02, 'CW': 0x03, 'FM': 0x05, 'WFM': 0x06}
        
        if mode not in modes:
            return False
        
        # Méthode 1: CI-V
        success = self._send_ci_v_via_telnet([0x06, modes[mode]])
        
        # Méthode 2: SCPI si CI-V échoue
        if not success:
            scpi_cmd = f"MODE {mode}"
            response = self._send_telnet_command(scpi_cmd)
            success = response is not None
        
        if success:
            self.current_mode = mode
            self.logger.info(f"Mode {mode} défini via réseau v1.33")
        
        return success

    def get_frequency(self) -> Optional[float]:
        """Lit la fréquence (simulation pour v1.33)"""
        return self.current_frequency

    def get_mode(self) -> Optional[str]:
        """Lit le mode (simulation pour v1.33)"""
        return self.current_mode

    def get_status(self) -> Dict[str, Any]:
        """Récupère l'état"""
        return {
            "connected": self.connected,
            "host": self.host,
            "connection_type": "IC-R8600_v1.33_Network_Telnet",
            "firmware_version": "1.33",
            "power_state": self.power_state,
            "frequency": self.current_frequency,
            "mode": self.current_mode
        }

def test_v133_network():
    """Test du contrôle réseau v1.33"""
    print("🔧 TEST CONTRÔLE RÉSEAU IC-R8600 V1.33")
    print("=" * 50)
    
    # Configuration logging
    logging.basicConfig(level=logging.INFO)
    
    controller = ICR8600_V133_Network("**************")
    
    try:
        # Connexion
        if controller.connect():
            print("✅ Connexion réseau v1.33 établie")
            
            # Test Power OFF
            print("\nTest Power OFF - OBSERVEZ L'IC-R8600 !")
            if controller.power_off():
                print("✅ Power OFF envoyé")
            else:
                print("❌ Échec Power OFF")
            
            time.sleep(3)
            
            # Test Power ON
            print("\nTest Power ON - OBSERVEZ L'IC-R8600 !")
            if controller.power_on():
                print("✅ Power ON envoyé")
            else:
                print("❌ Échec Power ON")
            
            time.sleep(3)
            
            # Test fréquence
            print("\nTest fréquence 145.500 MHz")
            if controller.set_frequency(145500000):
                print("✅ Fréquence envoyée")
            else:
                print("❌ Échec fréquence")
            
            # Statut
            status = controller.get_status()
            print(f"\nStatut: {status}")
            
            controller.disconnect()
            
        else:
            print("❌ Échec connexion réseau v1.33")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
    finally:
        controller.disconnect()

if __name__ == "__main__":
    test_v133_network()
