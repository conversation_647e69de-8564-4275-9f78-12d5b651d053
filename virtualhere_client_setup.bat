@echo off
REM ========================================
REM VirtualHere Client Setup - PC Supervision
REM Installation et configuration automatique
REM ========================================

echo [INFO] Installation VirtualHere Client pour IC-R8600
echo ========================================

REM Créer le répertoire d'installation
if not exist "C:\VirtualHere-Client" mkdir "C:\VirtualHere-Client"
cd /d "C:\VirtualHere-Client"

REM Télécharger VirtualHere Client
echo [INFO] Téléchargement VirtualHere Client...
powershell -Command "Invoke-WebRequest -Uri 'https://www.virtualhere.com/sites/default/files/usbclient/vhui64.exe' -OutFile 'vhui64.exe'"

if not exist "vhui64.exe" (
    echo [ERROR] Échec téléchargement VirtualHere Client
    pause
    exit /b 1
)

REM Configuration VirtualHere Client
echo [INFO] Configuration VirtualHere Client...
echo # Configuration VirtualHere Client pour IC-R8600 > client_config.ini
echo # Connexion automatique au serveur >> client_config.ini
echo AutoFind=1 >> client_config.ini
echo # Reconnexion automatique >> client_config.ini
echo AutoReconnect=1 >> client_config.ini
echo # Log pour debug >> client_config.ini
echo LogLevel=2 >> client_config.ini
echo LogFile=virtualhere_client.log >> client_config.ini

REM Créer script de connexion
echo [INFO] Création scripts de gestion...
echo @echo off > connect_ic_r8600.bat
echo echo [INFO] Connexion à l'IC-R8600 distant... >> connect_ic_r8600.bat
echo cd /d "C:\VirtualHere-Client" >> connect_ic_r8600.bat
echo echo Démarrage VirtualHere Client... >> connect_ic_r8600.bat
echo start "" vhui64.exe >> connect_ic_r8600.bat
echo timeout /t 5 >> connect_ic_r8600.bat
echo echo. >> connect_ic_r8600.bat
echo echo [INFO] VirtualHere Client démarré ! >> connect_ic_r8600.bat
echo echo Recherchez 'IC-R8600-Remote' dans la liste des serveurs >> connect_ic_r8600.bat
echo echo Clic droit sur l'IC-R8600 ^> 'Use this device' >> connect_ic_r8600.bat
echo pause >> connect_ic_r8600.bat

REM Script de vérification des ports COM
echo @echo off > check_com_ports.bat
echo echo [INFO] Vérification des ports COM disponibles: >> check_com_ports.bat
echo echo ======================================== >> check_com_ports.bat
echo powershell "Get-WmiObject -Class Win32_SerialPort | Select-Object DeviceID, Description, Name | Format-Table -AutoSize" >> check_com_ports.bat
echo echo. >> check_com_ports.bat
echo echo [INFO] Recherche spécifique IC-R8600: >> check_com_ports.bat
echo powershell "Get-WmiObject -Class Win32_SerialPort | Where-Object { $_.Description -like '*ICOM*' -or $_.Description -like '*IC-R8600*' -or $_.Description -like '*USB Serial*' } | Select-Object DeviceID, Description" >> check_com_ports.bat
echo echo. >> check_com_ports.bat
echo echo [INFO] Gestionnaire de périphériques: >> check_com_ports.bat
echo echo Ouvrez le Gestionnaire de périphériques pour voir les ports COM >> check_com_ports.bat
echo start devmgmt.msc >> check_com_ports.bat
echo pause >> check_com_ports.bat

REM Script de test de connexion
echo @echo off > test_connection.py
echo # Test de connexion IC-R8600 via VirtualHere >> test_connection.py
echo import serial >> test_connection.py
echo import serial.tools.list_ports >> test_connection.py
echo import time >> test_connection.py
echo. >> test_connection.py
echo def find_icom_port(): >> test_connection.py
echo     """Trouve automatiquement le port COM de l'IC-R8600""" >> test_connection.py
echo     ports = serial.tools.list_ports.comports() >> test_connection.py
echo     for port in ports: >> test_connection.py
echo         if 'ICOM' in port.description.upper() or 'IC-R8600' in port.description.upper(): >> test_connection.py
echo             return port.device >> test_connection.py
echo         if 'USB Serial' in port.description: >> test_connection.py
echo             return port.device >> test_connection.py
echo     return None >> test_connection.py
echo. >> test_connection.py
echo def test_icom_communication(port): >> test_connection.py
echo     """Test de communication avec l'IC-R8600""" >> test_connection.py
echo     try: >> test_connection.py
echo         ser = serial.Serial(port, 19200, timeout=2) >> test_connection.py
echo         print(f"Connexion établie sur {port}") >> test_connection.py
echo         >> test_connection.py
echo         # Commande CI-V de test (lecture fréquence) >> test_connection.py
echo         test_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD]) >> test_connection.py
echo         ser.write(test_cmd) >> test_connection.py
echo         time.sleep(0.5) >> test_connection.py
echo         >> test_connection.py
echo         response = ser.read(20) >> test_connection.py
echo         if response: >> test_connection.py
echo             print(f"Réponse reçue: {response.hex().upper()}") >> test_connection.py
echo             return True >> test_connection.py
echo         else: >> test_connection.py
echo             print("Aucune réponse de l'IC-R8600") >> test_connection.py
echo             return False >> test_connection.py
echo         >> test_connection.py
echo     except Exception as e: >> test_connection.py
echo         print(f"Erreur: {e}") >> test_connection.py
echo         return False >> test_connection.py
echo     finally: >> test_connection.py
echo         try: >> test_connection.py
echo             ser.close() >> test_connection.py
echo         except: >> test_connection.py
echo             pass >> test_connection.py
echo. >> test_connection.py
echo if __name__ == "__main__": >> test_connection.py
echo     print("=== Test de connexion IC-R8600 via VirtualHere ===") >> test_connection.py
echo     >> test_connection.py
echo     # Lister tous les ports >> test_connection.py
echo     print("Ports COM disponibles:") >> test_connection.py
echo     ports = serial.tools.list_ports.comports() >> test_connection.py
echo     for port in ports: >> test_connection.py
echo         print(f"  {port.device}: {port.description}") >> test_connection.py
echo     >> test_connection.py
echo     # Trouver le port ICOM >> test_connection.py
echo     icom_port = find_icom_port() >> test_connection.py
echo     if icom_port: >> test_connection.py
echo         print(f"\\nPort IC-R8600 détecté: {icom_port}") >> test_connection.py
echo         if test_icom_communication(icom_port): >> test_connection.py
echo             print("✅ Communication IC-R8600 réussie !") >> test_connection.py
echo         else: >> test_connection.py
echo             print("❌ Échec communication IC-R8600") >> test_connection.py
echo     else: >> test_connection.py
echo         print("❌ Port IC-R8600 non trouvé") >> test_connection.py
echo         print("Vérifiez que VirtualHere Client est connecté") >> test_connection.py

REM Script de test Python
echo @echo off > test_ic_r8600.bat
echo echo [INFO] Test de communication IC-R8600... >> test_ic_r8600.bat
echo python test_connection.py >> test_ic_r8600.bat
echo pause >> test_ic_r8600.bat

echo.
echo ========================================
echo [SUCCESS] Installation VirtualHere Client terminée !
echo ========================================
echo.
echo Scripts créés:
echo - connect_ic_r8600.bat  : Connecter au serveur distant
echo - check_com_ports.bat   : Vérifier les ports COM
echo - test_ic_r8600.bat     : Tester la communication
echo - test_connection.py    : Script de test Python
echo.
echo ÉTAPES SUIVANTES:
echo 1. Lancez: connect_ic_r8600.bat
echo 2. Dans VirtualHere Client, trouvez 'IC-R8600-Remote'
echo 3. Clic droit sur l'IC-R8600 ^> 'Use this device'
echo 4. Vérifiez avec: check_com_ports.bat
echo 5. Testez avec: test_ic_r8600.bat
echo.
pause
