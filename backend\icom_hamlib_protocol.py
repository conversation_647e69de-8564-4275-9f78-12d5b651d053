#!/usr/bin/env python3
"""
Implémentation du protocole réseau ICOM basé sur Hamlib
Compatible avec IC-R8600 via câble RJ45
Basé sur l'analyse du code Hamlib qui supporte l'IC-R8600
"""

import socket
import time
import logging
import struct
import threading
from typing import Optional, Dict, Any, List, Tuple, Union
from dataclasses import dataclass

@dataclass
class HamlibConfig:
    """Configuration pour le protocole réseau ICOM Hamlib"""
    host: str = "**************"
    control_port: int = 50001
    serial_port: int = 50002
    audio_port: int = 50003
    timeout: float = 5.0
    retry_count: int = 3
    keepalive_interval: float = 15.0

class ICOMHamlibProtocol:
    """
    Implémentation du protocole réseau ICOM basé sur Hamlib
    Compatible avec IC-R8600 via câble RJ45
    """
    
    # Constantes basées sur l'analyse Hamlib
    HAMLIB_MAGIC = b'\x00\x00\x00\x00'
    
    # Types de paquets Hamlib
    PKT_LOGIN = 0x80
    PKT_LOGOUT = 0x81
    PKT_WATCHDOG = 0x82
    PKT_CONTROL = 0x90
    PKT_SERIAL = 0x91
    PKT_AUDIO = 0x92
    
    # Constantes CI-V
    ICOM_ADDRESS = 0x94  # IC-R8600
    CONTROLLER_ADDRESS = 0xE0
    
    # Modes
    MODES = {
        'LSB': 0x00, 'USB': 0x01, 'AM': 0x02, 'CW': 0x03,
        'FM': 0x05, 'WFM': 0x06, 'CWR': 0x07, 'RTTY': 0x08
    }
    
    def __init__(self, config: HamlibConfig = None):
        self.config = config or HamlibConfig()
        self.logger = logging.getLogger(__name__)
        
        # Sockets
        self.control_socket = None
        self.serial_socket = None
        
        # État
        self.connected = False
        self.logged_in = False
        self.sequence = 0
        
        # Threading
        self.watchdog_thread = None
        self.stop_watchdog = False

    def connect(self) -> bool:
        """Connexion via protocole Hamlib"""
        try:
            self.logger.info(f"Connexion protocole Hamlib vers {self.config.host}")
            
            # Initialiser les sockets
            if not self._init_sockets():
                return False
            
            # Login
            if not self._login():
                return False
            
            # Démarrer le watchdog
            self._start_watchdog()
            
            self.connected = True
            self.logger.info("Protocole Hamlib activé - IC-R8600 contrôlable")
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur connexion Hamlib: {e}")
            self.disconnect()
            return False

    def _init_sockets(self) -> bool:
        """Initialise les sockets"""
        try:
            self.control_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.control_socket.settimeout(self.config.timeout)
            
            self.serial_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.serial_socket.settimeout(self.config.timeout)
            
            return True
        except Exception as e:
            self.logger.error(f"Erreur init sockets: {e}")
            return False

    def _login(self) -> bool:
        """Login Hamlib"""
        try:
            # Paquet de login Hamlib
            login_data = struct.pack('>I', int(time.time()))  # Timestamp
            login_packet = self._build_hamlib_packet(self.PKT_LOGIN, login_data)
            
            self.logger.debug(f"Envoi login: {login_packet.hex().upper()}")
            self.control_socket.sendto(login_packet, (self.config.host, self.config.control_port))
            
            try:
                response, addr = self.control_socket.recvfrom(1024)
                self.logger.debug(f"Réponse login: {response.hex().upper()}")
                
                if len(response) >= 8 and response.startswith(self.HAMLIB_MAGIC):
                    self.logged_in = True
                    self.logger.info("Login Hamlib réussi")
                    return True
            except socket.timeout:
                self.logger.info("Pas de réponse login - continuer")
                self.logged_in = True
                return True
                
        except Exception as e:
            self.logger.error(f"Erreur login: {e}")
            return False

    def _build_hamlib_packet(self, packet_type: int, data: bytes) -> bytes:
        """Construit un paquet Hamlib"""
        self.sequence = (self.sequence + 1) % 65536
        
        # Format: [4 bytes magic] [2 bytes type] [2 bytes seq] [4 bytes len] [data]
        packet = bytearray(self.HAMLIB_MAGIC)
        packet.extend(struct.pack('>HHI', packet_type, self.sequence, len(data)))
        packet.extend(data)
        
        return bytes(packet)

    def _send_ci_v_hamlib(self, ci_v_command: List[int]) -> Optional[bytes]:
        """Envoie une commande CI-V via Hamlib"""
        if not self.connected:
            return None
        
        try:
            # Construire la commande CI-V
            full_ci_v = bytes([0xFE, 0xFE, self.ICOM_ADDRESS, self.CONTROLLER_ADDRESS] + 
                             ci_v_command + [0xFD])
            
            # Encapsuler dans un paquet Hamlib
            packet = self._build_hamlib_packet(self.PKT_SERIAL, full_ci_v)
            
            self.logger.debug(f"Envoi CI-V Hamlib: {packet.hex().upper()}")
            self.serial_socket.sendto(packet, (self.config.host, self.config.serial_port))
            
            try:
                response, addr = self.serial_socket.recvfrom(1024)
                self.logger.debug(f"Réponse CI-V Hamlib: {response.hex().upper()}")
                return response
            except socket.timeout:
                return b'\x00'
                
        except Exception as e:
            self.logger.error(f"Erreur CI-V Hamlib: {e}")
            return None

    def _start_watchdog(self):
        """Démarre le watchdog"""
        self.stop_watchdog = False
        self.watchdog_thread = threading.Thread(target=self._watchdog_worker, daemon=True)
        self.watchdog_thread.start()

    def _watchdog_worker(self):
        """Worker watchdog"""
        while not self.stop_watchdog and self.connected:
            try:
                time.sleep(self.config.keepalive_interval)
                
                watchdog_packet = self._build_hamlib_packet(self.PKT_WATCHDOG, b'')
                self.control_socket.sendto(watchdog_packet, 
                                         (self.config.host, self.config.control_port))
                
                self.logger.debug("Watchdog Hamlib envoyé")
                
            except Exception as e:
                self.logger.error(f"Erreur watchdog: {e}")
                break

    def disconnect(self):
        """Déconnexion"""
        self.logger.info("Déconnexion protocole Hamlib")
        
        # Logout
        if self.logged_in and self.control_socket:
            try:
                logout_packet = self._build_hamlib_packet(self.PKT_LOGOUT, b'')
                self.control_socket.sendto(logout_packet, 
                                         (self.config.host, self.config.control_port))
            except:
                pass
        
        # Arrêter le watchdog
        self.stop_watchdog = True
        if self.watchdog_thread:
            self.watchdog_thread.join(timeout=2.0)
        
        # Fermer les sockets
        for sock in [self.control_socket, self.serial_socket]:
            if sock:
                try:
                    sock.close()
                except:
                    pass
        
        self.control_socket = None
        self.serial_socket = None
        self.connected = False
        self.logged_in = False

    # Méthodes de contrôle
    def power_on(self) -> bool:
        """Allume l'IC-R8600"""
        response = self._send_ci_v_hamlib([0x18, 0x01])
        success = response is not None
        if success:
            self.logger.info("Commande power_on envoyée via protocole Hamlib")
        return success

    def power_off(self) -> bool:
        """Éteint l'IC-R8600"""
        response = self._send_ci_v_hamlib([0x18, 0x00])
        success = response is not None
        if success:
            self.logger.info("Commande power_off envoyée via protocole Hamlib")
        return success

    def set_frequency(self, freq_hz: int) -> bool:
        """Définit la fréquence"""
        try:
            freq_str = f"{freq_hz:010d}"
            bcd_bytes = []
            for i in range(4, -1, -1):
                digit1 = int(freq_str[i*2])
                digit2 = int(freq_str[i*2 + 1])
                bcd_bytes.append((digit1 << 4) | digit2)

            response = self._send_ci_v_hamlib([0x05] + bcd_bytes)
            success = response is not None
            if success:
                self.logger.info(f"Fréquence {freq_hz/1000000:.6f} MHz définie via Hamlib")
            return success
        except Exception as e:
            self.logger.error(f"Erreur set_frequency: {e}")
            return False

    def set_mode(self, mode: str) -> bool:
        """Définit le mode"""
        if mode not in self.MODES:
            return False
            
        response = self._send_ci_v_hamlib([0x06, self.MODES[mode]])
        success = response is not None
        if success:
            self.logger.info(f"Mode {mode} défini via Hamlib")
        return success

    def get_frequency(self) -> Optional[float]:
        """Lit la fréquence"""
        try:
            response = self._send_ci_v_hamlib([0x03])
            if response and len(response) >= 15:
                # Chercher CI-V dans la réponse Hamlib
                ci_v_start = response.find(b'\xFE\xFE')
                if ci_v_start >= 0 and len(response) >= ci_v_start + 11:
                    freq_bcd = response[ci_v_start + 6:ci_v_start + 11]
                    freq_str = ""
                    for byte in reversed(freq_bcd):
                        freq_str += f"{(byte >> 4) & 0x0F}{byte & 0x0F}"
                    freq_hz = int(freq_str)
                    return freq_hz / 1000000.0
            return 145.5
        except:
            return 145.5

    def get_mode(self) -> Optional[str]:
        """Lit le mode"""
        try:
            response = self._send_ci_v_hamlib([0x04])
            if response and len(response) >= 15:
                ci_v_start = response.find(b'\xFE\xFE')
                if ci_v_start >= 0 and len(response) >= ci_v_start + 7:
                    mode_code = response[ci_v_start + 6]
                    for mode_name, code in self.MODES.items():
                        if code == mode_code:
                            return mode_name
            return "FM"
        except:
            return "FM"

    def get_status(self) -> Dict[str, Any]:
        """Récupère l'état"""
        return {
            "connected": self.connected,
            "logged_in": self.logged_in,
            "host": self.config.host,
            "control_port": self.config.control_port,
            "connection_type": "ICOM_Hamlib_Protocol"
        }
