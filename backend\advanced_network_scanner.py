#!/usr/bin/env python3
"""
Scanner réseau avancé pour IC-R8600
Détecte les services réseau disponibles et teste les protocoles
"""

import socket
import threading
import time
import struct
import subprocess
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Tuple, Optional

class ICOMNetworkScanner:
    """Scanner réseau avancé pour IC-R8600"""
    
    def __init__(self, target_ip: str = "**************"):
        self.target_ip = target_ip
        self.open_ports = []
        self.services = {}
        
        # Ports communs pour équipements ICOM
        self.common_ports = [
            50001, 50002, 50003, 50004, 50005,  # Ports ICOM standards
            8080, 8000, 8888, 80, 443,          # Ports web
            23, 22, 21,                         # Telnet, SSH, FTP
            4001, 4002, 4003,                   # Ports CI-V alternatifs
            7300, 7301, 7302,                   # Ports spécifiques ICOM
            1234, 5678, 9999                    # Ports génériques
        ]
    
    def ping_host(self) -> bool:
        """Test de connectivité de base"""
        print(f"🔍 Test de connectivité vers {self.target_ip}")
        
        try:
            if sys.platform.startswith('win'):
                result = subprocess.run(
                    ["ping", "-n", "1", "-w", "3000", self.target_ip],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
            else:
                result = subprocess.run(
                    ["ping", "-c", "1", "-W", "3", self.target_ip],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
            
            if result.returncode == 0:
                print(f"✅ {self.target_ip} est accessible")
                return True
            else:
                print(f"❌ {self.target_ip} n'est pas accessible")
                return False
                
        except Exception as e:
            print(f"❌ Erreur ping: {e}")
            return False
    
    def scan_port_tcp(self, port: int, timeout: float = 2.0) -> bool:
        """Scan d'un port TCP"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((self.target_ip, port))
            sock.close()
            return result == 0
        except:
            return False
    
    def scan_port_udp(self, port: int, timeout: float = 2.0) -> bool:
        """Scan d'un port UDP avec test CI-V"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(timeout)
            
            # Test avec commande CI-V de base
            ci_v_ping = bytes([0xFE, 0xFE, 0x96, 0xE0, 0xFD])
            sock.sendto(ci_v_ping, (self.target_ip, port))
            
            try:
                response, _ = sock.recvfrom(1024)
                sock.close()
                return len(response) > 0
            except socket.timeout:
                sock.close()
                return False
                
        except:
            return False
    
    def scan_ports_parallel(self) -> Dict[int, Dict[str, bool]]:
        """Scan parallèle des ports TCP et UDP"""
        print(f"🔍 Scan des ports sur {self.target_ip}")
        results = {}
        
        with ThreadPoolExecutor(max_workers=20) as executor:
            # Lancer les scans TCP et UDP en parallèle
            futures = {}
            
            for port in self.common_ports:
                tcp_future = executor.submit(self.scan_port_tcp, port, 1.0)
                udp_future = executor.submit(self.scan_port_udp, port, 1.0)
                futures[f"tcp_{port}"] = tcp_future
                futures[f"udp_{port}"] = udp_future
            
            # Collecter les résultats
            for future_name, future in futures.items():
                try:
                    protocol, port_str = future_name.split("_")
                    port = int(port_str)
                    
                    if port not in results:
                        results[port] = {"tcp": False, "udp": False}
                    
                    results[port][protocol] = future.result()
                    
                except Exception as e:
                    print(f"⚠️ Erreur scan {future_name}: {e}")
        
        return results
    
    def test_ci_v_commands(self, port: int, protocol: str = "udp") -> Dict[str, any]:
        """Test des commandes CI-V sur un port"""
        print(f"🧪 Test CI-V sur {protocol.upper()}:{port}")
        
        results = {
            "responsive": False,
            "commands_tested": [],
            "responses": {}
        }
        
        try:
            if protocol.lower() == "udp":
                sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            else:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.connect((self.target_ip, port))
            
            sock.settimeout(2.0)
            
            # Commandes CI-V de test
            test_commands = {
                "ping": [0xFE, 0xFE, 0x96, 0xE0, 0xFD],
                "get_frequency": [0xFE, 0xFE, 0x96, 0xE0, 0x03, 0xFD],
                "get_mode": [0xFE, 0xFE, 0x96, 0xE0, 0x04, 0xFD],
                "power_status": [0xFE, 0xFE, 0x96, 0xE0, 0x18, 0xFD]
            }
            
            for cmd_name, cmd_bytes in test_commands.items():
                try:
                    command = bytes(cmd_bytes)
                    
                    if protocol.lower() == "udp":
                        sock.sendto(command, (self.target_ip, port))
                    else:
                        sock.send(command)
                    
                    try:
                        if protocol.lower() == "udp":
                            response, _ = sock.recvfrom(1024)
                        else:
                            response = sock.recv(1024)
                        
                        if response:
                            results["responsive"] = True
                            results["commands_tested"].append(cmd_name)
                            results["responses"][cmd_name] = response.hex()
                            print(f"  ✅ {cmd_name}: {response.hex()}")
                        
                    except socket.timeout:
                        print(f"  ⏱️ {cmd_name}: timeout")
                    
                    time.sleep(0.1)  # Délai entre commandes
                    
                except Exception as e:
                    print(f"  ❌ {cmd_name}: {e}")
            
            sock.close()
            
        except Exception as e:
            print(f"❌ Erreur test CI-V: {e}")
        
        return results
    
    def test_web_interface(self, port: int) -> Dict[str, any]:
        """Test d'interface web sur un port"""
        print(f"🌐 Test interface web sur port {port}")
        
        try:
            import urllib.request
            import urllib.error
            
            url = f"http://{self.target_ip}:{port}"
            
            try:
                response = urllib.request.urlopen(url, timeout=3)
                content = response.read().decode('utf-8', errors='ignore')
                
                return {
                    "accessible": True,
                    "status_code": response.getcode(),
                    "content_length": len(content),
                    "has_icom_keywords": any(keyword in content.lower() 
                                           for keyword in ['icom', 'ic-r8600', 'radio', 'receiver'])
                }
                
            except urllib.error.URLError:
                return {"accessible": False}
                
        except Exception as e:
            print(f"  ❌ Erreur test web: {e}")
            return {"accessible": False, "error": str(e)}
    
    def test_telnet_interface(self, port: int = 23) -> Dict[str, any]:
        """Test d'interface Telnet"""
        print(f"📟 Test interface Telnet sur port {port}")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5.0)
            sock.connect((self.target_ip, port))
            
            # Lire le message de bienvenue
            welcome = sock.recv(1024)
            
            # Tester quelques commandes
            test_commands = ["help", "status", "version", "info"]
            responses = {}
            
            for cmd in test_commands:
                sock.send(f"{cmd}\r\n".encode())
                time.sleep(0.5)
                
                try:
                    response = sock.recv(2048)
                    responses[cmd] = response.decode('utf-8', errors='ignore')
                except:
                    responses[cmd] = None
            
            sock.close()
            
            return {
                "accessible": True,
                "welcome_message": welcome.decode('utf-8', errors='ignore'),
                "command_responses": responses
            }
            
        except Exception as e:
            return {"accessible": False, "error": str(e)}
    
    def full_scan(self) -> Dict[str, any]:
        """Scan complet de l'IC-R8600"""
        print("🚀 Démarrage du scan complet IC-R8600")
        print("=" * 50)
        
        results = {
            "target_ip": self.target_ip,
            "ping_success": False,
            "open_ports": {},
            "ci_v_services": {},
            "web_interfaces": {},
            "telnet_interface": {},
            "recommendations": []
        }
        
        # Test de connectivité
        results["ping_success"] = self.ping_host()
        
        if not results["ping_success"]:
            results["recommendations"].append("Vérifiez la connectivité réseau et l'adresse IP")
            return results
        
        # Scan des ports
        port_results = self.scan_ports_parallel()
        results["open_ports"] = port_results
        
        # Afficher les ports ouverts
        open_tcp_ports = [port for port, protocols in port_results.items() if protocols.get("tcp")]
        open_udp_ports = [port for port, protocols in port_results.items() if protocols.get("udp")]
        
        if open_tcp_ports:
            print(f"✅ Ports TCP ouverts: {open_tcp_ports}")
        if open_udp_ports:
            print(f"✅ Ports UDP réactifs: {open_udp_ports}")
        
        # Test des services CI-V
        for port in open_udp_ports + open_tcp_ports:
            if port in open_udp_ports:
                ci_v_result = self.test_ci_v_commands(port, "udp")
                if ci_v_result["responsive"]:
                    results["ci_v_services"][f"udp_{port}"] = ci_v_result
            
            if port in open_tcp_ports:
                ci_v_result = self.test_ci_v_commands(port, "tcp")
                if ci_v_result["responsive"]:
                    results["ci_v_services"][f"tcp_{port}"] = ci_v_result
        
        # Test des interfaces web
        web_ports = [port for port in open_tcp_ports if port in [80, 8080, 8000, 8888]]
        for port in web_ports:
            web_result = self.test_web_interface(port)
            if web_result["accessible"]:
                results["web_interfaces"][port] = web_result
        
        # Test Telnet
        if 23 in open_tcp_ports:
            telnet_result = self.test_telnet_interface()
            results["telnet_interface"] = telnet_result
        
        return results


def main():
    """Fonction principale"""
    print("Scanner Réseau Avancé IC-R8600")
    print("===============================")
    
    # Demander l'IP cible
    target_ip = input("Adresse IP de l'IC-R8600 (défaut: **************): ").strip()
    if not target_ip:
        target_ip = "**************"
    
    scanner = ICOMNetworkScanner(target_ip)
    results = scanner.full_scan()
    
    # Afficher le résumé
    print("\n" + "=" * 50)
    print("RÉSUMÉ DU SCAN")
    print("=" * 50)
    
    print(f"Cible: {results['target_ip']}")
    print(f"Ping: {'✅ Succès' if results['ping_success'] else '❌ Échec'}")
    
    if results["ci_v_services"]:
        print("\n🎯 SERVICES CI-V DÉTECTÉS:")
        for service, details in results["ci_v_services"].items():
            print(f"  - {service}: {len(details['commands_tested'])} commandes répondent")
    
    if results["web_interfaces"]:
        print("\n🌐 INTERFACES WEB DÉTECTÉES:")
        for port, details in results["web_interfaces"].items():
            print(f"  - Port {port}: {details['status_code']}")
    
    if results["telnet_interface"].get("accessible"):
        print("\n📟 INTERFACE TELNET DÉTECTÉE")
    
    # Recommandations
    print("\n💡 RECOMMANDATIONS:")
    if results["ci_v_services"]:
        for service in results["ci_v_services"]:
            protocol, port = service.split("_")
            print(f"  ✅ Utilisez {protocol.upper()}:{port} pour le contrôle CI-V")
    else:
        print("  ❌ Aucun service CI-V détecté")
        print("  🔧 Vérifiez la configuration 'Remote Control' sur l'IC-R8600")
    
    for rec in results["recommendations"]:
        print(f"  ⚠️ {rec}")


if __name__ == "__main__":
    main()
