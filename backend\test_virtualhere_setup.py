#!/usr/bin/env python3
"""
Test de configuration VirtualHere pour IC-R8600
Vérification complète de l'infrastructure USB distante
"""

import serial
import serial.tools.list_ports
import time
import subprocess
import socket
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VirtualHereTest:
    """Test complet de l'infrastructure VirtualHere"""
    
    def __init__(self):
        self.distant_ip = "************"
        self.local_ip = "*************"
        self.virtualhere_port = 7575
        self.icom_port = None
        
    def test_1_network_connectivity(self):
        """Test 1: Connectivité réseau vers PC distant"""
        print("🔍 TEST 1: CONNECTIVITÉ RÉSEAU")
        print("=" * 50)
        
        try:
            # Test ping
            result = subprocess.run(['ping', '-n', '2', self.distant_ip], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"✅ Ping vers {self.distant_ip}: SUCCÈS")
                
                # Extraire temps de réponse
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'temps=' in line or 'time=' in line:
                        print(f"   {line.strip()}")
                
                return True
            else:
                print(f"❌ Ping vers {self.distant_ip}: ÉCHEC")
                print("Vérifiez:")
                print("- PC distant allumé")
                print("- Réseau ************/24 accessible")
                return False
                
        except Exception as e:
            print(f"❌ Erreur ping: {e}")
            return False
    
    def test_2_virtualhere_server(self):
        """Test 2: VirtualHere Server sur PC distant"""
        print("\n🔍 TEST 2: VIRTUALHERE SERVER")
        print("=" * 50)
        
        try:
            # Test connexion TCP sur port VirtualHere
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5.0)
            result = sock.connect_ex((self.distant_ip, self.virtualhere_port))
            sock.close()
            
            if result == 0:
                print(f"✅ VirtualHere Server sur {self.distant_ip}:{self.virtualhere_port}: ACTIF")
                return True
            else:
                print(f"❌ VirtualHere Server sur {self.distant_ip}:{self.virtualhere_port}: INACTIF")
                print("Vérifiez:")
                print("- VirtualHere Server lancé sur PC distant")
                print("- Port 7575 ouvert dans pare-feu")
                print("- Version compatible Windows 7")
                return False
                
        except Exception as e:
            print(f"❌ Erreur test VirtualHere Server: {e}")
            return False
    
    def test_3_virtualhere_client(self):
        """Test 3: VirtualHere Client local"""
        print("\n🔍 TEST 3: VIRTUALHERE CLIENT")
        print("=" * 50)
        
        try:
            # Vérifier si VirtualHere Client est en cours
            result = subprocess.run(['tasklist', '/fi', 'imagename eq vhui64.exe'], 
                                  capture_output=True, text=True)
            
            if 'vhui64.exe' in result.stdout:
                print("✅ VirtualHere Client: EN COURS")
                return True
            else:
                print("❌ VirtualHere Client: NON LANCÉ")
                print("Actions:")
                print("1. Lancez VirtualHere Client (vhui64.exe)")
                print("2. Ajoutez serveur: File > Specify USB Servers > ************")
                print("3. Clic droit sur IC-R8600 > 'Use this device'")
                return False
                
        except Exception as e:
            print(f"❌ Erreur test VirtualHere Client: {e}")
            return False
    
    def test_4_com_ports_detection(self):
        """Test 4: Détection ports COM"""
        print("\n🔍 TEST 4: DÉTECTION PORTS COM")
        print("=" * 50)
        
        try:
            ports = serial.tools.list_ports.comports()
            
            print("Ports COM détectés:")
            icom_ports = []
            
            for port in ports:
                print(f"  {port.device}: {port.description}")
                
                # Recherche IC-R8600 / CP210x
                desc = port.description.upper()
                if any(keyword in desc for keyword in ['ICOM', 'IC-R8600', 'CP210', 'CP2102', 'SILICON LABS']):
                    icom_ports.append(port)
                    print(f"  ✅ IC-R8600 DÉTECTÉ: {port.device}")
                    self.icom_port = port.device
            
            if icom_ports:
                print(f"\n✅ {len(icom_ports)} port(s) IC-R8600 trouvé(s)")
                return True
            else:
                print("\n❌ Aucun port IC-R8600 détecté")
                print("Vérifiez:")
                print("- VirtualHere Client connecté au serveur")
                print("- IC-R8600 'Use this device' activé")
                print("- Drivers CP210x installés")
                return False
                
        except Exception as e:
            print(f"❌ Erreur détection ports: {e}")
            return False
    
    def test_5_serial_communication(self):
        """Test 5: Communication série CI-V"""
        print("\n🔍 TEST 5: COMMUNICATION SÉRIE CI-V")
        print("=" * 50)
        
        if not self.icom_port:
            print("❌ Aucun port IC-R8600 disponible")
            return False
        
        try:
            print(f"Test communication sur {self.icom_port}...")
            
            # Connexion série
            ser = serial.Serial(self.icom_port, 19200, timeout=3)
            print(f"✅ Connexion série établie")
            
            # Test commandes CI-V
            test_commands = [
                ("Status", [0xFE, 0xFE, 0x94, 0xE0, 0x19, 0x00, 0xFD]),
                ("Lecture fréquence", [0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD]),
            ]
            
            success_count = 0
            
            for cmd_name, cmd_bytes in test_commands:
                print(f"\nTest {cmd_name}:")
                print(f"  Envoi: {bytes(cmd_bytes).hex().upper()}")
                
                ser.reset_input_buffer()
                ser.write(bytes(cmd_bytes))
                time.sleep(0.5)
                
                response = ser.read(50)
                if response:
                    print(f"  ✅ Réponse: {response.hex().upper()}")
                    success_count += 1
                else:
                    print(f"  ⚠️  Pas de réponse")
            
            ser.close()
            
            if success_count > 0:
                print(f"\n✅ Communication CI-V: {success_count}/{len(test_commands)} commandes réussies")
                return True
            else:
                print(f"\n❌ Communication CI-V: Aucune réponse")
                print("Vérifiez:")
                print("- IC-R8600 allumé")
                print("- Câble USB connecté sur PC distant")
                print("- Configuration CI-V correcte")
                return False
                
        except Exception as e:
            print(f"❌ Erreur communication série: {e}")
            return False
    
    def test_6_frequency_control(self):
        """Test 6: Contrôle de fréquence"""
        print("\n🔍 TEST 6: CONTRÔLE FRÉQUENCE")
        print("=" * 50)
        print("⚠️  OBSERVEZ L'AFFICHAGE DE L'IC-R8600 !")
        
        if not self.icom_port:
            print("❌ Aucun port IC-R8600 disponible")
            return False
        
        try:
            ser = serial.Serial(self.icom_port, 19200, timeout=3)
            
            # Test fréquence 145.500 MHz
            freq_cmd = [0xFE, 0xFE, 0x94, 0xE0, 0x05, 0x00, 0x00, 0x50, 0x45, 0x01, 0xFD]
            print(f"Définition fréquence 145.500 MHz...")
            print(f"Commande: {bytes(freq_cmd).hex().upper()}")
            
            ser.write(bytes(freq_cmd))
            time.sleep(2)
            
            response = input("La fréquence 145.500 MHz s'affiche-t-elle sur l'IC-R8600 ? (o/n): ")
            
            ser.close()
            
            if response.lower() == 'o':
                print("✅ Contrôle fréquence: SUCCÈS")
                return True
            else:
                print("❌ Contrôle fréquence: ÉCHEC")
                return False
                
        except Exception as e:
            print(f"❌ Erreur contrôle fréquence: {e}")
            return False
    
    def run_complete_test(self):
        """Exécute tous les tests"""
        print("🎯 TEST COMPLET INFRASTRUCTURE VIRTUALHERE")
        print("=" * 60)
        print("Configuration:")
        print(f"- PC distant: {self.distant_ip}")
        print(f"- PC supervision: {self.local_ip}")
        print(f"- VirtualHere port: {self.virtualhere_port}")
        print("=" * 60)
        
        tests = [
            ("Connectivité réseau", self.test_1_network_connectivity),
            ("VirtualHere Server", self.test_2_virtualhere_server),
            ("VirtualHere Client", self.test_3_virtualhere_client),
            ("Détection ports COM", self.test_4_com_ports_detection),
            ("Communication série", self.test_5_serial_communication),
            ("Contrôle fréquence", self.test_6_frequency_control),
        ]
        
        results = []
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append((test_name, result))
                
                if not result:
                    print(f"\n⚠️  Test '{test_name}' échoué - Arrêt des tests")
                    break
                    
                time.sleep(1)
                
            except Exception as e:
                print(f"❌ Erreur test '{test_name}': {e}")
                results.append((test_name, False))
                break
        
        # Rapport final
        print("\n" + "=" * 60)
        print("📊 RAPPORT FINAL")
        print("=" * 60)
        
        success_count = sum(1 for _, result in results if result)
        total_tests = len(results)
        
        for test_name, result in results:
            status = "✅ SUCCÈS" if result else "❌ ÉCHEC"
            print(f"{status} {test_name}")
        
        print(f"\nRésultat: {success_count}/{total_tests} tests réussis")
        
        if success_count == total_tests:
            print("\n🎉 INFRASTRUCTURE VIRTUALHERE OPÉRATIONNELLE !")
            print("Vous pouvez maintenant lancer l'application FastAPI")
            print("Port IC-R8600 détecté:", self.icom_port)
        else:
            print("\n⚠️  INFRASTRUCTURE INCOMPLÈTE")
            print("Corrigez les problèmes avant de continuer")
        
        return success_count == total_tests

def main():
    """Test principal"""
    tester = VirtualHereTest()
    tester.run_complete_test()
    
    input("\nAppuyez sur Entrée pour quitter...")

if __name__ == "__main__":
    main()
