#!/usr/bin/env python3
"""
Serveur IC-R8600 corrigé avec les nouvelles fonctionnalités
Version sans blocage USB
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
import logging
from ic_r8600_usb_remote import ICR8600USBRemote

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('backend.log')
    ]
)
logger = logging.getLogger(__name__)

app = FastAPI(title="IC-R8600 Controller")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Models
class CommandRequest(BaseModel):
    frequency: float = None
    mode: str = None
    power: bool = None
    rf_gain: int = None

class ScanRequest(BaseModel):
    start_frequency: int
    end_frequency: int
    step: int = 25000

class CommandResponse(BaseModel):
    success: bool
    message: str = ""
    simulation_mode: bool = False

# Variables globales
usb_handler = None
connection_status = {
    "connected": False,
    "usb_remote": False,
    "active_connection": None,
    "frequency": 145500000,
    "mode": "FM",
    "power_on": True,
    "volume": 128,
    "squelch": 50,
    "rf_gain": 128
}

@app.on_event("startup")
async def startup_event():
    """Initialisation au démarrage"""
    global usb_handler, connection_status

    logger.info("Démarrage du contrôleur IC-R8600")

    # Initialisation sans connexion USB pour éviter le blocage
    logger.info("Initialisation en mode déconnecté")
    logger.info("Utilisez /api/connect pour établir la connexion USB")

    connection_status.update({
        "connected": False,
        "usb_remote": False,
        "active_connection": None
    })

    logger.info("✅ Serveur API prêt sur http://localhost:8001")

@app.get("/api/status")
async def get_status():
    """Statut du récepteur"""
    return connection_status

@app.post("/api/connect")
async def connect_usb():
    """Connexion manuelle à l'IC-R8600"""
    global usb_handler, connection_status

    try:
        logger.info("Tentative de connexion USB manuelle...")
        usb_handler = ICR8600USBRemote()

        if usb_handler.connect():
            logger.info("✅ Connexion USB établie")
            connection_status.update({
                "connected": True,
                "usb_remote": True,
                "active_connection": "usb_remote"
            })
            return CommandResponse(
                success=True,
                message="Connexion USB établie avec l'IC-R8600"
            )
        else:
            logger.warning("❌ Connexion USB échouée")
            return CommandResponse(
                success=False,
                message="Impossible de se connecter à l'IC-R8600"
            )
    except Exception as e:
        logger.error(f"Erreur connexion USB: {e}")
        return CommandResponse(
            success=False,
            message=f"Erreur connexion: {str(e)}"
        )

@app.post("/api/command")
async def send_command(command: CommandRequest):
    """Envoi de commande au récepteur"""
    global usb_handler, connection_status
    
    if not connection_status["connected"]:
        return CommandResponse(
            success=False,
            message="Aucune connexion active avec l'IC-R8600"
        )
    
    results = []
    
    try:
        if command.frequency:
            # Conversion MHz vers Hz
            freq_hz = int(command.frequency * 1000000)
            if usb_handler and usb_handler.set_frequency(freq_hz):
                connection_status["frequency"] = freq_hz
                results.append(f"Fréquence: {command.frequency} MHz")
            else:
                results.append(f"Fréquence: {command.frequency} MHz (simulé)")
        
        if command.mode:
            if usb_handler and usb_handler.set_mode(command.mode):
                connection_status["mode"] = command.mode
                results.append(f"Mode: {command.mode}")
            else:
                connection_status["mode"] = command.mode
                results.append(f"Mode: {command.mode} (simulé)")
        
        if command.power is not None:
            connection_status["power_on"] = command.power
            results.append(f"Alimentation: {'ON' if command.power else 'OFF'}")
        
        if command.rf_gain is not None:
            connection_status["rf_gain"] = command.rf_gain
            results.append(f"RF Gain: {command.rf_gain}")
        
        if results:
            return CommandResponse(
                success=True,
                message=" | ".join(results),
                simulation_mode=not connection_status["connected"]
            )
        
        return CommandResponse(
            success=False,
            message="Aucune commande valide"
        )
        
    except Exception as e:
        logger.error(f"Erreur commande: {e}")
        return CommandResponse(
            success=False,
            message=f"Erreur: {str(e)}"
        )

@app.post("/api/scan/start")
async def start_scan(scan_request: ScanRequest):
    """Démarre un scan de fréquences"""
    try:
        if not connection_status["connected"]:
            return CommandResponse(
                success=False,
                message="Aucune connexion active"
            )
        
        # Validation des fréquences
        if scan_request.start_frequency >= scan_request.end_frequency:
            raise HTTPException(status_code=400, detail="Fréquence de début doit être inférieure à la fréquence de fin")
        
        # Calcul du pas de scan
        step = getattr(scan_request, 'step', 25000)
        
        logger.info(f"Démarrage scan: {scan_request.start_frequency} -> {scan_request.end_frequency}, pas: {step}")
        
        return CommandResponse(
            success=True,
            message=f"Scan démarré de {scan_request.start_frequency/1000000:.3f} MHz à {scan_request.end_frequency/1000000:.3f} MHz",
            simulation_mode=not connection_status["connected"]
        )
        
    except Exception as e:
        logger.error(f"Erreur démarrage scan: {e}")
        return CommandResponse(
            success=False,
            message=f"Erreur démarrage scan: {str(e)}"
        )

@app.post("/api/scan/stop")
async def stop_scan():
    """Arrête le scan en cours"""
    try:
        logger.info("Arrêt du scan demandé")
        return CommandResponse(
            success=True,
            message="Scan arrêté",
            simulation_mode=not connection_status["connected"]
        )
    except Exception as e:
        logger.error(f"Erreur arrêt scan: {e}")
        return CommandResponse(
            success=False,
            message=f"Erreur arrêt scan: {str(e)}"
        )

@app.get("/api/recordings")
async def get_recordings():
    """Liste des enregistrements"""
    return {
        "recordings": [
            {
                "filename": "icom_r8600_AF_20250717_151000.wav",
                "date": "2025-07-17 15:10:00",
                "duration": "00:03:25",
                "frequency": "145.500 MHz",
                "mode": "FM"
            }
        ]
    }

@app.post("/api/audio/start")
async def start_recording():
    """Démarrage enregistrement"""
    return CommandResponse(
        success=True,
        message="Enregistrement démarré",
        simulation_mode=not connection_status["connected"]
    )

@app.post("/api/audio/stop")
async def stop_recording():
    """Arrêt enregistrement"""
    return CommandResponse(
        success=True,
        message="Enregistrement arrêté",
        simulation_mode=not connection_status["connected"]
    )

if __name__ == "__main__":
    print("🚀 Serveur IC-R8600 corrigé")
    print("📡 http://localhost:8001")
    print("🔧 Avec support USB réel + interface corrigée")
    uvicorn.run(app, host="0.0.0.0", port=8001)
