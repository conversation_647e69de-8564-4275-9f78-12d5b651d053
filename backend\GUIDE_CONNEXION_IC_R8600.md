# 🔌 Guide de Connexion IC-R8600 via Câble RJ45

## ✅ **Application Configurée pour Contrôle Réseau UDP**

Votre application est maintenant configurée pour fonctionner **exclusivement via câble RJ45** avec encapsulation des commandes CI-V dans UDP. Compatible avec la configuration Network Control de l'IC-R8600.

---

## 📋 **Étapes de Configuration**

### **1. Configuration Physique**

- ✅ **IC-R8600 allumé**
- ✅ **Câble RJ45 connecté** entre PC et IC-R8600
- ✅ **Même réseau** (PC et IC-R8600 sur le même segment réseau)

### **2. Configuration Réseau IC-R8600**

```
MENU → SET → Connectors → Network
├── IP Address: ************00
├── Subnet Mask: *************
├── Gateway: ************
├── Control Port UDP: 50001 ← Port de contrôle
├── Serial Port UDP: 50002  ← Port série
├── Audio Port UDP: 50003   ← Port audio
└── Network Control: ON ← IMPORTANT !
```

### **3. Redémarrage Obligatoire**

⚠️ **Redémarrez l'IC-R8600** après avoir activé Network Control

---

## 🧪 **Test de Connectivité**

### **Test Rapide**

```bash
cd backend
python test_tcp_connection.py
```

### **Résultats Attendus**

```
✅ Ping réseau: OK
✅ Connexion CI-V UDP: OK
🎯 Network Control fonctionnel
```

---

## 🚀 **Lancement de l'Application**

### **Commande**

```bash
cd backend
python lancer_application.py
```

### **Séquence de Démarrage Normale**

```
🌐 Connexion UDP vers IC-R8600 via câble RJ45...
🔌 Tentative de connexion UDP...
✅ IC-R8600 accessible sur ************00
✅ Sockets UDP créés
✅ Network Control IC-R8600 activé
🎉 Application démarrée sur http://localhost:8000
```

---

## ❌ **Résolution des Problèmes**

### **Problème: "IC-R8600 non accessible"**

**Solutions:**

1. Vérifiez que l'IC-R8600 est allumé
2. Vérifiez le câble RJ45
3. Vérifiez l'adresse IP dans les menus de l'IC-R8600
4. Testez avec `ping ************00`

### **Problème: "Network Control non activé"**

**Solutions:**

1. Activez Network Control dans les menus
2. Redémarrez l'IC-R8600
3. Vérifiez la configuration des ports UDP

### **Problème: "Timeout UDP"**

**Solutions:**

1. Network Control pas encore activé
2. Ports UDP mal configurés
3. Firewall bloquant les paquets UDP

---

## 🔧 **Configuration Réseau PC**

### **Vérification IP PC**

```bash
ipconfig
```

### **Configuration Recommandée**

- **IP PC**: 192.168.37.x (ex: ************0)
- **Masque**: *************
- **Passerelle**: ************

---

## 📊 **Fonctionnalités Disponibles**

### **Contrôle CI-V via TCP/IP**

- ✅ Changement de fréquence
- ✅ Changement de mode
- ✅ Contrôle du volume
- ✅ Lecture du statut
- ✅ Enregistrement audio
- ✅ Scan automatique

### **Interface Web**

- **Frontend**: http://localhost:5173
- **API Backend**: http://localhost:8000
- **Documentation API**: http://localhost:8000/docs

---

## 🎯 **Points Importants**

1. **Pas de mode simulation** - L'application refuse de démarrer sans connexion réseau
2. **TCP uniquement** - Pas d'UDP, connexion directe TCP sur port 50001
3. **Activation automatique** - Le récepteur bascule en remote control automatiquement
4. **Encapsulation CI-V** - Commandes CI-V encapsulées dans TCP/IP
5. **Contrôle exclusif** - Une seule application peut contrôler l'IC-R8600 à la fois

---

## 📞 **Support**

Si vous rencontrez des problèmes :

1. Exécutez `python test_tcp_connection.py` pour diagnostiquer
2. Vérifiez les logs de l'application
3. Consultez la documentation ICOM pour la configuration réseau

**L'application est maintenant optimisée pour le contrôle réseau professionnel de l'IC-R8600 !** 🎉
