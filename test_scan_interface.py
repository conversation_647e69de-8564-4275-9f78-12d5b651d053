#!/usr/bin/env python3
"""
Test des nouvelles fonctionnalités de l'interface
"""
import requests
import json

API_BASE = "http://localhost:8001"

def test_scan():
    """Test du scan de fréquences"""
    print("🔍 TEST SCAN DE FRÉQUENCES")
    print("=" * 50)
    
    # Test démarrage scan
    scan_data = {
        "start_frequency": 144000000,  # 144 MHz
        "end_frequency": 146000000,    # 146 MHz
        "step": 25000                  # 25 kHz
    }
    
    try:
        response = requests.post(f"{API_BASE}/api/scan/start", json=scan_data)
        print(f"Status: {response.status_code}")
        result = response.json()
        print(f"✅ Démarrage scan: {result}")
        
        # Test arrêt scan
        response = requests.post(f"{API_BASE}/api/scan/stop")
        result = response.json()
        print(f"✅ Arrêt scan: {result}")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_mode_change():
    """Test du changement de mode"""
    print("\n🔍 TEST CHANGEMENT MODE")
    print("=" * 50)
    
    modes = ['FM', 'AM', 'USB', 'LSB']
    
    for mode in modes:
        try:
            response = requests.post(f"{API_BASE}/api/command", json={"mode": mode})
            result = response.json()
            print(f"✅ Mode {mode}: {result.get('success', False)}")
        except Exception as e:
            print(f"❌ Erreur mode {mode}: {e}")

if __name__ == "__main__":
    print("🎯 TEST NOUVELLES FONCTIONNALITÉS")
    print("=" * 50)
    
    test_scan()
    test_mode_change()
    
    print("\n🎉 Tests terminés !")
