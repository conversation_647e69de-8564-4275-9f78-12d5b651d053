#!/usr/bin/env python3
"""
Application simplifiée IC-R8600 VirtualHere
Démarrage rapide pour test de commande
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import serial
import time
import logging

# Configuration logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Application FastAPI
app = FastAPI(title="IC-R8600 VirtualHere Simple", version="1.0.0")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Modèles
class FrequencyRequest(BaseModel):
    frequency_mhz: float

class ModeRequest(BaseModel):
    mode: str

class CommandRequest(BaseModel):
    command: str = None
    parameters: dict = {}
    # Champs optionnels pour compatibilité frontend
    frequency: float = None
    mode: str = None
    rf_gain: int = None
    power_on: bool = None
    volume: int = None
    squelch: int = None

# Variables globales
serial_connection = None
current_status = {
    "connected": False,
    "port": "COM6",
    "frequency": 145500000,
    "mode": "FM",
    "power_on": True,
    "volume": 128,
    "squelch": 50
}

def connect_ic_r8600():
    """Connexion simple à l'IC-R8600"""
    global serial_connection, current_status
    
    try:
        if serial_connection:
            serial_connection.close()
        
        logger.info("Connexion IC-R8600 sur COM6...")
        serial_connection = serial.Serial('COM6', 19200, timeout=1)
        current_status["connected"] = True
        logger.info("✅ IC-R8600 connecté sur COM6")
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur connexion: {e}")
        current_status["connected"] = False
        return False

def send_ci_v_command(command_bytes):
    """Envoie une commande CI-V"""
    global serial_connection
    
    if not serial_connection or not current_status["connected"]:
        if not connect_ic_r8600():
            return False
    
    try:
        serial_connection.write(command_bytes)
        time.sleep(0.3)
        response = serial_connection.read(50)
        logger.info(f"Commande: {command_bytes.hex().upper()}")
        if response:
            logger.info(f"Réponse: {response.hex().upper()}")
        return True
        
    except Exception as e:
        logger.error(f"Erreur commande: {e}")
        current_status["connected"] = False
        return False

# Endpoints
@app.get("/")
async def root():
    return {
        "message": "IC-R8600 VirtualHere Simple",
        "status": "running",
        "port": "COM7"
    }

@app.get("/api/status")
async def get_status():
    """État du système"""
    return {
        "connected": current_status["connected"],
        "port": current_status["port"],
        "frequency": current_status["frequency"],
        "mode": current_status["mode"],
        "power_on": current_status["power_on"],
        "volume": current_status["volume"],
        "squelch": current_status["squelch"],
        "virtualhere": True,
        "connection_type": "usb_remote_virtualhere"
    }

@app.post("/api/connect")
async def connect():
    """Connexion manuelle"""
    success = connect_ic_r8600()
    return {
        "success": success,
        "message": "Connecté" if success else "Échec connexion"
    }

@app.post("/api/set_frequency")
async def set_frequency(request: FrequencyRequest):
    """Définir la fréquence"""
    try:
        freq_hz = int(request.frequency_mhz * 1000000)
        
        # Conversion BCD pour IC-R8600
        freq_str = f"{freq_hz:010d}"
        bcd_bytes = []
        for i in range(4, -1, -1):
            digit1 = int(freq_str[i*2])
            digit2 = int(freq_str[i*2 + 1])
            bcd_bytes.append((digit1 << 4) | digit2)

        # Commande CI-V avec adresse locale 94h (qui fonctionne !)
        command = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x05] + bcd_bytes + [0xFD])
        
        if send_ci_v_command(command):
            current_status["frequency"] = freq_hz
            logger.info(f"✅ Fréquence définie: {request.frequency_mhz} MHz")
            return {
                "success": True,
                "frequency_mhz": request.frequency_mhz,
                "frequency_hz": freq_hz
            }
        else:
            raise HTTPException(status_code=500, detail="Échec définition fréquence")
            
    except Exception as e:
        logger.error(f"Erreur set_frequency: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/set_mode")
async def set_mode(request: ModeRequest):
    """Définir le mode"""
    modes = {
        'LSB': 0x00, 'USB': 0x01, 'AM': 0x02, 'CW': 0x03,
        'FM': 0x05, 'WFM': 0x06, 'CWR': 0x07, 'RTTY': 0x08
    }
    
    if request.mode not in modes:
        raise HTTPException(status_code=400, detail="Mode invalide")
    
    try:
        # Commande CI-V avec adresse locale 94h
        command = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x06, modes[request.mode], 0xFD])
        
        if send_ci_v_command(command):
            current_status["mode"] = request.mode
            logger.info(f"✅ Mode défini: {request.mode}")
            return {
                "success": True,
                "mode": request.mode
            }
        else:
            raise HTTPException(status_code=500, detail="Échec définition mode")
            
    except Exception as e:
        logger.error(f"Erreur set_mode: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/test_command")
async def test_command():
    """Test de commande simple"""
    try:
        # Commande de test (lecture fréquence) avec adresse locale 94h
        command = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
        
        if send_ci_v_command(command):
            return {
                "success": True,
                "message": "Commande test envoyée",
                "command": command.hex().upper()
            }
        else:
            return {
                "success": False,
                "message": "Échec commande test"
            }
            
    except Exception as e:
        logger.error(f"Erreur test_command: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/command")
async def send_command(request: CommandRequest):
    """Endpoint générique pour les commandes"""
    try:
        # Gestion des commandes basées sur les champs présents
        if request.frequency is not None:
            # Commande de fréquence
            result = await set_frequency(FrequencyRequest(frequency_mhz=request.frequency))
            return result
        elif request.mode is not None:
            # Commande de mode
            result = await set_mode(ModeRequest(mode=request.mode))
            return result
        elif request.power_on is not None:
            # Commande power RÉELLE
            if request.power_on:
                logger.info("Commande Power ON réelle")
                power_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x01, 0xFD])
                if send_ci_v_command(power_cmd):
                    current_status["power_on"] = True
                    return {"success": True, "message": "Power ON envoyé à l'IC-R8600"}
                else:
                    return {"success": False, "message": "Échec Power ON"}
            else:
                logger.info("Commande Power OFF réelle")
                power_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x00, 0xFD])
                if send_ci_v_command(power_cmd):
                    current_status["power_on"] = False
                    return {"success": True, "message": "Power OFF envoyé à l'IC-R8600"}
                else:
                    return {"success": False, "message": "Échec Power OFF"}
        elif request.command:
            # Commandes avec format command/parameters
            if request.command == "set_frequency":
                freq_mhz = request.parameters.get("frequency", 145.5)
                return await set_frequency(FrequencyRequest(frequency_mhz=freq_mhz))
            elif request.command == "set_mode":
                mode = request.parameters.get("mode", "FM")
                return await set_mode(ModeRequest(mode=mode))
            elif request.command == "get_status":
                return await get_status()
            else:
                return {"success": False, "message": f"Commande inconnue: {request.command}"}
        else:
            return {"success": False, "message": "Aucune commande spécifiée"}

    except Exception as e:
        logger.error(f"Erreur send_command: {e}")
        return {"success": False, "message": f"Erreur: {str(e)}"}

@app.get("/api/recordings")
async def get_recordings():
    """Liste des enregistrements (simulé)"""
    # Retourner directement un tableau pour compatibilité frontend
    return []

@app.post("/api/recordings/start")
async def start_recording():
    """Démarrer un enregistrement (simulé)"""
    return {
        "success": False,
        "message": "Enregistrement non implémenté dans la version simple"
    }

@app.post("/api/recordings/stop")
async def stop_recording():
    """Arrêter un enregistrement (simulé)"""
    return {
        "success": False,
        "message": "Enregistrement non implémenté dans la version simple"
    }

@app.post("/api/power_on")
async def power_on():
    """Power ON réel de l'IC-R8600"""
    try:
        logger.info("Power ON IC-R8600")
        power_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x01, 0xFD])

        if send_ci_v_command(power_cmd):
            current_status["power_on"] = True
            return {"success": True, "message": "IC-R8600 Power ON"}
        else:
            return {"success": False, "message": "Échec Power ON"}

    except Exception as e:
        logger.error(f"Erreur power_on: {e}")
        return {"success": False, "message": f"Erreur: {str(e)}"}

@app.post("/api/power_off")
async def power_off():
    """Power OFF réel de l'IC-R8600"""
    try:
        logger.info("Power OFF IC-R8600")
        power_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x00, 0xFD])

        if send_ci_v_command(power_cmd):
            current_status["power_on"] = False
            return {"success": True, "message": "IC-R8600 Power OFF"}
        else:
            return {"success": False, "message": "Échec Power OFF"}

    except Exception as e:
        logger.error(f"Erreur power_off: {e}")
        return {"success": False, "message": f"Erreur: {str(e)}"}

@app.post("/api/set_volume")
async def set_volume(volume: int):
    """Réglage volume réel"""
    try:
        if not (0 <= volume <= 255):
            return {"success": False, "message": "Volume doit être entre 0 et 255"}

        logger.info(f"Volume IC-R8600: {volume}")
        # Commande volume CI-V (0x14 0x01)
        vol_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x14, 0x01, volume, 0xFD])

        if send_ci_v_command(vol_cmd):
            return {"success": True, "message": f"Volume défini: {volume}"}
        else:
            return {"success": False, "message": "Échec réglage volume"}

    except Exception as e:
        logger.error(f"Erreur set_volume: {e}")
        return {"success": False, "message": f"Erreur: {str(e)}"}

@app.post("/api/set_squelch")
async def set_squelch(squelch: int):
    """Réglage squelch réel"""
    try:
        if not (0 <= squelch <= 255):
            return {"success": False, "message": "Squelch doit être entre 0 et 255"}

        logger.info(f"Squelch IC-R8600: {squelch}")
        # Commande squelch CI-V (0x14 0x03)
        sql_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x14, 0x03, squelch, 0xFD])

        if send_ci_v_command(sql_cmd):
            return {"success": True, "message": f"Squelch défini: {squelch}"}
        else:
            return {"success": False, "message": "Échec réglage squelch"}

    except Exception as e:
        logger.error(f"Erreur set_squelch: {e}")
        return {"success": False, "message": f"Erreur: {str(e)}"}

@app.get("/api/get_signal_meter")
async def get_signal_meter():
    """Lecture S-meter réel"""
    try:
        logger.info("Lecture S-meter IC-R8600")
        # Commande lecture S-meter CI-V (0x15 0x02)
        meter_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x15, 0x02, 0xFD])

        if send_ci_v_command(meter_cmd):
            # Note: Dans une vraie implémentation, il faudrait lire la réponse
            return {"success": True, "s_meter": 5, "message": "S-meter lu"}
        else:
            return {"success": False, "message": "Échec lecture S-meter"}

    except Exception as e:
        logger.error(f"Erreur get_signal_meter: {e}")
        return {"success": False, "message": f"Erreur: {str(e)}"}

# Démarrage
@app.on_event("startup")
async def startup():
    logger.info("🚀 Démarrage IC-R8600 VirtualHere Simple")
    logger.info("Port: COM6 (VirtualHere)")
    
    # Tentative de connexion automatique
    if connect_ic_r8600():
        logger.info("✅ Connexion automatique réussie")
    else:
        logger.warning("⚠️ Connexion automatique échouée")

@app.on_event("shutdown")
async def shutdown():
    global serial_connection
    if serial_connection:
        serial_connection.close()
    logger.info("🛑 Application arrêtée")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
