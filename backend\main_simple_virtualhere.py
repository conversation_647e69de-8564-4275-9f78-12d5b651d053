#!/usr/bin/env python3
"""
Application simplifiée IC-R8600 VirtualHere
Démarrage rapide pour test de commande
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import serial
import time
import logging

# Configuration logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Application FastAPI
app = FastAPI(title="IC-R8600 VirtualHere Simple", version="1.0.0")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Modèles
class FrequencyRequest(BaseModel):
    frequency_mhz: float

class ModeRequest(BaseModel):
    mode: str

class CommandRequest(BaseModel):
    command: str = None
    parameters: dict = {}
    # Champs optionnels pour compatibilité frontend
    frequency: float = None
    mode: str = None
    rf_gain: int = None
    power_on: bool = None
    volume: int = None
    squelch: int = None

# Variables globales
serial_connection = None
current_status = {
    "connected": False,
    "port": "AUTO",
    "connection_mode": "AUTO",
    "frequency": 145500000,
    "mode": "FM",
    "power_on": True,
    "volume": 128,
    "squelch": 50
}

def scan_available_ports():
    """Scanne tous les ports COM disponibles"""
    import serial.tools.list_ports

    available_ports = []
    for port in serial.tools.list_ports.comports():
        available_ports.append({
            "device": port.device,
            "description": port.description,
            "hwid": port.hwid,
            "is_icom_candidate": any(keyword in port.description.upper()
                                   for keyword in ['ICOM', 'IC-R8600', 'CP210', 'SILICON LABS', 'USB SERIAL'])
        })

    return available_ports

def test_port_for_ic_r8600(port_name):
    """Test si un port spécifique contient l'IC-R8600"""
    try:
        ser = serial.Serial(port_name, 19200, timeout=1)
        # Test commande CI-V (lecture fréquence)
        cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
        ser.reset_input_buffer()
        ser.write(cmd)
        time.sleep(0.3)
        response = ser.read(20)
        ser.close()

        if response and len(response) > 6:
            logger.info(f"✅ IC-R8600 confirmé sur {port_name}")
            return True
        else:
            return False
    except Exception as e:
        logger.debug(f"Port {port_name} non accessible: {e}")
        return False

def find_ic_r8600_port_universal():
    """Détection universelle IC-R8600 - teste TOUS les ports COM disponibles"""
    logger.info("🔍 DÉTECTION UNIVERSELLE IC-R8600")
    logger.info("=" * 50)

    available_ports = scan_available_ports()
    logger.info(f"📋 Ports COM détectés: {[p['device'] for p in available_ports]}")

    if not available_ports:
        logger.error("❌ Aucun port COM disponible")
        return None, None

    # Test TOUS les ports disponibles (pas de priorité)
    ic_r8600_candidates = []

    for port_info in available_ports:
        port_name = port_info['device']
        logger.info(f"🔍 Test {port_name}: {port_info['description']}")

        if test_port_for_ic_r8600(port_name):
            # Déterminer le type de connexion par analyse
            connection_type = analyze_connection_type(port_info)

            ic_r8600_candidates.append({
                'port': port_name,
                'type': connection_type,
                'description': port_info['description'],
                'hwid': port_info['hwid']
            })

            logger.info(f"✅ IC-R8600 TROUVÉ sur {port_name} ({connection_type})")

    if not ic_r8600_candidates:
        logger.warning("❌ IC-R8600 non trouvé sur aucun port")
        return None, None

    # Si plusieurs candidats, prendre le premier (ou implémenter logique de choix)
    if len(ic_r8600_candidates) > 1:
        logger.warning(f"⚠️ Plusieurs IC-R8600 détectés: {[c['port'] for c in ic_r8600_candidates]}")
        logger.info("Utilisation du premier trouvé")

    selected = ic_r8600_candidates[0]
    logger.info(f"🎯 IC-R8600 sélectionné: {selected['port']} ({selected['type']})")

    return selected['port'], selected['type']

def analyze_connection_type(port_info):
    """Analyse le type de connexion basé sur les informations du port"""
    desc = port_info['description'].upper()
    hwid = port_info['hwid'].upper()
    port_name = port_info['device']

    # Analyse par description
    if 'CP210' in desc or 'SILICON LABS' in desc:
        # CP210x peut être VirtualHere ou USB direct
        if 'USB VID:PID=10C4:EA60' in hwid:
            # Analyse par numéro de port (heuristique)
            if port_name in ['COM6', 'COM7', 'COM8', 'COM9']:
                return "VirtualHere_CP210x"
            else:
                return "USB_Direct_CP210x"
        return "CP210x_Unknown"

    elif 'ICOM' in desc or 'IC-R8600' in desc:
        return "USB_Direct_ICOM"

    elif 'USB SERIAL' in desc:
        return "USB_Direct_Generic"

    elif 'FTDI' in desc:
        return "USB_Direct_FTDI"

    else:
        # Analyse par numéro de port comme fallback
        if port_name in ['COM6', 'COM7', 'COM8', 'COM9']:
            return "Probable_VirtualHere"
        elif port_name in ['COM3', 'COM4', 'COM5']:
            return "Probable_USB_Direct"
        else:
            return "Unknown_Type"

def connect_ic_r8600():
    """Connexion dynamique à l'IC-R8600"""
    global serial_connection, current_status

    try:
        if serial_connection:
            serial_connection.close()
            serial_connection = None

        # Détection universelle du port
        port, mode = find_ic_r8600_port_universal()
        if not port:
            logger.error("❌ Aucun port IC-R8600 trouvé")
            current_status["connected"] = False
            current_status["port"] = "NONE"
            current_status["connection_mode"] = "NONE"
            return False

        logger.info(f"🔌 Connexion IC-R8600 sur {port} (Mode: {mode})...")
        serial_connection = serial.Serial(port, 19200, timeout=1)

        # Mise à jour du statut
        current_status["connected"] = True
        current_status["port"] = port
        current_status["connection_mode"] = mode

        logger.info(f"✅ IC-R8600 connecté sur {port} en mode {mode}")
        return True

    except Exception as e:
        logger.error(f"❌ Erreur connexion: {e}")
        current_status["connected"] = False
        current_status["port"] = "ERROR"
        current_status["connection_mode"] = "ERROR"
        return False

def reconnect_if_needed():
    """Reconnexion automatique si nécessaire"""
    global serial_connection, current_status

    if not current_status["connected"] or not serial_connection:
        logger.info("🔄 Tentative de reconnexion automatique...")
        return connect_ic_r8600()

    # Test de la connexion existante
    try:
        cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
        serial_connection.reset_input_buffer()
        serial_connection.write(cmd)
        time.sleep(0.3)
        response = serial_connection.read(20)

        if response:
            return True  # Connexion OK
        else:
            logger.warning("⚠️ Connexion perdue, reconnexion...")
            return connect_ic_r8600()

    except Exception as e:
        logger.warning(f"⚠️ Erreur connexion, reconnexion: {e}")
        return connect_ic_r8600()

def send_ci_v_command(command_bytes):
    """Envoie une commande CI-V avec reconnexion automatique"""
    global serial_connection

    # Vérifier/rétablir la connexion
    if not reconnect_if_needed():
        logger.error("❌ Impossible de se connecter à l'IC-R8600")
        return False

    try:
        serial_connection.reset_input_buffer()
        serial_connection.write(command_bytes)
        time.sleep(0.3)
        response = serial_connection.read(50)

        logger.info(f"📤 Commande: {command_bytes.hex().upper()}")
        if response:
            logger.info(f"📥 Réponse: {response.hex().upper()}")

        return True

    except Exception as e:
        logger.error(f"❌ Erreur commande: {e}")
        current_status["connected"] = False
        # Tentative de reconnexion immédiate
        if reconnect_if_needed():
            try:
                serial_connection.write(command_bytes)
                time.sleep(0.3)
                response = serial_connection.read(50)
                logger.info(f"🔄 Commande après reconnexion: {command_bytes.hex().upper()}")
                return True
            except:
                pass

        return False

# Endpoints
@app.get("/")
async def root():
    return {
        "message": "IC-R8600 VirtualHere Simple",
        "status": "running",
        "port": "COM7"
    }

@app.get("/api/status")
async def get_status():
    """État du système"""
    return {
        "connected": current_status["connected"],
        "port": current_status["port"],
        "connection_mode": current_status.get("connection_mode", "AUTO"),
        "frequency": current_status["frequency"],
        "mode": current_status["mode"],
        "power_on": current_status["power_on"],
        "volume": current_status["volume"],
        "squelch": current_status["squelch"],
        "virtualhere": current_status.get("connection_mode") == "VirtualHere",
        "usb_direct": current_status.get("connection_mode") == "USB_Direct",
        "connection_type": current_status.get("connection_mode", "AUTO")
    }

@app.post("/api/connect")
async def connect():
    """Connexion manuelle avec scan dynamique"""
    success = connect_ic_r8600()
    return {
        "success": success,
        "port": current_status["port"],
        "connection_mode": current_status["connection_mode"],
        "message": f"Connecté sur {current_status['port']} ({current_status['connection_mode']})" if success else "Échec connexion"
    }

@app.get("/api/scan_ports")
async def scan_ports():
    """Scan universel de tous les ports COM avec test IC-R8600"""
    try:
        logger.info("🔍 Démarrage scan universel des ports...")
        available_ports = scan_available_ports()

        # Test détaillé de chaque port
        detailed_ports = []
        ic_r8600_found = []

        for port_info in available_ports:
            port_name = port_info["device"]
            logger.info(f"Test {port_name}...")

            # Test IC-R8600
            is_ic_r8600 = test_port_for_ic_r8600(port_name)
            connection_type = analyze_connection_type(port_info) if is_ic_r8600 else "N/A"

            port_detail = {
                **port_info,
                "is_ic_r8600": is_ic_r8600,
                "connection_type": connection_type,
                "status": "🎯 IC-R8600 DÉTECTÉ" if is_ic_r8600 else "📍 Disponible",
                "priority": 1 if is_ic_r8600 else 2
            }

            detailed_ports.append(port_detail)

            if is_ic_r8600:
                ic_r8600_found.append({
                    "port": port_name,
                    "type": connection_type,
                    "description": port_info["description"]
                })

        # Trier par priorité (IC-R8600 en premier)
        detailed_ports.sort(key=lambda x: x["priority"])

        return {
            "success": True,
            "total_ports": len(detailed_ports),
            "ic_r8600_count": len(ic_r8600_found),
            "ports": detailed_ports,
            "ic_r8600_detected": ic_r8600_found,
            "current_port": current_status["port"],
            "current_mode": current_status["connection_mode"],
            "recommendation": ic_r8600_found[0] if ic_r8600_found else None
        }

    except Exception as e:
        logger.error(f"Erreur scan_ports: {e}")
        return {"success": False, "error": str(e)}

@app.post("/api/reconnect")
async def force_reconnect():
    """Force la reconnexion avec scan dynamique"""
    try:
        success = reconnect_if_needed()
        return {
            "success": success,
            "port": current_status["port"],
            "connection_mode": current_status["connection_mode"],
            "message": f"Reconnecté sur {current_status['port']} ({current_status['connection_mode']})" if success else "Échec reconnexion"
        }
    except Exception as e:
        logger.error(f"Erreur reconnect: {e}")
        return {"success": False, "error": str(e)}

@app.post("/api/set_frequency")
async def set_frequency(request: FrequencyRequest):
    """Définir la fréquence"""
    try:
        freq_hz = int(request.frequency_mhz * 1000000)
        
        # Conversion BCD pour IC-R8600 (format correct)
        freq_str = f"{freq_hz:010d}"
        bcd_bytes = []

        # IC-R8600 format BCD : LSB first, mais ordre des digits correct
        # Pour 88500000 Hz → "0088500000" → [00, 85, 50, 00, 00]
        for i in range(5):
            pos = 8 - (i * 2)  # Position dans la chaîne (de droite à gauche)
            digit1 = int(freq_str[pos + 1])    # Digit de droite (unités)
            digit2 = int(freq_str[pos])        # Digit de gauche (dizaines)
            bcd_bytes.append((digit2 << 4) | digit1)

        logger.info(f"🔧 Fréquence {request.frequency_mhz} MHz → {freq_hz} Hz → '{freq_str}' → BCD: {[hex(b) for b in bcd_bytes]}")

        # Commande CI-V avec adresse locale 94h (qui fonctionne !)
        command = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x05] + bcd_bytes + [0xFD])
        
        if send_ci_v_command(command):
            current_status["frequency"] = freq_hz
            logger.info(f"✅ Fréquence définie: {request.frequency_mhz} MHz")
            return {
                "success": True,
                "frequency_mhz": request.frequency_mhz,
                "frequency_hz": freq_hz
            }
        else:
            raise HTTPException(status_code=500, detail="Échec définition fréquence")
            
    except Exception as e:
        logger.error(f"Erreur set_frequency: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/set_mode")
async def set_mode(request: ModeRequest):
    """Définir le mode"""
    modes = {
        'LSB': 0x00, 'USB': 0x01, 'AM': 0x02, 'CW': 0x03,
        'FM': 0x05, 'WFM': 0x06, 'CWR': 0x07, 'RTTY': 0x08
    }
    
    if request.mode not in modes:
        raise HTTPException(status_code=400, detail="Mode invalide")
    
    try:
        # Commande CI-V avec adresse locale 94h
        command = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x06, modes[request.mode], 0xFD])
        
        if send_ci_v_command(command):
            current_status["mode"] = request.mode
            logger.info(f"✅ Mode défini: {request.mode}")
            return {
                "success": True,
                "mode": request.mode
            }
        else:
            raise HTTPException(status_code=500, detail="Échec définition mode")
            
    except Exception as e:
        logger.error(f"Erreur set_mode: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/test_command")
async def test_command():
    """Test de commande simple"""
    try:
        # Commande de test (lecture fréquence) avec adresse locale 94h
        command = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
        
        if send_ci_v_command(command):
            return {
                "success": True,
                "message": "Commande test envoyée",
                "command": command.hex().upper()
            }
        else:
            return {
                "success": False,
                "message": "Échec commande test"
            }
            
    except Exception as e:
        logger.error(f"Erreur test_command: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/command")
async def send_command(request: CommandRequest):
    """Endpoint générique pour les commandes"""
    try:
        # Gestion des commandes basées sur les champs présents
        if request.frequency is not None:
            # Commande de fréquence
            result = await set_frequency(FrequencyRequest(frequency_mhz=request.frequency))
            return result
        elif request.mode is not None:
            # Commande de mode
            result = await set_mode(ModeRequest(mode=request.mode))
            return result
        elif request.power_on is not None:
            # Commande power RÉELLE
            if request.power_on:
                logger.info("Commande Power ON réelle")
                power_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x01, 0xFD])
                if send_ci_v_command(power_cmd):
                    current_status["power_on"] = True
                    return {"success": True, "message": "Power ON envoyé à l'IC-R8600"}
                else:
                    return {"success": False, "message": "Échec Power ON"}
            else:
                logger.info("Commande Power OFF réelle")
                power_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x00, 0xFD])
                if send_ci_v_command(power_cmd):
                    current_status["power_on"] = False
                    return {"success": True, "message": "Power OFF envoyé à l'IC-R8600"}
                else:
                    return {"success": False, "message": "Échec Power OFF"}
        elif request.command:
            # Commandes avec format command/parameters
            if request.command == "set_frequency":
                freq_mhz = request.parameters.get("frequency", 145.5)
                return await set_frequency(FrequencyRequest(frequency_mhz=freq_mhz))
            elif request.command == "set_mode":
                mode = request.parameters.get("mode", "FM")
                return await set_mode(ModeRequest(mode=mode))
            elif request.command == "get_status":
                return await get_status()
            else:
                return {"success": False, "message": f"Commande inconnue: {request.command}"}
        else:
            return {"success": False, "message": "Aucune commande spécifiée"}

    except Exception as e:
        logger.error(f"Erreur send_command: {e}")
        return {"success": False, "message": f"Erreur: {str(e)}"}

@app.get("/api/recordings")
async def get_recordings():
    """Liste des enregistrements (simulé)"""
    # Retourner directement un tableau pour compatibilité frontend
    return []

@app.post("/api/recordings/start")
async def start_recording():
    """Démarrer un enregistrement (simulé)"""
    return {
        "success": False,
        "message": "Enregistrement non implémenté dans la version simple"
    }

@app.post("/api/recordings/stop")
async def stop_recording():
    """Arrêter un enregistrement (simulé)"""
    return {
        "success": False,
        "message": "Enregistrement non implémenté dans la version simple"
    }

@app.post("/api/power_on")
async def power_on():
    """Power ON réel de l'IC-R8600"""
    try:
        logger.info("Power ON IC-R8600")
        power_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x01, 0xFD])

        if send_ci_v_command(power_cmd):
            current_status["power_on"] = True
            return {"success": True, "message": "IC-R8600 Power ON"}
        else:
            return {"success": False, "message": "Échec Power ON"}

    except Exception as e:
        logger.error(f"Erreur power_on: {e}")
        return {"success": False, "message": f"Erreur: {str(e)}"}

@app.post("/api/power_off")
async def power_off():
    """Power OFF réel de l'IC-R8600"""
    try:
        logger.info("Power OFF IC-R8600")
        power_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x00, 0xFD])

        if send_ci_v_command(power_cmd):
            current_status["power_on"] = False
            return {"success": True, "message": "IC-R8600 Power OFF"}
        else:
            return {"success": False, "message": "Échec Power OFF"}

    except Exception as e:
        logger.error(f"Erreur power_off: {e}")
        return {"success": False, "message": f"Erreur: {str(e)}"}

@app.post("/api/set_volume")
async def set_volume(volume: int):
    """Réglage volume réel"""
    try:
        if not (0 <= volume <= 255):
            return {"success": False, "message": "Volume doit être entre 0 et 255"}

        logger.info(f"Volume IC-R8600: {volume}")
        # Commande volume CI-V (0x14 0x01)
        vol_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x14, 0x01, volume, 0xFD])

        if send_ci_v_command(vol_cmd):
            return {"success": True, "message": f"Volume défini: {volume}"}
        else:
            return {"success": False, "message": "Échec réglage volume"}

    except Exception as e:
        logger.error(f"Erreur set_volume: {e}")
        return {"success": False, "message": f"Erreur: {str(e)}"}

@app.post("/api/set_squelch")
async def set_squelch(squelch: int):
    """Réglage squelch réel"""
    try:
        if not (0 <= squelch <= 255):
            return {"success": False, "message": "Squelch doit être entre 0 et 255"}

        logger.info(f"Squelch IC-R8600: {squelch}")
        # Commande squelch CI-V (0x14 0x03)
        sql_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x14, 0x03, squelch, 0xFD])

        if send_ci_v_command(sql_cmd):
            return {"success": True, "message": f"Squelch défini: {squelch}"}
        else:
            return {"success": False, "message": "Échec réglage squelch"}

    except Exception as e:
        logger.error(f"Erreur set_squelch: {e}")
        return {"success": False, "message": f"Erreur: {str(e)}"}

@app.get("/api/get_signal_meter")
async def get_signal_meter():
    """Lecture S-meter réel"""
    try:
        logger.info("Lecture S-meter IC-R8600")
        # Commande lecture S-meter CI-V (0x15 0x02)
        meter_cmd = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x15, 0x02, 0xFD])

        if send_ci_v_command(meter_cmd):
            # Note: Dans une vraie implémentation, il faudrait lire la réponse
            return {"success": True, "s_meter": 5, "message": "S-meter lu"}
        else:
            return {"success": False, "message": "Échec lecture S-meter"}

    except Exception as e:
        logger.error(f"Erreur get_signal_meter: {e}")
        return {"success": False, "message": f"Erreur: {str(e)}"}

# Démarrage
@app.on_event("startup")
async def startup():
    logger.info("🚀 Démarrage IC-R8600 VirtualHere Simple")
    logger.info("Port: COM6 (VirtualHere)")
    
    # Tentative de connexion automatique
    if connect_ic_r8600():
        logger.info("✅ Connexion automatique réussie")
    else:
        logger.warning("⚠️ Connexion automatique échouée")

@app.on_event("shutdown")
async def shutdown():
    global serial_connection
    if serial_connection:
        serial_connection.close()
    logger.info("🛑 Application arrêtée")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
