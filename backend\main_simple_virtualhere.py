#!/usr/bin/env python3
"""
Application simplifiée IC-R8600 VirtualHere
Démarrage rapide pour test de commande
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import serial
import time
import logging

# Configuration logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Application FastAPI
app = FastAPI(title="IC-R8600 VirtualHere Simple", version="1.0.0")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Modèles
class FrequencyRequest(BaseModel):
    frequency_mhz: float

class ModeRequest(BaseModel):
    mode: str

class CommandRequest(BaseModel):
    command: str
    parameters: dict = {}

# Variables globales
serial_connection = None
current_status = {
    "connected": False,
    "port": "COM7",
    "frequency": 145500000,
    "mode": "FM"
}

def connect_ic_r8600():
    """Connexion simple à l'IC-R8600"""
    global serial_connection, current_status
    
    try:
        if serial_connection:
            serial_connection.close()
        
        logger.info("Connexion IC-R8600 sur COM7...")
        serial_connection = serial.Serial('COM7', 19200, timeout=1)
        current_status["connected"] = True
        logger.info("✅ IC-R8600 connecté sur COM6")
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur connexion: {e}")
        current_status["connected"] = False
        return False

def send_ci_v_command(command_bytes):
    """Envoie une commande CI-V"""
    global serial_connection
    
    if not serial_connection or not current_status["connected"]:
        if not connect_ic_r8600():
            return False
    
    try:
        serial_connection.write(command_bytes)
        time.sleep(0.3)
        response = serial_connection.read(50)
        logger.info(f"Commande: {command_bytes.hex().upper()}")
        if response:
            logger.info(f"Réponse: {response.hex().upper()}")
        return True
        
    except Exception as e:
        logger.error(f"Erreur commande: {e}")
        current_status["connected"] = False
        return False

# Endpoints
@app.get("/")
async def root():
    return {
        "message": "IC-R8600 VirtualHere Simple",
        "status": "running",
        "port": "COM7"
    }

@app.get("/api/status")
async def get_status():
    """État du système"""
    return {
        "connected": current_status["connected"],
        "port": current_status["port"],
        "frequency": current_status["frequency"],
        "mode": current_status["mode"],
        "virtualhere": True,
        "connection_type": "usb_remote_virtualhere"
    }

@app.post("/api/connect")
async def connect():
    """Connexion manuelle"""
    success = connect_ic_r8600()
    return {
        "success": success,
        "message": "Connecté" if success else "Échec connexion"
    }

@app.post("/api/set_frequency")
async def set_frequency(request: FrequencyRequest):
    """Définir la fréquence"""
    try:
        freq_hz = int(request.frequency_mhz * 1000000)
        
        # Conversion BCD pour IC-R8600
        freq_str = f"{freq_hz:010d}"
        bcd_bytes = []
        for i in range(4, -1, -1):
            digit1 = int(freq_str[i*2])
            digit2 = int(freq_str[i*2 + 1])
            bcd_bytes.append((digit1 << 4) | digit2)
        
        # Commande CI-V
        command = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x05] + bcd_bytes + [0xFD])
        
        if send_ci_v_command(command):
            current_status["frequency"] = freq_hz
            logger.info(f"✅ Fréquence définie: {request.frequency_mhz} MHz")
            return {
                "success": True,
                "frequency_mhz": request.frequency_mhz,
                "frequency_hz": freq_hz
            }
        else:
            raise HTTPException(status_code=500, detail="Échec définition fréquence")
            
    except Exception as e:
        logger.error(f"Erreur set_frequency: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/set_mode")
async def set_mode(request: ModeRequest):
    """Définir le mode"""
    modes = {
        'LSB': 0x00, 'USB': 0x01, 'AM': 0x02, 'CW': 0x03,
        'FM': 0x05, 'WFM': 0x06, 'CWR': 0x07, 'RTTY': 0x08
    }
    
    if request.mode not in modes:
        raise HTTPException(status_code=400, detail="Mode invalide")
    
    try:
        # Commande CI-V
        command = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x06, modes[request.mode], 0xFD])
        
        if send_ci_v_command(command):
            current_status["mode"] = request.mode
            logger.info(f"✅ Mode défini: {request.mode}")
            return {
                "success": True,
                "mode": request.mode
            }
        else:
            raise HTTPException(status_code=500, detail="Échec définition mode")
            
    except Exception as e:
        logger.error(f"Erreur set_mode: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/test_command")
async def test_command():
    """Test de commande simple"""
    try:
        # Commande de test (lecture fréquence)
        command = bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD])
        
        if send_ci_v_command(command):
            return {
                "success": True,
                "message": "Commande test envoyée",
                "command": command.hex().upper()
            }
        else:
            return {
                "success": False,
                "message": "Échec commande test"
            }
            
    except Exception as e:
        logger.error(f"Erreur test_command: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/command")
async def send_command(request: CommandRequest):
    """Endpoint générique pour les commandes"""
    try:
        # Mapper les commandes courantes
        if request.command == "set_frequency":
            freq_mhz = request.parameters.get("frequency", 145.5)
            return await set_frequency(FrequencyRequest(frequency_mhz=freq_mhz))
        elif request.command == "set_mode":
            mode = request.parameters.get("mode", "FM")
            return await set_mode(ModeRequest(mode=mode))
        elif request.command == "get_status":
            return await get_status()
        else:
            return {
                "success": False,
                "message": f"Commande inconnue: {request.command}"
            }
    except Exception as e:
        logger.error(f"Erreur send_command: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/recordings")
async def get_recordings():
    """Liste des enregistrements (simulé)"""
    # Retourner directement un tableau pour compatibilité frontend
    return []

@app.post("/api/recordings/start")
async def start_recording():
    """Démarrer un enregistrement (simulé)"""
    return {
        "success": False,
        "message": "Enregistrement non implémenté dans la version simple"
    }

@app.post("/api/recordings/stop")
async def stop_recording():
    """Arrêter un enregistrement (simulé)"""
    return {
        "success": False,
        "message": "Enregistrement non implémenté dans la version simple"
    }

# Démarrage
@app.on_event("startup")
async def startup():
    logger.info("🚀 Démarrage IC-R8600 VirtualHere Simple")
    logger.info("Port: COM7 (VirtualHere)")
    
    # Tentative de connexion automatique
    if connect_ic_r8600():
        logger.info("✅ Connexion automatique réussie")
    else:
        logger.warning("⚠️ Connexion automatique échouée")

@app.on_event("shutdown")
async def shutdown():
    global serial_connection
    if serial_connection:
        serial_connection.close()
    logger.info("🛑 Application arrêtée")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
