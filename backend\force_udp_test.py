#!/usr/bin/env python3
"""
Test forcé des ports UDP après redémarrage IC-R8600
"""

import socket
import time

def test_udp_after_restart():
    """Test UDP après redémarrage complet de l'IC-R8600"""
    host = "**************"
    ports = [50001, 50002, 50003]
    
    print("TEST UDP FORCE APRES REDEMARRAGE IC-R8600")
    print("=" * 60)
    print("IMPORTANT: Redemarrez l'IC-R8600 avant ce test !")
    print("=" * 60)
    
    input("Appuyez sur Entree apres avoir redémarre l'IC-R8600...")
    
    for port in ports:
        print(f"\nTest port UDP {port}:")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(3.0)
            
            # Commandes CI-V à tester
            commands = [
                bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD]),  # Lecture fréquence
                bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x01, 0xFD]),  # Power ON
                bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x00, 0xFD]),  # Power OFF
            ]
            
            for i, cmd in enumerate(commands):
                print(f"  Commande {i+1}: {cmd.hex().upper()}")
                
                try:
                    sock.sendto(cmd, (host, port))
                    response, addr = sock.recvfrom(1024)
                    
                    print(f"  SUCCES! Reponse: {response.hex().upper()}")
                    print(f"  Port {port} FONCTIONNE !")
                    
                    sock.close()
                    return port  # Retourner le port qui fonctionne
                    
                except socket.timeout:
                    print(f"  Timeout commande {i+1}")
                except Exception as e:
                    print(f"  Erreur commande {i+1}: {e}")
            
            sock.close()
            print(f"  Port {port}: Aucune reponse")
            
        except Exception as e:
            print(f"  Erreur port {port}: {e}")
    
    print("\nAucun port UDP ne repond aux commandes CI-V")
    return None

def test_alternative_addresses():
    """Test avec différentes adresses CI-V"""
    host = "**************"
    port = 50001
    
    print(f"\nTEST ADRESSES CI-V ALTERNATIVES sur port {port}")
    print("=" * 50)
    
    # Différentes adresses à tester
    addresses = [
        (0x94, "IC-R8600 standard"),
        (0x96, "IC-R8600 alternatif"),
        (0x00, "Broadcast"),
        (0x88, "IC-R8600 possible"),
    ]
    
    for addr, desc in addresses:
        print(f"\nTest adresse 0x{addr:02X} ({desc}):")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(2.0)
            
            # Commande de lecture de fréquence avec cette adresse
            cmd = bytes([0xFE, 0xFE, addr, 0xE0, 0x03, 0xFD])
            print(f"  Commande: {cmd.hex().upper()}")
            
            sock.sendto(cmd, (host, port))
            response, addr_resp = sock.recvfrom(1024)
            
            print(f"  SUCCES! Reponse: {response.hex().upper()}")
            print(f"  Adresse 0x{addr:02X} FONCTIONNE !")
            
            sock.close()
            return addr
            
        except socket.timeout:
            print(f"  Timeout")
        except Exception as e:
            print(f"  Erreur: {e}")
        
        if 'sock' in locals():
            sock.close()
    
    print("\nAucune adresse CI-V ne repond")
    return None

def main():
    """Test principal"""
    print("DIAGNOSTIC COMPLET PORTS UDP IC-R8600")
    print("=" * 60)
    
    # Test 1: Ports UDP après redémarrage
    working_port = test_udp_after_restart()
    
    if working_port:
        print(f"\nSUCCES! Port UDP {working_port} fonctionne")
        print("L'application peut etre modifiee pour utiliser UDP")
    else:
        # Test 2: Adresses alternatives
        working_addr = test_alternative_addresses()
        
        if working_addr:
            print(f"\nSUCCES! Adresse 0x{working_addr:02X} fonctionne")
        else:
            print("\nECHEC: Aucun port UDP ne repond")
            print("\nRECOMMANDATIONS:")
            print("1. Verifiez Network Control: ON sur l'IC-R8600")
            print("2. Redemarrez completement l'IC-R8600")
            print("3. Verifiez la configuration IP")
            print("4. Testez avec le logiciel RS-R8600 officiel")
            print("5. L'interface Telnet reste fonctionnelle pour le monitoring")

if __name__ == "__main__":
    main()
