#!/usr/bin/env python3
"""
Debug des commandes CI-V pour IC-R8600
Teste les commandes directement via Telnet
"""

import socket
import time
import sys

def test_civ_commands_direct():
    """Test direct des commandes CI-V via Telnet"""
    host = "**************"
    port = 23
    
    print("DEBUG COMMANDES CI-V DIRECTES")
    print("=" * 50)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        sock.connect((host, port))
        
        # Lire le message de bienvenue
        welcome = sock.recv(1024)
        print(f"Bienvenue: {welcome.decode('ascii', errors='ignore').strip()}")
        
        # Test des commandes CI-V importantes
        commands = [
            # Commande, Description, Réponse attendue
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x03, 0xFD]), "Lecture frequence", "Frequence actuelle"),
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x04, 0xFD]), "Lecture mode", "Mode actuel"),
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x19, 0x00, 0xFD]), "Statut power", "Statut alimentation"),
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x01, 0xFD]), "Power ON", "Confirmation power ON"),
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x00, 0xFD]), "Power OFF", "Confirmation power OFF"),
        ]
        
        for i, (cmd, desc, expected) in enumerate(commands):
            print(f"\n{i+1}. Test: {desc}")
            print(f"   Commande: {cmd.hex().upper()}")
            
            # Envoyer la commande
            sock.send(cmd)
            time.sleep(0.5)
            
            # Lire la réponse
            try:
                response = sock.recv(1024)
                print(f"   Reponse brute: {response.hex().upper()}")
                
                # Chercher des patterns CI-V
                if b'\xFE\xFE' in response:
                    fe_pos = response.find(b'\xFE\xFE')
                    fd_pos = response.find(b'\xFD', fe_pos)
                    if fd_pos >= 0:
                        ci_v_data = response[fe_pos:fd_pos+1]
                        print(f"   CI-V extrait: {ci_v_data.hex().upper()}")
                        
                        # Analyser la réponse
                        if len(ci_v_data) >= 6:
                            src = ci_v_data[2]
                            dst = ci_v_data[3]
                            cmd_byte = ci_v_data[4]
                            data = ci_v_data[5:-1]
                            
                            print(f"   Source: 0x{src:02X}, Dest: 0x{dst:02X}")
                            print(f"   Commande: 0x{cmd_byte:02X}")
                            print(f"   Donnees: {data.hex().upper()}")
                            
                            # Vérifier si c'est un ACK/NAK
                            if cmd_byte == 0xFB:
                                print("   -> ACK (Commande acceptee)")
                            elif cmd_byte == 0xFA:
                                print("   -> NAK (Commande refusee)")
                            elif cmd_byte == 0x03 and len(data) >= 5:
                                # Réponse de fréquence
                                freq_hz = 0
                                for j, byte in enumerate(data):
                                    digit1 = (byte >> 4) & 0x0F
                                    digit2 = byte & 0x0F
                                    freq_hz += (digit1 * 10 + digit2) * (10 ** (j * 2))
                                freq_mhz = freq_hz / 1000000.0
                                print(f"   -> Frequence: {freq_mhz:.6f} MHz")
                            elif cmd_byte == 0x04 and len(data) >= 1:
                                # Réponse de mode
                                mode_code = data[0]
                                modes = {0x00: 'LSB', 0x01: 'USB', 0x02: 'AM', 0x03: 'CW',
                                        0x05: 'FM', 0x06: 'WFM', 0x07: 'CWR', 0x08: 'RTTY'}
                                mode_name = modes.get(mode_code, f'UNKNOWN(0x{mode_code:02X})')
                                print(f"   -> Mode: {mode_name}")
                else:
                    print("   -> Pas de pattern CI-V detecte")
                    # Afficher le texte lisible
                    text = response.decode('ascii', errors='ignore').strip()
                    if text:
                        print(f"   -> Texte: {text}")
                
            except socket.timeout:
                print("   -> Timeout (pas de reponse)")
            
            # Pause entre les commandes
            time.sleep(1)
        
        sock.close()
        print("\nTest termine")
        
    except Exception as e:
        print(f"Erreur: {e}")

def test_power_sequence():
    """Test spécifique de la séquence power ON/OFF"""
    host = "**************"
    port = 23
    
    print("\n" + "=" * 50)
    print("TEST SEQUENCE POWER ON/OFF")
    print("=" * 50)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        sock.connect((host, port))
        
        # Lire le prompt
        sock.recv(1024)
        
        # Séquence de test power
        sequence = [
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x19, 0x00, 0xFD]), "Lecture statut power initial"),
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x00, 0xFD]), "Power OFF"),
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x19, 0x00, 0xFD]), "Verification power OFF"),
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x18, 0x01, 0xFD]), "Power ON"),
            (bytes([0xFE, 0xFE, 0x94, 0xE0, 0x19, 0x00, 0xFD]), "Verification power ON"),
        ]
        
        for i, (cmd, desc) in enumerate(sequence):
            print(f"\n{i+1}. {desc}")
            print(f"   Commande: {cmd.hex().upper()}")
            
            sock.send(cmd)
            time.sleep(1)  # Délai plus long pour les commandes power
            
            try:
                response = sock.recv(1024)
                print(f"   Reponse: {response.hex().upper()}")
                
                # Analyser spécifiquement les réponses power
                if b'\xFE\xFE' in response:
                    fe_pos = response.find(b'\xFE\xFE')
                    fd_pos = response.find(b'\xFD', fe_pos)
                    if fd_pos >= 0:
                        ci_v_data = response[fe_pos:fd_pos+1]
                        if len(ci_v_data) >= 6:
                            cmd_byte = ci_v_data[4]
                            if cmd_byte == 0xFB:
                                print("   -> SUCCES: Commande acceptee")
                            elif cmd_byte == 0xFA:
                                print("   -> ECHEC: Commande refusee")
                            elif cmd_byte == 0x19:
                                # Réponse de statut power
                                if len(ci_v_data) >= 7:
                                    power_status = ci_v_data[6]
                                    status_text = "ON" if power_status == 0x01 else "OFF"
                                    print(f"   -> Statut power: {status_text}")
                
            except socket.timeout:
                print("   -> Timeout")
        
        sock.close()
        
    except Exception as e:
        print(f"Erreur sequence power: {e}")

if __name__ == "__main__":
    test_civ_commands_direct()
    test_power_sequence()
