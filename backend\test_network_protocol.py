#!/usr/bin/env python3
"""
Suite de tests pour validation du protocole réseau IC-R8600
Tests complets des fonctionnalités CI-V over Ethernet
"""

import time
import logging
from typing import Dict, List, Tuple, Optional
from icom_network_handler import ICOMNetworkHandler, NetworkConfig, ConnectionType

class ICOMNetworkTester:
    """Testeur pour protocole réseau IC-R8600"""
    
    def __init__(self, host: str = "**************", port: int = 50001):
        self.host = host
        self.port = port
        self.test_results = {}
        
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def test_connectivity(self) -> Dict[str, bool]:
        """Test de connectivité de base"""
        print("🔍 Test de connectivité")
        results = {}
        
        # Test UDP
        udp_config = NetworkConfig(
            host=self.host,
            port=self.port,
            connection_type=ConnectionType.UDP,
            timeout=3.0
        )
        
        udp_handler = ICOMNetworkHandler(udp_config)
        results["udp_connection"] = udp_handler.connect()
        if results["udp_connection"]:
            print("  ✅ Connexion UDP réussie")
            udp_handler.disconnect()
        else:
            print("  ❌ Connexion UDP échouée")
        
        # Test TCP
        tcp_config = NetworkConfig(
            host=self.host,
            port=self.port,
            connection_type=ConnectionType.TCP,
            timeout=3.0
        )
        
        tcp_handler = ICOMNetworkHandler(tcp_config)
        results["tcp_connection"] = tcp_handler.connect()
        if results["tcp_connection"]:
            print("  ✅ Connexion TCP réussie")
            tcp_handler.disconnect()
        else:
            print("  ❌ Connexion TCP échouée")
        
        return results
    
    def test_basic_commands(self, connection_type: ConnectionType) -> Dict[str, bool]:
        """Test des commandes de base"""
        print(f"🧪 Test commandes de base ({connection_type.value.upper()})")
        
        config = NetworkConfig(
            host=self.host,
            port=self.port,
            connection_type=connection_type,
            timeout=3.0
        )
        
        handler = ICOMNetworkHandler(config)
        results = {}
        
        if not handler.connect():
            print("  ❌ Connexion échouée")
            return {"connection": False}
        
        results["connection"] = True
        
        # Test lecture fréquence
        freq = handler.get_frequency()
        results["get_frequency"] = freq is not None
        if freq:
            print(f"  ✅ Fréquence lue: {freq} Hz")
        else:
            print("  ❌ Lecture fréquence échouée")
        
        # Test lecture mode
        mode = handler.get_mode()
        results["get_mode"] = mode is not None
        if mode:
            print(f"  ✅ Mode lu: {mode}")
        else:
            print("  ❌ Lecture mode échouée")
        
        # Test lecture RSSI
        rssi = handler.get_rssi()
        results["get_rssi"] = rssi is not None
        if rssi is not None:
            print(f"  ✅ RSSI lu: {rssi}")
        else:
            print("  ❌ Lecture RSSI échouée")
        
        handler.disconnect()
        return results
    
    def test_frequency_control(self, connection_type: ConnectionType) -> Dict[str, bool]:
        """Test du contrôle de fréquence"""
        print(f"📻 Test contrôle fréquence ({connection_type.value.upper()})")
        
        config = NetworkConfig(
            host=self.host,
            port=self.port,
            connection_type=connection_type,
            timeout=3.0
        )
        
        handler = ICOMNetworkHandler(config)
        results = {}
        
        if not handler.connect():
            return {"connection": False}
        
        # Sauvegarder la fréquence actuelle
        original_freq = handler.get_frequency()
        if not original_freq:
            handler.disconnect()
            return {"get_original_frequency": False}
        
        print(f"  📡 Fréquence originale: {original_freq} Hz")
        
        # Test fréquences
        test_frequencies = [
            145500000,  # 145.5 MHz (2m)
            433500000,  # 433.5 MHz (70cm)
            121500000,  # 121.5 MHz (Aviation)
        ]
        
        for test_freq in test_frequencies:
            # Définir la fréquence
            set_success = handler.set_frequency(test_freq)
            results[f"set_frequency_{test_freq}"] = set_success
            
            if set_success:
                time.sleep(0.5)  # Attendre la prise en compte
                
                # Vérifier la fréquence
                read_freq = handler.get_frequency()
                freq_match = read_freq == test_freq
                results[f"verify_frequency_{test_freq}"] = freq_match
                
                if freq_match:
                    print(f"  ✅ Fréquence {test_freq} Hz définie et vérifiée")
                else:
                    print(f"  ❌ Fréquence {test_freq} Hz: définie mais lecture {read_freq}")
            else:
                print(f"  ❌ Échec définition fréquence {test_freq} Hz")
        
        # Restaurer la fréquence originale
        restore_success = handler.set_frequency(original_freq)
        results["restore_frequency"] = restore_success
        
        if restore_success:
            print(f"  ✅ Fréquence originale restaurée")
        else:
            print(f"  ⚠️ Échec restauration fréquence originale")
        
        handler.disconnect()
        return results
    
    def test_mode_control(self, connection_type: ConnectionType) -> Dict[str, bool]:
        """Test du contrôle de mode"""
        print(f"🎛️ Test contrôle mode ({connection_type.value.upper()})")
        
        config = NetworkConfig(
            host=self.host,
            port=self.port,
            connection_type=connection_type,
            timeout=3.0
        )
        
        handler = ICOMNetworkHandler(config)
        results = {}
        
        if not handler.connect():
            return {"connection": False}
        
        # Sauvegarder le mode actuel
        original_mode = handler.get_mode()
        if not original_mode:
            handler.disconnect()
            return {"get_original_mode": False}
        
        print(f"  🎛️ Mode original: {original_mode}")
        
        # Test modes
        test_modes = ["FM", "AM", "USB", "LSB"]
        
        for test_mode in test_modes:
            # Définir le mode
            set_success = handler.set_mode(test_mode)
            results[f"set_mode_{test_mode}"] = set_success
            
            if set_success:
                time.sleep(0.5)  # Attendre la prise en compte
                
                # Vérifier le mode
                read_mode = handler.get_mode()
                mode_match = read_mode == test_mode
                results[f"verify_mode_{test_mode}"] = mode_match
                
                if mode_match:
                    print(f"  ✅ Mode {test_mode} défini et vérifié")
                else:
                    print(f"  ❌ Mode {test_mode}: défini mais lecture {read_mode}")
            else:
                print(f"  ❌ Échec définition mode {test_mode}")
        
        # Restaurer le mode original
        restore_success = handler.set_mode(original_mode)
        results["restore_mode"] = restore_success
        
        if restore_success:
            print(f"  ✅ Mode original restauré")
        else:
            print(f"  ⚠️ Échec restauration mode original")
        
        handler.disconnect()
        return results
    
    def test_power_control(self, connection_type: ConnectionType) -> Dict[str, bool]:
        """Test du contrôle d'alimentation (ATTENTION: peut éteindre le récepteur)"""
        print(f"⚡ Test contrôle alimentation ({connection_type.value.upper()})")
        print("  ⚠️ ATTENTION: Ce test peut éteindre le récepteur!")
        
        response = input("  Continuer? (o/N): ").strip().lower()
        if response not in ['o', 'oui', 'y', 'yes']:
            return {"skipped": True}
        
        config = NetworkConfig(
            host=self.host,
            port=self.port,
            connection_type=connection_type,
            timeout=3.0
        )
        
        handler = ICOMNetworkHandler(config)
        results = {}
        
        if not handler.connect():
            return {"connection": False}
        
        # Test power ON (normalement déjà ON)
        power_on_success = handler.power_on()
        results["power_on"] = power_on_success
        
        if power_on_success:
            print("  ✅ Commande Power ON envoyée")
        else:
            print("  ❌ Échec commande Power ON")
        
        # Note: On évite de tester power_off pour ne pas éteindre le récepteur
        print("  ℹ️ Test Power OFF ignoré pour éviter d'éteindre le récepteur")
        
        handler.disconnect()
        return results
    
    def test_performance(self, connection_type: ConnectionType) -> Dict[str, float]:
        """Test de performance et latence"""
        print(f"⚡ Test performance ({connection_type.value.upper()})")
        
        config = NetworkConfig(
            host=self.host,
            port=self.port,
            connection_type=connection_type,
            timeout=3.0
        )
        
        handler = ICOMNetworkHandler(config)
        results = {}
        
        if not handler.connect():
            return {"connection_time": -1}
        
        # Test latence commandes
        commands_to_test = [
            ("get_frequency", lambda: handler.get_frequency()),
            ("get_mode", lambda: handler.get_mode()),
            ("get_rssi", lambda: handler.get_rssi()),
        ]
        
        for cmd_name, cmd_func in commands_to_test:
            times = []
            
            for _ in range(5):  # 5 mesures
                start_time = time.time()
                result = cmd_func()
                end_time = time.time()
                
                if result is not None:
                    times.append(end_time - start_time)
                
                time.sleep(0.1)  # Délai entre mesures
            
            if times:
                avg_time = sum(times) / len(times)
                results[f"{cmd_name}_latency"] = avg_time
                print(f"  📊 {cmd_name}: {avg_time*1000:.1f} ms")
            else:
                results[f"{cmd_name}_latency"] = -1
                print(f"  ❌ {cmd_name}: échec")
        
        handler.disconnect()
        return results
    
    def run_full_test_suite(self) -> Dict[str, Dict]:
        """Exécute la suite complète de tests"""
        print("🚀 Suite de tests complète IC-R8600")
        print("=" * 50)
        
        all_results = {}
        
        # Test de connectivité
        connectivity_results = self.test_connectivity()
        all_results["connectivity"] = connectivity_results
        
        # Déterminer le meilleur type de connexion
        if connectivity_results.get("udp_connection"):
            preferred_connection = ConnectionType.UDP
            print("\n✅ UDP disponible - utilisation d'UDP")
        elif connectivity_results.get("tcp_connection"):
            preferred_connection = ConnectionType.TCP
            print("\n✅ TCP disponible - utilisation de TCP")
        else:
            print("\n❌ Aucune connexion disponible")
            return all_results
        
        # Tests avec le type de connexion préféré
        print(f"\n🧪 Tests avec {preferred_connection.value.upper()}")
        
        all_results["basic_commands"] = self.test_basic_commands(preferred_connection)
        all_results["frequency_control"] = self.test_frequency_control(preferred_connection)
        all_results["mode_control"] = self.test_mode_control(preferred_connection)
        all_results["power_control"] = self.test_power_control(preferred_connection)
        all_results["performance"] = self.test_performance(preferred_connection)
        
        return all_results
    
    def print_summary(self, results: Dict[str, Dict]):
        """Affiche un résumé des résultats"""
        print("\n" + "=" * 50)
        print("RÉSUMÉ DES TESTS")
        print("=" * 50)
        
        total_tests = 0
        passed_tests = 0
        
        for category, category_results in results.items():
            if isinstance(category_results, dict):
                category_total = len([k for k, v in category_results.items() if isinstance(v, bool)])
                category_passed = len([k for k, v in category_results.items() if v is True])
                
                total_tests += category_total
                passed_tests += category_passed
                
                if category_total > 0:
                    success_rate = (category_passed / category_total) * 100
                    print(f"{category:20}: {category_passed}/{category_total} ({success_rate:.1f}%)")
        
        if total_tests > 0:
            overall_success = (passed_tests / total_tests) * 100
            print(f"{'TOTAL':20}: {passed_tests}/{total_tests} ({overall_success:.1f}%)")
        
        # Recommandations
        print("\n💡 RECOMMANDATIONS:")
        
        if results.get("connectivity", {}).get("udp_connection"):
            print("  ✅ Utilisez UDP pour le contrôle optimal")
        elif results.get("connectivity", {}).get("tcp_connection"):
            print("  ✅ Utilisez TCP pour le contrôle")
        else:
            print("  ❌ Vérifiez la configuration réseau de l'IC-R8600")
        
        if passed_tests == total_tests:
            print("  🎉 Tous les tests sont passés - contrôle réseau fonctionnel!")
        elif passed_tests > total_tests * 0.8:
            print("  ✅ La plupart des tests passent - contrôle réseau largement fonctionnel")
        else:
            print("  ⚠️ Plusieurs tests échouent - vérifiez la configuration")


def main():
    """Fonction principale"""
    print("Tests Protocole Réseau IC-R8600")
    print("===============================")
    
    # Demander l'IP cible
    target_ip = input("Adresse IP de l'IC-R8600 (défaut: **************): ").strip()
    if not target_ip:
        target_ip = "**************"
    
    target_port = input("Port (défaut: 50001): ").strip()
    if not target_port:
        target_port = 50001
    else:
        target_port = int(target_port)
    
    # Créer et exécuter les tests
    tester = ICOMNetworkTester(target_ip, target_port)
    results = tester.run_full_test_suite()
    tester.print_summary(results)


if __name__ == "__main__":
    main()
