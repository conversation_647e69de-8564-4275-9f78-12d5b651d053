#!/usr/bin/env python3
"""
Vérification complète de la configuration IC-R8600 pour contrôle réseau
"""

import socket
import time
import subprocess
import sys

def check_network_connectivity():
    """Vérification de la connectivité réseau"""
    print("=" * 60)
    print("1. VÉRIFICATION CONNECTIVITÉ RÉSEAU")
    print("=" * 60)
    
    host = "**************"
    
    # Test ping
    try:
        result = subprocess.run(['ping', '-n', '4', host], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ Ping réussi vers IC-R8600")
            print(f"   Réponse: {result.stdout.split('=')[1].split('ms')[0].strip()}ms")
        else:
            print("❌ Ping échoué vers IC-R8600")
            return False
    except Exception as e:
        print(f"❌ Erreur ping: {e}")
        return False
    
    return True

def check_ports():
    """Vérification des ports réseau"""
    print("\n" + "=" * 60)
    print("2. VÉRIFICATION PORTS RÉSEAU")
    print("=" * 60)
    
    host = "**************"
    ports_to_check = [23, 50001, 50002, 50003, 80, 443, 8080]
    
    for port in ports_to_check:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2.0)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"✅ Port {port} OUVERT")
            else:
                print(f"❌ Port {port} FERMÉ")
                
        except Exception as e:
            print(f"❌ Port {port} ERREUR: {e}")

def test_telnet_manual():
    """Test Telnet manuel pour voir les réponses exactes"""
    print("\n" + "=" * 60)
    print("3. TEST TELNET MANUEL")
    print("=" * 60)
    
    host = "**************"
    port = 23
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10.0)
        sock.connect((host, port))
        
        print("✅ Connexion Telnet établie")
        
        # Lire le message de bienvenue
        try:
            welcome = sock.recv(1024)
            print(f"Message de bienvenue: {welcome}")
            print(f"Hex: {welcome.hex().upper()}")
            print(f"ASCII: {welcome.decode('ascii', errors='ignore')}")
        except socket.timeout:
            print("Pas de message de bienvenue")
        
        # Envoyer des commandes de test
        test_commands = [
            b"help\r\n",
            b"?\r\n", 
            b"status\r\n",
            b"info\r\n",
            b"version\r\n",
            b"\r\n",
        ]
        
        for cmd in test_commands:
            print(f"\nEnvoi: {cmd}")
            sock.sendall(cmd)
            time.sleep(1)
            
            try:
                response = sock.recv(1024)
                print(f"Réponse: {response}")
                print(f"ASCII: {response.decode('ascii', errors='ignore')}")
            except socket.timeout:
                print("Pas de réponse")
        
        sock.close()
        
    except Exception as e:
        print(f"❌ Erreur Telnet: {e}")

def test_udp_discovery():
    """Test de découverte UDP"""
    print("\n" + "=" * 60)
    print("4. TEST DÉCOUVERTE UDP")
    print("=" * 60)
    
    host = "**************"
    ports = [50001, 50002, 50003]
    
    for port in ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(3.0)
            
            # Envoyer différents types de paquets de découverte
            discovery_packets = [
                b'\x00\x00\x00\x00',  # Magic ICOM
                b'\xFE\xFE\x94\xE0\x19\x00\xFD',  # CI-V status
                b'HELLO',  # Test simple
                b'\x01\x02\x03\x04',  # Test binaire
            ]
            
            print(f"\nTest port UDP {port}:")
            
            for i, packet in enumerate(discovery_packets):
                sock.sendto(packet, (host, port))
                print(f"  Envoi paquet {i+1}: {packet.hex().upper()}")
                
                try:
                    response, addr = sock.recvfrom(1024)
                    print(f"  ✅ Réponse: {response.hex().upper()}")
                    print(f"     ASCII: {response.decode('ascii', errors='ignore')}")
                except socket.timeout:
                    print(f"  ❌ Pas de réponse")
            
            sock.close()
            
        except Exception as e:
            print(f"❌ Erreur UDP port {port}: {e}")

def check_ic_r8600_manual_config():
    """Guide de vérification manuelle de la configuration IC-R8600"""
    print("\n" + "=" * 60)
    print("5. VÉRIFICATION MANUELLE IC-R8600")
    print("=" * 60)
    
    print("VÉRIFIEZ MANUELLEMENT SUR L'IC-R8600 :")
    print()
    print("1. MENU → SET → Network → Network Control")
    print("   ➤ Doit être sur 'ON'")
    print()
    print("2. MENU → SET → Network → IP Address")
    print("   ➤ Doit être **************")
    print()
    print("3. MENU → SET → Network → Subnet Mask")
    print("   ➤ Doit être *************")
    print()
    print("4. MENU → SET → Network → Gateway")
    print("   ➤ Peut être ************ ou 0.0.0.0")
    print()
    print("5. MENU → SET → Network → Control Port")
    print("   ➤ Vérifiez le numéro de port (50001 par défaut)")
    print()
    print("6. MENU → SET → Network → Serial Port")
    print("   ➤ Vérifiez le numéro de port (50002 par défaut)")
    print()
    print("7. REDÉMARRAGE COMPLET")
    print("   ➤ Éteignez et rallumez l'IC-R8600 après changement")
    print()
    print("8. BOUTON LOCAL")
    print("   ➤ Appuyez sur [LOCAL] pour désactiver le contrôle local")
    print("   ➤ L'écran doit afficher 'REMOTE' ou similaire")
    print()
    
    input("Appuyez sur Entrée après avoir vérifié ces paramètres...")

def test_rs_r8600_compatibility():
    """Test de compatibilité avec le logiciel officiel"""
    print("\n" + "=" * 60)
    print("6. TEST COMPATIBILITÉ RS-R8600")
    print("=" * 60)
    
    print("POUR VALIDER LA CONFIGURATION :")
    print()
    print("1. Téléchargez le logiciel officiel RS-R8600 d'ICOM")
    print("   ➤ https://www.icom.co.jp/world/support/download/")
    print()
    print("2. Configurez RS-R8600 avec :")
    print("   ➤ IP: **************")
    print("   ➤ Port: 50001")
    print("   ➤ Connexion: Network")
    print()
    print("3. Testez la connexion avec RS-R8600")
    print("   ➤ Si RS-R8600 fonctionne, le problème est dans notre code")
    print("   ➤ Si RS-R8600 ne fonctionne pas, le problème est dans la config IC-R8600")
    print()

def main():
    """Fonction principale de diagnostic"""
    print("DIAGNOSTIC COMPLET IC-R8600 CONTRÔLE RÉSEAU")
    print("Observez attentivement chaque étape !")
    
    # Étape 1: Connectivité
    if not check_network_connectivity():
        print("\n❌ ARRÊT: Problème de connectivité réseau de base")
        return
    
    # Étape 2: Ports
    check_ports()
    
    # Étape 3: Telnet
    test_telnet_manual()
    
    # Étape 4: UDP
    test_udp_discovery()
    
    # Étape 5: Configuration manuelle
    check_ic_r8600_manual_config()
    
    # Étape 6: Test logiciel officiel
    test_rs_r8600_compatibility()
    
    print("\n" + "=" * 60)
    print("DIAGNOSTIC TERMINÉ")
    print("=" * 60)
    print("RÉSULTATS ATTENDUS :")
    print("- Si AUCUN port n'est ouvert → Problème config réseau IC-R8600")
    print("- Si ports ouverts mais pas de réponses → Problème protocole")
    print("- Si RS-R8600 officiel ne fonctionne pas → Problème IC-R8600")
    print("- Si RS-R8600 officiel fonctionne → Problème notre implémentation")

if __name__ == "__main__":
    main()
