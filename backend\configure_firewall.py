#!/usr/bin/env python3
"""
Configuration du firewall Windows pour l'application IC-R8600
Crée les règles nécessaires pour autoriser la communication UDP
"""

import subprocess
import sys
import os

def run_as_admin():
    """Vérifie si le script est exécuté en tant qu'administrateur"""
    try:
        return os.getuid() == 0
    except AttributeError:
        # Windows
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin() != 0

def create_firewall_rules():
    """Crée les règles de firewall pour l'application"""
    
    if not run_as_admin():
        print("❌ Ce script doit être exécuté en tant qu'administrateur")
        print("🔧 Clic droit → 'Exécuter en tant qu'administrateur'")
        return False
    
    print("🛡️ Configuration du firewall Windows...")
    
    # Obtenir le chemin de Python
    python_path = sys.executable
    print(f"📍 Python détecté: {python_path}")
    
    rules = [
        {
            "name": "IC-R8600 Python UDP Out",
            "direction": "out",
            "action": "allow",
            "protocol": "UDP",
            "localport": "any",
            "remoteport": "50001,50002,50003",
            "program": python_path,
            "description": "Autoriser Python à envoyer des commandes UDP vers IC-R8600"
        },
        {
            "name": "IC-R8600 Python UDP In", 
            "direction": "in",
            "action": "allow",
            "protocol": "UDP",
            "localport": "any",
            "remoteport": "50001,50002,50003",
            "program": python_path,
            "description": "Autoriser Python à recevoir des réponses UDP de l'IC-R8600"
        },
        {
            "name": "IC-R8600 Python TCP Out",
            "direction": "out", 
            "action": "allow",
            "protocol": "TCP",
            "localport": "any",
            "remoteport": "23,50001,50002,50003",
            "program": python_path,
            "description": "Autoriser Python à se connecter en TCP à l'IC-R8600"
        }
    ]
    
    success_count = 0
    
    for rule in rules:
        print(f"\n📝 Création de la règle: {rule['name']}")
        
        # Supprimer la règle existante si elle existe
        delete_cmd = [
            "netsh", "advfirewall", "firewall", "delete", "rule",
            f"name={rule['name']}"
        ]
        
        try:
            subprocess.run(delete_cmd, capture_output=True, check=False)
        except:
            pass
        
        # Créer la nouvelle règle
        create_cmd = [
            "netsh", "advfirewall", "firewall", "add", "rule",
            f"name={rule['name']}",
            f"dir={rule['direction']}",
            f"action={rule['action']}",
            f"protocol={rule['protocol']}",
            f"localport={rule.get('localport', 'any')}",
            f"remoteport={rule.get('remoteport', 'any')}",
            f"program={rule['program']}",
            "enable=yes"
        ]
        
        try:
            result = subprocess.run(create_cmd, capture_output=True, text=True, check=True)
            print(f"  ✅ Règle créée avec succès")
            success_count += 1
        except subprocess.CalledProcessError as e:
            print(f"  ❌ Erreur création règle: {e}")
            print(f"  📄 Sortie: {e.stdout}")
            print(f"  📄 Erreur: {e.stderr}")
    
    print(f"\n📊 Résumé: {success_count}/{len(rules)} règles créées")
    
    if success_count == len(rules):
        print("✅ Toutes les règles de firewall ont été créées")
        return True
    else:
        print("⚠️ Certaines règles n'ont pas pu être créées")
        return False

def test_firewall_rules():
    """Teste si les règles de firewall sont actives"""
    print("\n🧪 Test des règles de firewall...")
    
    try:
        result = subprocess.run([
            "netsh", "advfirewall", "firewall", "show", "rule", 
            "name=IC-R8600 Python UDP Out"
        ], capture_output=True, text=True)
        
        if "IC-R8600 Python UDP Out" in result.stdout:
            print("✅ Règles de firewall détectées")
            return True
        else:
            print("❌ Règles de firewall non trouvées")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test firewall: {e}")
        return False

def show_manual_instructions():
    """Affiche les instructions manuelles si l'automatisation échoue"""
    print("\n" + "=" * 70)
    print("📋 INSTRUCTIONS MANUELLES - FIREWALL")
    print("=" * 70)
    
    print("Si la configuration automatique échoue, suivez ces étapes :")
    print("\n1. 🔧 Ouvrir le Firewall Windows :")
    print("   - Panneau de configuration → Système et sécurité → Firewall Windows")
    print("   - Ou tapez 'wf.msc' dans Exécuter (Win+R)")
    
    print("\n2. 📝 Créer une règle de trafic sortant :")
    print("   - Clic droit sur 'Règles de trafic sortant'")
    print("   - Nouvelle règle → Programme")
    print(f"   - Chemin du programme : {sys.executable}")
    print("   - Autoriser la connexion")
    print("   - Tous les profils")
    print("   - Nom : 'IC-R8600 Python UDP'")
    
    print("\n3. 📝 Créer une règle de trafic entrant :")
    print("   - Même procédure pour 'Règles de trafic entrant'")
    
    print("\n4. 🔄 Redémarrer l'application après configuration")

if __name__ == "__main__":
    print("=" * 70)
    print("🛡️ CONFIGURATION FIREWALL IC-R8600")
    print("=" * 70)
    
    # Vérifier les permissions
    if not run_as_admin():
        print("⚠️ Permissions administrateur requises")
        print("\n🔧 Pour exécuter en tant qu'administrateur :")
        print("1. Ouvrir une invite de commande en tant qu'administrateur")
        print("2. Naviguer vers le dossier backend")
        print("3. Exécuter : python configure_firewall.py")
        print("\nOu utiliser les instructions manuelles ci-dessous...")
        show_manual_instructions()
        sys.exit(1)
    
    # Créer les règles
    success = create_firewall_rules()
    
    # Tester les règles
    if success:
        test_firewall_rules()
        
        print("\n🎉 Configuration firewall terminée !")
        print("🚀 Vous pouvez maintenant tester l'application :")
        print("   python lancer_application.py")
    else:
        print("\n⚠️ Configuration automatique échouée")
        show_manual_instructions()
    
    print("\n" + "=" * 70)
