#!/usr/bin/env python3
"""
Test de l'API CI-V directe
"""

import requests
import time

def test_civ_api():
    """Test des commandes CI-V via l'API"""
    base_url = "http://localhost:8000"
    
    print("TEST API CI-V DIRECTE")
    print("=" * 40)
    
    # Commandes CI-V à tester
    commands = [
        ("FEFE94E003FD", "Lecture fréquence"),
        ("FEFE94E004FD", "Lecture mode"),
        ("FEFE94E01900FD", "Lecture statut power"),
        ("FEFE94E01801FD", "Power ON"),
        ("FEFE94E01800FD", "Power OFF"),
        ("FEFE94E01801FD", "Power ON (test 2)"),
    ]
    
    for i, (cmd_hex, description) in enumerate(commands):
        print(f"\n{i+1}. {description}")
        print(f"   Commande: {cmd_hex}")
        
        try:
            response = requests.post(
                f"{base_url}/api/civ_command",
                params={"command_hex": cmd_hex},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result["success"]:
                    print(f"   ✅ SUCCES")
                    print(f"   Réponse: {result['response_hex']}")
                    print(f"   Longueur: {result['response_length']} bytes")
                    
                    # Analyser la réponse
                    resp_hex = result['response_hex']
                    if resp_hex.startswith("FEFE"):
                        print(f"   📡 Pattern CI-V détecté")
                        if "FB" in resp_hex:
                            print(f"   ✅ ACK reçu")
                        elif "FA" in resp_hex:
                            print(f"   ❌ NAK reçu")
                else:
                    print(f"   ❌ ECHEC: {result['error']}")
            else:
                print(f"   ❌ Erreur HTTP: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Erreur: {e}")
        
        # Pause entre les commandes
        time.sleep(1)

def test_status_api():
    """Test de l'API de statut"""
    base_url = "http://localhost:8000"
    
    print(f"\n" + "=" * 40)
    print("TEST API STATUT")
    print("=" * 40)
    
    try:
        response = requests.get(f"{base_url}/api/status")
        if response.status_code == 200:
            status = response.json()
            print("✅ Statut récupéré:")
            for key, value in status.items():
                print(f"   {key}: {value}")
        else:
            print(f"❌ Erreur statut: {response.status_code}")
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    test_status_api()
    test_civ_api()
