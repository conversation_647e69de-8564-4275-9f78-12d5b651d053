#!/usr/bin/env python3
"""
Test final des commandes IC-R8600 avec adresse 94h
"""

import requests
import time

BASE_URL = "http://localhost:8001"

def test_frequency():
    """Test changement de fréquence"""
    print("🔍 TEST FRÉQUENCE 88.5 MHz")
    print("⚠️  OBSERVEZ L'AFFICHAGE IC-R8600 !")
    
    data = {"frequency_mhz": 88.5}
    response = requests.post(f"{BASE_URL}/api/set_frequency", json=data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Commande envoyée: {result['success']}")
        print(f"✅ Fréquence: {result['frequency_mhz']} MHz")
        return True
    else:
        print(f"❌ Erreur: {response.status_code}")
        return False

def test_mode():
    """Test changement de mode"""
    print("\n🔍 TEST MODE AM")
    print("⚠️  OBSERVEZ L'AFFICHAGE IC-R8600 !")
    
    data = {"mode": "AM"}
    response = requests.post(f"{BASE_URL}/api/set_mode", json=data)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Commande envoyée: {result['success']}")
        print(f"✅ Mode: {result['mode']}")
        return True
    else:
        print(f"❌ Erreur: {response.status_code}")
        return False

def main():
    """Test final"""
    print("🎉 TEST FINAL IC-R8600 - ADRESSE 94h")
    print("=" * 50)
    
    # Test fréquence
    test_frequency()
    time.sleep(2)
    
    # Test mode
    test_mode()
    
    print("\n🎉 TESTS TERMINÉS !")
    print("L'IC-R8600 devrait maintenant réagir aux commandes !")

if __name__ == "__main__":
    main()
