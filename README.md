# ICOM IC-R8600 Controller

Un projet FullStack pour contrôler le récepteur ICOM IC-R8600 via CI-V avec support réseau Ethernet.

## 🎯 Fonctionnalités

- **Contrôle réseau** : Contrôle via câble RJ45 Ethernet (priorité)
- **Fallback USB** : Basculement automatique vers USB si réseau indisponible
- **Contrôle distant** : ON/OFF, fréquence, modulation, RF gain, filtres
- **Monitoring** : Fréquence actuelle, RSSI en temps réel
- **Enregistrement audio** : Capture AF/IF en fichiers WAV
- **Scan programmable** : Balayage automatique de fréquences
- **Interface web moderne** : React + FastAPI
- **Auto-découverte** : Détection automatique de l'IC-R8600 sur le réseau

## 🏗️ Architecture

```
icom-controller/
├── frontend/           # React app (Vite)
│   ├── src/
│   │   ├── App.jsx
│   │   ├── components/
│   │   └── main.jsx
│   ├── package.json
│   └── index.html
├── backend/
│   ├── main.py         # FastAPI server
│   ├── icom_handler.py # CI-V communication
│   ├── audio_recorder.py # Audio capture
│   └── models.py       # Pydantic models
├── requirements.txt
└── README.md
```

## 🚀 Installation

### Backend

```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend

```bash
cd frontend
npm install
npm run dev
```

## 🔌 Configuration

### Réseau (Recommandé)

- **IP IC-R8600** : ************** (configurable)
- **Port CI-V** : 50001
- **Protocole** : UDP/TCP
- **Câble** : RJ45 Ethernet

### Série (Fallback)

- **Port série** : `/dev/ttyUSB0` (Linux) ou `COM6` (Windows)
- **Baudrate** : 19200
- **Adresse CI-V** : 0x96 (IC-R8600)
- **Adresse contrôleur** : 0xE0

## 📡 Commandes CI-V supportées

| Fonction      | Commande                      | Exemple     |
| ------------- | ----------------------------- | ----------- |
| Power ON/OFF  | `FE FE 96 E0 18 01 FD`        | ON          |
| Set Frequency | `FE FE 96 E0 05 [freq] FD`    | 145.500 MHz |
| Get Frequency | `FE FE 96 E0 03 FD`           | -           |
| Set Mode      | `FE FE 96 E0 06 [mode] FD`    | AM/FM/USB   |
| Get RSSI      | `FE FE 96 E0 15 02 FD`        | -           |
| RF Gain       | `FE FE 96 E0 14 02 [gain] FD` | 0-255       |

## 🎵 Enregistrement Audio

- **Format** : WAV 16-bit 48kHz
- **Sources** : AF (Audio Frequency) ou IF (Intermediate Frequency)
- **Stockage** : `backend/recordings/`

## 🌐 API Endpoints

- `POST /api/command` - Envoyer commande CI-V
- `GET /api/status` - État actuel du récepteur
- `POST /api/audio/start` - Démarrer enregistrement
- `POST /api/audio/stop` - Arrêter enregistrement
- `GET /api/recordings` - Liste des fichiers audio

## 🚀 Configuration Réseau Rapide

### Étape 1: Configuration IC-R8600

```
Menu → SET → Connectors → LAN
- IP Address: **************
- Remote Control: ON
- CI-V Port: 50001
```

### Étape 2: Configuration PC

```bash
# Configuration automatique
cd backend
python configure_network.py

# Ou manuel (Windows)
netsh interface ip set address "Ethernet" static ************* *************
```

### Étape 3: Test

```bash
cd backend
python quick_network_test.py
```

## 📋 Utilisation

1. **Connecter l'IC-R8600** via RJ45 (priorité) ou USB (fallback)
2. **Lancer le backend** FastAPI
3. **Ouvrir l'interface** React
4. **Configurer** la fréquence et modulation
5. **Contrôler** le récepteur en temps réel

### Démarrage Automatique

```bash
cd backend
python main.py
# L'application détecte automatiquement la meilleure connexion
```

## 🔧 Outils de Diagnostic

### Test Rapide

```bash
cd backend
python quick_network_test.py
# Test complet en 30 secondes
```

### Scanner Réseau Avancé

```bash
cd backend
python advanced_network_scanner.py
# Découverte et analyse complète
```

### Tests Protocole Complets

```bash
cd backend
python test_network_protocol.py
# Suite de tests détaillée
```

### Découverte Automatique

```bash
cd backend
python scan_network.py
# Trouve l'IC-R8600 en DHCP
```

## 📚 Documentation

- **[Guide Configuration Réseau](NETWORK_SETUP_GUIDE.md)** - Configuration détaillée
- **[Documentation Technique](TECHNICAL.md)** - Spécifications CI-V
- **[Exemples](EXAMPLES.md)** - Exemples d'utilisation

## 🆘 Dépannage

### Problèmes Courants

#### Pas de connexion réseau

```bash
# Vérifier la connectivité
ping **************

# Tester les ports
python advanced_network_scanner.py
```

#### Connexion instable

```bash
# Basculer vers USB
curl -X POST http://localhost:8000/api/connection/switch

# Vérifier le statut
curl http://localhost:8000/api/connection/status
```

#### Performance dégradée

- Utilisez une connexion directe PC ↔ IC-R8600
- Vérifiez la qualité du câble RJ45
- Activez le mode UDP (plus rapide que TCP)

## 🎯 Avantages du Contrôle Réseau

| Critère          | Réseau RJ45 | USB Série      |
| ---------------- | ----------- | -------------- |
| **Latence**      | 5-15ms      | 20-50ms        |
| **Stabilité**    | Excellente  | Variable       |
| **Distance**     | 100m        | 5m             |
| **Installation** | Plug & Play | Drivers requis |
| **Débit**        | Élevé       | Limité         |
| **Fiabilité**    | Très haute  | Moyenne        |

---

**🎉 Profitez du contrôle réseau haute performance de votre IC-R8600 !**
