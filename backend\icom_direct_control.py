#!/usr/bin/env python3
"""
Contrôle direct ICOM IC-R8600 basé sur les spécifications techniques
Implémentation des protocoles réseau ICOM documentés
"""

import socket
import time
import struct
import logging
from typing import Optional, List

class ICOMDirectControl:
    """Contrôle direct ICOM IC-R8600 via spécifications techniques"""
    
    def __init__(self, host="**************"):
        self.host = host
        self.logger = logging.getLogger(__name__)
        
        # Ports ICOM officiels
        self.control_port = 50001
        self.serial_port = 50002
        self.audio_port = 50003
        
        # Sockets
        self.control_socket = None
        self.serial_socket = None
        
        # État
        self.connected = False

    def connect(self) -> bool:
        """Connexion directe selon spécifications ICOM"""
        try:
            self.logger.info(f"Connexion directe ICOM vers {self.host}")
            
            # Test 1: Connexion UDP simple
            if self._test_udp_connection():
                self.logger.info("Connexion UDP établie")
                self.connected = True
                return True
            
            # Test 2: Connexion TCP si UDP échoue
            if self._test_tcp_connection():
                self.logger.info("Connexion TCP établie")
                self.connected = True
                return True
            
            self.logger.error("Échec de toutes les méthodes de connexion")
            return False
            
        except Exception as e:
            self.logger.error(f"Erreur connexion directe: {e}")
            return False

    def _test_udp_connection(self) -> bool:
        """Test connexion UDP"""
        try:
            # Socket UDP pour contrôle
            self.control_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.control_socket.settimeout(5.0)
            
            # Socket UDP pour série
            self.serial_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.serial_socket.settimeout(5.0)
            
            # Test de connectivité avec ping UDP
            test_data = b'\x00\x01\x02\x03'
            self.control_socket.sendto(test_data, (self.host, self.control_port))
            
            try:
                response, addr = self.control_socket.recvfrom(1024)
                self.logger.info(f"Réponse UDP reçue: {response.hex().upper()}")
                return True
            except socket.timeout:
                self.logger.info("Pas de réponse UDP - continuer quand même")
                return True  # Certains équipements ne répondent pas au ping
                
        except Exception as e:
            self.logger.error(f"Erreur test UDP: {e}")
            return False

    def _test_tcp_connection(self) -> bool:
        """Test connexion TCP"""
        try:
            # Test port Telnet
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5.0)
            result = sock.connect_ex((self.host, 23))
            sock.close()
            
            if result == 0:
                self.logger.info("Port Telnet accessible")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Erreur test TCP: {e}")
            return False

    def send_ci_v_direct(self, command: List[int]) -> bool:
        """Envoie une commande CI-V directe"""
        if not self.connected:
            self.logger.error("Non connecté")
            return False
        
        try:
            # Construire la commande CI-V complète
            ci_v_command = bytes([0xFE, 0xFE, 0x94, 0xE0] + command + [0xFD])
            
            self.logger.info(f"Envoi CI-V direct: {ci_v_command.hex().upper()}")
            
            # Méthode 1: UDP sur port série
            if self.serial_socket:
                self.serial_socket.sendto(ci_v_command, (self.host, self.serial_port))
                self.logger.info("Commande envoyée via UDP série")
            
            # Méthode 2: UDP sur port contrôle
            if self.control_socket:
                self.control_socket.sendto(ci_v_command, (self.host, self.control_port))
                self.logger.info("Commande envoyée via UDP contrôle")
            
            # Méthode 3: TCP Telnet
            self._send_via_telnet(ci_v_command)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur envoi CI-V direct: {e}")
            return False

    def _send_via_telnet(self, command: bytes):
        """Envoie via Telnet"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3.0)
            sock.connect((self.host, 23))
            
            sock.sendall(command)
            self.logger.info("Commande envoyée via Telnet")
            
            # Lire la réponse
            try:
                response = sock.recv(1024)
                if response:
                    self.logger.info(f"Réponse Telnet: {response.hex().upper()}")
            except socket.timeout:
                pass
            
            sock.close()
            
        except Exception as e:
            self.logger.debug(f"Telnet non disponible: {e}")

    def power_on(self) -> bool:
        """Allume l'IC-R8600"""
        return self.send_ci_v_direct([0x18, 0x01])

    def power_off(self) -> bool:
        """Éteint l'IC-R8600"""
        return self.send_ci_v_direct([0x18, 0x00])

    def set_frequency(self, freq_hz: int) -> bool:
        """Définit la fréquence"""
        try:
            # Conversion en BCD
            freq_str = f"{freq_hz:010d}"
            bcd_bytes = []
            for i in range(4, -1, -1):
                digit1 = int(freq_str[i*2])
                digit2 = int(freq_str[i*2 + 1])
                bcd_bytes.append((digit1 << 4) | digit2)

            return self.send_ci_v_direct([0x05] + bcd_bytes)
            
        except Exception as e:
            self.logger.error(f"Erreur set_frequency: {e}")
            return False

    def set_mode(self, mode: str) -> bool:
        """Définit le mode"""
        modes = {'LSB': 0x00, 'USB': 0x01, 'AM': 0x02, 'CW': 0x03, 'FM': 0x05, 'WFM': 0x06}
        
        if mode not in modes:
            return False
            
        return self.send_ci_v_direct([0x06, modes[mode]])

    def disconnect(self):
        """Déconnexion"""
        self.logger.info("Déconnexion contrôle direct")
        
        for sock in [self.control_socket, self.serial_socket]:
            if sock:
                try:
                    sock.close()
                except:
                    pass
        
        self.control_socket = None
        self.serial_socket = None
        self.connected = False

def test_direct_control():
    """Test du contrôle direct"""
    print("=" * 60)
    print("TEST CONTRÔLE DIRECT ICOM IC-R8600")
    print("=" * 60)
    print("MÉTHODE: Envoi direct des commandes CI-V")
    print("PORTS: UDP 50001, 50002 + TCP 23")
    print("=" * 60)
    
    # Configuration logging
    logging.basicConfig(level=logging.INFO)
    
    controller = ICOMDirectControl("**************")
    
    try:
        # Connexion
        print("\n1. Test de connexion...")
        if controller.connect():
            print("✅ Connexion établie")
        else:
            print("❌ Échec de connexion")
            return
        
        # Test Power OFF
        print("\n2. Test Power OFF...")
        print("   OBSERVEZ L'IC-R8600 !")
        if controller.power_off():
            print("✅ Commande Power OFF envoyée")
        else:
            print("❌ Échec Power OFF")
        
        time.sleep(5)
        
        # Test Power ON
        print("\n3. Test Power ON...")
        print("   OBSERVEZ L'IC-R8600 !")
        if controller.power_on():
            print("✅ Commande Power ON envoyée")
        else:
            print("❌ Échec Power ON")
        
        time.sleep(5)
        
        # Test fréquence
        print("\n4. Test fréquence 145.500 MHz...")
        print("   OBSERVEZ L'AFFICHAGE !")
        if controller.set_frequency(145500000):
            print("✅ Commande fréquence envoyée")
        else:
            print("❌ Échec fréquence")
        
        time.sleep(3)
        
        # Test mode
        print("\n5. Test mode FM...")
        print("   OBSERVEZ L'AFFICHAGE !")
        if controller.set_mode("FM"):
            print("✅ Commande mode envoyée")
        else:
            print("❌ Échec mode")
        
        time.sleep(3)
        
        controller.disconnect()
        print("\n✅ Test terminé")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
    finally:
        controller.disconnect()

if __name__ == "__main__":
    print("CONTRÔLE DIRECT ICOM IC-R8600")
    print("Observez attentivement l'IC-R8600 pendant le test !")
    
    test_direct_control()
    
    print("\n" + "=" * 60)
    print("RÉSULTATS:")
    print("- Si effet sur IC-R8600 → Méthode directe fonctionne !")
    print("- Si pas d'effet → Problème configuration ou protocole")
    print("- Vérifiez les logs pour les détails techniques")
