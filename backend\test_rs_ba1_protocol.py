#!/usr/bin/env python3
"""
Test du protocole réseau ICOM RS-BA1 pour IC-R8600
Basé sur le reverse engineering du protocole propriétaire
"""

import time
import logging
from icom_rs_protocol import ICOMRSProtocol, ICOMRSConfig

# Configuration du logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_rs_ba1_protocol():
    """Test du protocole réseau ICOM RS-BA1"""
    print("=" * 60)
    print("TEST PROTOCOLE RÉSEAU ICOM RS-BA1 POUR IC-R8600")
    print("=" * 60)
    print("PROTOCOLE BASÉ SUR LE REVERSE ENGINEERING DE RS-BA1")
    print("Assurez-vous que:")
    print("1. L'IC-R8600 est allumé")
    print("2. Network Control est activé (MENU → SET → Network → Network Control: ON)")
    print("3. L'IC-R8600 a été redémarré après activation")
    print("4. L'IP ************** est correcte")
    print("5. Le bouton [LOCAL] a été pressé pour activer le mode REMOTE")
    print("=" * 60)
    
    # Configuration
    config = ICOMRSConfig(
        host="**************",
        control_port=50001,
        serial_port=50002,
        audio_port=50003,
        radio_model="IC-R8600"
    )
    
    # Création du handler
    icom = ICOMRSProtocol(config)
    
    try:
        # Connexion
        print("\n1. Test de connexion au protocole RS-BA1...")
        if icom.connect():
            print("✅ Connexion RS-BA1 réussie !")
            print(f"   Session ID: {icom.session_id.hex().upper() if icom.session_id else 'N/A'}")
            print(f"   Radio ID: {icom.radio_id.hex().upper() if icom.radio_id else 'N/A'}")
        else:
            print("❌ Échec de connexion RS-BA1")
            return
        
        # Test Power OFF
        print("\n2. Test Power OFF...")
        print("   OBSERVEZ L'IC-R8600 - IL DOIT S'ÉTEINDRE !")
        if icom.power_off():
            print("✅ Commande Power OFF envoyée via RS-BA1")
        else:
            print("❌ Échec commande Power OFF")
        
        # Attente pour observation
        print("   Attente 8 secondes pour observer l'effet...")
        time.sleep(8)
        
        # Test Power ON
        print("\n3. Test Power ON...")
        print("   OBSERVEZ L'IC-R8600 - IL DOIT S'ALLUMER !")
        if icom.power_on():
            print("✅ Commande Power ON envoyée via RS-BA1")
        else:
            print("❌ Échec commande Power ON")
        
        # Attente pour observation
        print("   Attente 8 secondes pour observer l'effet...")
        time.sleep(8)
        
        # Test fréquence
        print("\n4. Test changement fréquence à 145.500 MHz...")
        print("   OBSERVEZ L'AFFICHAGE DE FRÉQUENCE SUR L'IC-R8600 !")
        if icom.set_frequency(145500000):
            print("✅ Commande fréquence envoyée via RS-BA1")
        else:
            print("❌ Échec commande fréquence")
        
        # Attente pour observation
        print("   Attente 5 secondes pour observer le changement...")
        time.sleep(5)
        
        # Test mode
        print("\n5. Test changement mode à FM...")
        print("   OBSERVEZ L'AFFICHAGE DE MODE SUR L'IC-R8600 !")
        if icom.set_mode("FM"):
            print("✅ Commande mode envoyée via RS-BA1")
        else:
            print("❌ Échec commande mode")
        
        # Attente pour observation
        print("   Attente 5 secondes pour observer le changement...")
        time.sleep(5)
        
        # Test lecture de fréquence
        print("\n6. Test lecture de fréquence...")
        freq = icom.get_frequency()
        print(f"   Fréquence lue: {freq:.6f} MHz")
        
        # Test lecture de mode
        print("\n7. Test lecture de mode...")
        mode = icom.get_mode()
        print(f"   Mode lu: {mode}")
        
        # Affichage du statut
        print("\n8. Statut de la connexion:")
        status = icom.get_status()
        for key, value in status.items():
            print(f"   {key}: {value}")
        
        # Déconnexion
        print("\n9. Déconnexion...")
        icom.disconnect()
        print("✅ Déconnexion RS-BA1 effectuée")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Nettoyage
        if icom.connected:
            icom.disconnect()

def test_multiple_commands():
    """Test de commandes multiples pour vérifier la stabilité"""
    print("\n" + "=" * 60)
    print("TEST COMMANDES MULTIPLES")
    print("=" * 60)
    
    config = ICOMRSConfig(host="**************")
    icom = ICOMRSProtocol(config)
    
    try:
        if not icom.connect():
            print("❌ Échec de connexion pour test multiple")
            return
        
        print("✅ Connexion établie pour test multiple")
        
        # Test de plusieurs fréquences
        frequencies = [145500000, 146000000, 144000000, 145750000]
        
        for i, freq in enumerate(frequencies):
            print(f"\nTest {i+1}/4: Fréquence {freq/1000000:.3f} MHz")
            print("   OBSERVEZ LE CHANGEMENT SUR L'IC-R8600 !")
            
            if icom.set_frequency(freq):
                print("   ✅ Commande envoyée")
            else:
                print("   ❌ Échec")
            
            time.sleep(3)
        
        # Test de plusieurs modes
        modes = ["FM", "AM", "USB", "LSB"]
        
        for i, mode in enumerate(modes):
            print(f"\nTest mode {i+1}/4: {mode}")
            print("   OBSERVEZ LE CHANGEMENT SUR L'IC-R8600 !")
            
            if icom.set_mode(mode):
                print("   ✅ Commande envoyée")
            else:
                print("   ❌ Échec")
            
            time.sleep(2)
        
        icom.disconnect()
        print("\n✅ Test multiple terminé")
        
    except Exception as e:
        print(f"❌ Erreur test multiple: {e}")
    finally:
        if icom.connected:
            icom.disconnect()

def test_protocol_analysis():
    """Analyse du protocole pour debugging"""
    print("\n" + "=" * 60)
    print("ANALYSE DU PROTOCOLE RS-BA1")
    print("=" * 60)
    
    config = ICOMRSConfig(host="**************")
    icom = ICOMRSProtocol(config)
    
    # Activer le logging debug
    logging.getLogger('icom_rs_protocol').setLevel(logging.DEBUG)
    
    try:
        print("Connexion avec logging debug activé...")
        if icom.connect():
            print("✅ Connexion réussie - Vérifiez les logs pour l'analyse des paquets")
            
            # Test d'une commande simple avec analyse
            print("\nTest commande avec analyse des paquets...")
            icom.power_on()
            
            time.sleep(2)
            icom.disconnect()
        else:
            print("❌ Échec de connexion pour analyse")
            
    except Exception as e:
        print(f"❌ Erreur analyse: {e}")
    finally:
        if icom.connected:
            icom.disconnect()

if __name__ == "__main__":
    print("DIAGNOSTIC PROTOCOLE RS-BA1 POUR IC-R8600")
    print("Observez attentivement l'IC-R8600 pendant les tests !")
    print("Ce protocole est basé sur le reverse engineering de RS-BA1")
    
    # Test principal
    test_rs_ba1_protocol()
    
    # Test multiple
    choice = input("\nVoulez-vous tester des commandes multiples ? (o/n): ")
    if choice.lower() == 'o':
        test_multiple_commands()
    
    # Analyse du protocole
    choice = input("\nVoulez-vous analyser le protocole en détail ? (o/n): ")
    if choice.lower() == 'o':
        test_protocol_analysis()
    
    print("\n" + "=" * 60)
    print("DIAGNOSTIC RS-BA1 TERMINÉ")
    print("=" * 60)
    print("RÉSULTATS ATTENDUS :")
    print("- Si les commandes ont un effet sur l'IC-R8600 → SUCCÈS !")
    print("- Si pas d'effet mais connexion OK → Protocole à affiner")
    print("- Si échec de connexion → Problème configuration IC-R8600")
    print("- Vérifiez les logs DEBUG pour l'analyse des paquets")
