#!/usr/bin/env python3
"""
Test des commandes IC-R8600 via VirtualHere
Interface simple pour commander le récepteur
"""

import requests
import time

BASE_URL = "http://localhost:8000"

def test_status():
    """Test du statut"""
    print("🔍 TEST STATUT")
    print("=" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/api/status")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Connecté: {data['connected']}")
            print(f"✅ Port: {data['port']}")
            print(f"✅ Fréquence: {data['frequency'] / 1000000:.3f} MHz")
            print(f"✅ Mode: {data['mode']}")
            print(f"✅ VirtualHere: {data['virtualhere']}")
            return True
        else:
            print(f"❌ Erreur: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_communication():
    """Test de communication"""
    print("\n🔍 TEST COMMUNICATION")
    print("=" * 40)
    
    try:
        response = requests.get(f"{BASE_URL}/api/test_command")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Succès: {data['success']}")
            print(f"✅ Message: {data['message']}")
            print(f"✅ Commande: {data['command']}")
            return True
        else:
            print(f"❌ Erreur: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def set_frequency(freq_mhz):
    """Définir une fréquence"""
    print(f"\n📡 DÉFINITION FRÉQUENCE: {freq_mhz} MHz")
    print("=" * 40)
    print("⚠️  OBSERVEZ L'AFFICHAGE DE L'IC-R8600 !")
    
    try:
        data = {"frequency_mhz": freq_mhz}
        response = requests.post(f"{BASE_URL}/api/set_frequency", json=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Succès: {result['success']}")
            print(f"✅ Fréquence: {result['frequency_mhz']} MHz")
            print(f"✅ Fréquence Hz: {result['frequency_hz']} Hz")
            
            # Vérifier sur l'IC-R8600
            input(f"La fréquence {freq_mhz} MHz s'affiche-t-elle sur l'IC-R8600 ? (Appuyez sur Entrée)")
            return True
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"❌ Détail: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def set_mode(mode):
    """Définir un mode"""
    print(f"\n📻 DÉFINITION MODE: {mode}")
    print("=" * 40)
    print("⚠️  OBSERVEZ L'AFFICHAGE DE L'IC-R8600 !")
    
    try:
        data = {"mode": mode}
        response = requests.post(f"{BASE_URL}/api/set_mode", json=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Succès: {result['success']}")
            print(f"✅ Mode: {result['mode']}")
            
            # Vérifier sur l'IC-R8600
            input(f"Le mode {mode} s'affiche-t-il sur l'IC-R8600 ? (Appuyez sur Entrée)")
            return True
        else:
            print(f"❌ Erreur: {response.status_code}")
            print(f"❌ Détail: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Test complet des commandes"""
    print("🎯 TEST COMMANDES IC-R8600 VIRTUALHERE")
    print("=" * 60)
    print("Application: http://localhost:8000")
    print("VirtualHere: COM6 (CP2102 USB to UART Bridge)")
    print("=" * 60)
    
    # Test 1: Statut
    if not test_status():
        print("❌ Échec test statut - Arrêt")
        return
    
    # Test 2: Communication
    if not test_communication():
        print("❌ Échec test communication - Arrêt")
        return
    
    # Test 3: Fréquences
    frequencies = [145.500, 88.500, 162.550, 446.000]
    
    for freq in frequencies:
        if not set_frequency(freq):
            print(f"❌ Échec fréquence {freq} MHz")
            break
        time.sleep(1)
    
    # Test 4: Modes
    modes = ["FM", "AM", "USB", "LSB"]
    
    for mode in modes:
        if not set_mode(mode):
            print(f"❌ Échec mode {mode}")
            break
        time.sleep(1)
    
    # Test final
    print("\n" + "=" * 60)
    print("🎉 TESTS TERMINÉS")
    print("=" * 60)
    print("L'IC-R8600 est maintenant contrôlable via VirtualHere !")
    print("Interface web: http://localhost:8000")
    print("API docs: http://localhost:8000/docs")

if __name__ == "__main__":
    main()
